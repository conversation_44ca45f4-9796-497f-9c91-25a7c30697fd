import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M14.5 11h-1a.5.5 0 0 0 0 1h1a.5.5 0 0 0 0-1zm-10-8A1.5 1.5 0 0 0 3 4.5v10A2.5 2.5 0 0 0 5.5 17h10a1.5 1.5 0 0 0 1.5-1.5v-9a1.5 1.5 0 0 0-1-1.415V4.5A1.5 1.5 0 0 0 14.5 3h-10zM4 14.5V5.915c.156.055.325.085.5.085h11a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-10A1.5 1.5 0 0 1 4 14.5zM4.5 4h10a.5.5 0 0 1 .5.5V5H4.5a.5.5 0 0 1 0-1z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wallet20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
