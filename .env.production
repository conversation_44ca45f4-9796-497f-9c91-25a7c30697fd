# ConnectAI 生产环境复刻配置文件
# 请根据实际情况修改以下配置

# ==================== 基础配置 ====================
COMPOSE_PROJECT_NAME=connectai-production-replica
COMPOSE_FILE=production-replica-docker-compose.yml

# ==================== 数据库配置 ====================
# MySQL 配置
MYSQL_ROOT_PASSWORD=connectai2023
MYSQL_DATABASE_MANAGER=connectai-manager
MYSQL_DATABASE_PROXY=connectai-proxy
MYSQL_DATABASE_SERVER=connectai-server
MYSQL_DATABASE_PROXYALL=connectai-proxyall

# Redis 配置
REDIS_PASSWORD=

# ==================== AI API 配置 ====================
# OpenAI 配置 (如果您有OpenAI API密钥，请替换下面的值)
OPENAI_API_KEY=sk-test-key-for-local-development
OPENAI_API_BASE=https://api.openai.com
OPENAI_API_VERSION=2023-03-15-preview

# Claude 配置
CLAUDE_API_KEY=your_claude_api_key_here
CLAUDE_API_BASE=https://api.anthropic.com

# 星火配置
XINGHUO_APP_ID=your_xinghuo_app_id
XINGHUO_API_SECRET=your_xinghuo_api_secret
XINGHUO_API_KEY=your_xinghuo_api_key

# 文心一言配置
WENXIN_API_KEY=your_wenxin_api_key
WENXIN_SECRET_KEY=your_wenxin_secret_key

# 智谱AI配置
ZHIPUAI_API_KEY=your_zhipuai_api_key

# 通义千问配置
QWEN_API_KEY=your_qwen_api_key

# 阿里云配置
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_access_key_secret

# 腾讯云混元配置
HUNYUAN_SECRET_ID=your_hunyuan_secret_id
HUNYUAN_SECRET_KEY=your_hunyuan_secret_key

# 百川配置
BAICHUAN_API_KEY=your_baichuan_api_key

# 商汤配置
SENSENOVA_API_KEY=your_sensenova_api_key

# MiniMax配置
MINIMAX_API_KEY=your_minimax_api_key
MINIMAX_GROUP_ID=your_minimax_group_id

# Gemini 配置
GEMINI_API_KEY=your_gemini_api_key

# ElevenLabs 配置
ELEVENLABS_API_KEY=your_elevenlabs_api_key

# Stability AI 配置
STABILITY_API_KEY=your_stability_api_key

# ==================== 企业应用配置 ====================
# 飞书配置
FEISHU_APP_ID=your_feishu_app_id
FEISHU_APP_SECRET=your_feishu_app_secret
FEISHU_VERIFICATION_TOKEN=your_feishu_verification_token
FEISHU_ENCRYPT_KEY=your_feishu_encrypt_key

# 钉钉配置
DINGDING_APP_KEY=your_dingding_app_key
DINGDING_APP_SECRET=your_dingding_app_secret
DINGDING_AGENT_ID=your_dingding_agent_id

# 企业微信配置
WEWORK_CORP_ID=your_wework_corp_id
WEWORK_CORP_SECRET=your_wework_corp_secret
WEWORK_AGENT_ID=your_wework_agent_id

# ==================== 系统配置 ====================
# 系统域名配置
SYSTEM_DOMAIN=http://localhost:8081
SYSTEM_LOGIN_URL=http://localhost:8081/login
SYSTEM_API_URL=http://manager:3000/api/code2session

# 文件上传配置
UPLOAD_PATH=/data/files
MAX_CONTENT_LENGTH=104867600

# Elasticsearch 配置
ES_HOST=elasticsearch
ES_PORT=9200
ES_JAVA_OPTS=-Xms512m -Xmx512m

# RabbitMQ 配置
RABBITMQ_DEFAULT_USER=rabbitmq
RABBITMQ_DEFAULT_PASS=rabbitmq
RABBITMQ_DEFAULT_VHOST=/
RABBITMQ_ERLANG_COOKIE=SWQOKODSQALRPCLNMEQG

# ==================== 代理配置 ====================
# 代理服务器配置 (如果需要)
HTTP_PROXY=
HTTPS_PROXY=
NO_PROXY=localhost,127.0.0.1

# ==================== 日志配置 ====================
LOG_LEVEL=INFO
LOG_FORMAT=json

# ==================== 安全配置 ====================
# JWT 密钥
JWT_SECRET_KEY=your_jwt_secret_key_here

# 加密密钥
ENCRYPT_KEY=your_encrypt_key_here

# ==================== 监控配置 ====================
# 是否启用监控
ENABLE_MONITORING=true

# 监控端口
MONITORING_PORT=9090

# ==================== 备份配置 ====================
# 备份目录
BACKUP_DIR=/opt/connectai/backups

# 备份保留天数
BACKUP_RETENTION_DAYS=30

# ==================== 扩展配置 ====================
# 是否启用调试模式
DEBUG=false

# 时区配置
TZ=Asia/Shanghai

# 语言配置
LANG=zh_CN.UTF-8

# ==================== 自定义配置 ====================
# 在这里添加您的自定义环境变量
