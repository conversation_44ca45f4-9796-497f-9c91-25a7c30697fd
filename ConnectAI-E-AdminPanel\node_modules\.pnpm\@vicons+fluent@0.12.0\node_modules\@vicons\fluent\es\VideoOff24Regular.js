import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.28 2.22a.75.75 0 1 0-1.06 1.06l1.567 1.567A3.25 3.25 0 0 0 2 7.75v8.5a3.25 3.25 0 0 0 3.25 3.25h8.5a3.25 3.25 0 0 0 2.903-1.786l4.066 4.067a.75.75 0 0 0 1.061-1.061L3.28 2.22zm12.196 14.317A1.75 1.75 0 0 1 13.75 18h-8.5a1.75 1.75 0 0 1-1.75-1.75v-8.5c0-.869.633-1.59 1.463-1.727l10.514 10.514zm.024-4.219V7.75A1.75 1.75 0 0 0 13.75 6H9.182l-1.5-1.5h6.068A3.25 3.25 0 0 1 17 7.75v.173l3.864-2.318A.75.75 0 0 1 22 6.248V17.75c0 .301-.17.543-.403.665L20.5 17.318V7.573L17 9.674v4.144l-1.5-1.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoOff24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
