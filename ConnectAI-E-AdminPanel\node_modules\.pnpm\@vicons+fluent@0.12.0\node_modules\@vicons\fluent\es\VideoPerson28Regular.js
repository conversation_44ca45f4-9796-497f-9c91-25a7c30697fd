import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M14 15a3.5 3.5 0 1 0 0-7a3.5 3.5 0 0 0 0 7zm0-1.5a2 2 0 1 1 0-4a2 2 0 0 1 0 4zM2.004 7a3 3 0 0 1 3-3h17.997a3 3 0 0 1 3 3v14a3 3 0 0 1-3 3H5.004a3 3 0 0 1-3-3V7zm3-1.5a1.5 1.5 0 0 0-1.5 1.5v14a1.5 1.5 0 0 0 1.5 1.5H8.25v-4a2 2 0 0 1 2-2h7.5a2 2 0 0 1 2 2v4H23a1.5 1.5 0 0 0 1.5-1.5V7A1.5 1.5 0 0 0 23 5.5H5.005zm13.246 17v-4a.5.5 0 0 0-.5-.5h-7.5a.5.5 0 0 0-.5.5v4h8.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoPerson28Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
