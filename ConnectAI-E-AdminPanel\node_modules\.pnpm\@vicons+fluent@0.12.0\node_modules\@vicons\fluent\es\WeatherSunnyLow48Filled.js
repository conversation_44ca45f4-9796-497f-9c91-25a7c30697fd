import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M24 13.08c6.03 0 10.92 4.89 10.92 10.92c0 .683-.064 1.351-.184 2h8.014a1.25 1.25 0 1 1 0 2.5H5.25a1.25 1.25 0 0 1 0-2.5h8.013a10.983 10.983 0 0 1-.183-2c0-6.03 4.89-10.92 10.92-10.92zM11.505 9.804l.102.091l2.148 2.148a1.25 1.25 0 0 1-1.666 1.859l-.102-.091l-2.148-2.148a1.25 1.25 0 0 1 1.666-1.859zm26.639.091a1.25 1.25 0 0 1 .091 1.666l-.091.102l-2.148 2.148a1.25 1.25 0 0 1-1.859-1.666l.091-.102l2.148-2.148a1.25 1.25 0 0 1 1.768 0zM24 3.997c.648 0 1.18.492 1.244 1.122l.006.128v3.038a1.25 1.25 0 0 1-2.493.127l-.007-.127V5.247c0-.69.56-1.25 1.25-1.25zM21.25 38a1.25 1.25 0 1 0 0 2.5h5.5a1.25 1.25 0 0 0 0-2.5h-5.5zM12 33.25c0-.69.56-1.25 1.25-1.25h21.5a1.25 1.25 0 1 1 0 2.5h-21.5c-.69 0-1.25-.56-1.25-1.25z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSunnyLow48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
