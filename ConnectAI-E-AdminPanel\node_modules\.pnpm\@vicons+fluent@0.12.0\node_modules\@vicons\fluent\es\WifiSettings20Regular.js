import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.964 8.18A9.612 9.612 0 0 0 3.238 6.816c-.393.393-.775.86-1.121 1.36a.5.5 0 0 0 .821.57a9.018 9.018 0 0 1 1.007-1.223A8.612 8.612 0 0 1 17.141 8.75a.5.5 0 1 0 .823-.568zM7.183 11.287a4.031 4.031 0 0 1 3.85-1.056c-.319.26-.608.554-.862.878a3.025 3.025 0 0 0-2.281.885a3.051 3.051 0 0 0-.631.937a.5.5 0 1 1-.916-.4c.204-.467.486-.89.84-1.244zm7.412-2.497c.071.07.141.144.209.22a5.59 5.59 0 0 0-1.356.091a5.45 5.45 0 0 0-8.275 1.785a.5.5 0 0 1-.892-.451a6.45 6.45 0 0 1 10.315-1.645zm-2.528 2.653a2 2 0 0 1-1.431 2.478l-.461.118a4.706 4.706 0 0 0 .01 1.016l.35.083a2 2 0 0 1 1.456 2.519l-.127.423c.257.203.537.377.835.518l.325-.345a2 2 0 0 1 2.91.002l.337.358c.292-.135.568-.302.822-.498l-.157-.556a2 2 0 0 1 1.431-2.478l.46-.118a4.7 4.7 0 0 0-.01-1.017l-.348-.082a2 2 0 0 1-1.456-2.519l.126-.422a4.326 4.326 0 0 0-.835-.518l-.325.343a2 2 0 0 1-2.91-.001l-.337-.358a4.316 4.316 0 0 0-.821.497l.156.557zM14.5 15.5a1 1 0 1 1 0-2a1 1 0 0 1 0 2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WifiSettings20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
