import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.28 2.22a.75.75 0 1 0-1.06 1.06l.98.98A2.25 2.25 0 0 0 2 6.25v11.505a2.25 2.25 0 0 0 2.25 2.25h7.25a6.45 6.45 0 0 1-.423-1.501H8.5L8.5 15.75l.007-.057a.25.25 0 0 1 .243-.193h2.563a6.471 6.471 0 0 1 .709-1.5H8.75l-.144.006A1.75 1.75 0 0 0 7 15.75l-.001 2.754H4.25a.75.75 0 0 1-.75-.75V6.25a.75.75 0 0 1 .75-.75h.19L9 10.061A3 3 0 0 0 11.94 13l1.178 1.178a5.5 5.5 0 0 0 7.717 7.697a.75.75 0 0 0 .946-1.155L3.28 2.22zm16.456 18.577a.562.562 0 0 1-.778.084L17.5 19.777l-1.458 1.104c-.458.347-1.074-.12-.899-.68l.557-1.788l-1.458-1.104c-.458-.347-.223-1.101.343-1.101h.562l4.589 4.589zM10.553 7.372l1.157 1.156a1.5 1.5 0 0 1 1.762 1.762l1.157 1.157a3 3 0 0 0-4.075-4.075zm4.988 4.987l1.701 1.701a.577.577 0 0 1 .813.36l.206.659l1.128 1.129h1.026c.566 0 .8.754.343 1.1l-.152.116l2.035 2.035a5.5 5.5 0 0 0-7.1-7.1zM7.18 4l1.5 1.5h11.064a.75.75 0 0 1 .75.75v5.48a6.52 6.52 0 0 1 1.5 1.075V6.25A2.25 2.25 0 0 0 19.745 4H7.182z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoPersonStarOff24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
