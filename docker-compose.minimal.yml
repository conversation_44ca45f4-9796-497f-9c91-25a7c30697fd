version: '3.8'

networks:
  connectai-minimal:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
  files_data:
  portainer_data:

services:
  # ==================== 基础设施服务 (最小化) ====================

  # MySQL 数据库
  mysql:
    image: mysql:5.7
    container_name: connectai_mysql_minimal
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE_MANAGER}
      TZ: ${TZ}
    ports:
      - "53306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - connectai-minimal

  # Redis 缓存
  redis:
    image: redis:alpine
    container_name: connectai_redis_minimal
    restart: always
    ports:
      - "49642:6379"
    volumes:
      - redis_data:/data
    networks:
      - connectai-minimal

  # Nginx 代理
  nginx-proxy:
    image: jwilder/nginx-proxy:alpine
    container_name: connectai_nginx_minimal
    restart: always
    ports:
      - "8081:80"
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
    networks:
      - connectai-minimal

  # ==================== ConnectAI 核心服务 (模拟) ====================

  # 知识服务器 (简化版)
  know-server:
    image: nginx:alpine
    container_name: connectai_know_server_minimal
    restart: always
    ports:
      - "8086:80"
    environment:
      - VIRTUAL_HOST=know.local.connectai.com
    volumes:
      - files_data:/usr/share/nginx/html
    networks:
      - connectai-minimal

  # ConnectAI 管理服务 (简化版)
  manager:
    image: nginx:alpine
    container_name: connectai_manager_minimal
    restart: always
    ports:
      - "50344:80"
    environment:
      - VIRTUAL_HOST=manager.local.connectai.com
    depends_on:
      - mysql
      - redis
    networks:
      - connectai-minimal

  # ==================== AI 代理服务 (模拟) ====================

  # OpenAI 代理 (模拟)
  proxy-openai:
    image: nginx:alpine
    container_name: connectai_proxy_openai_minimal
    restart: always
    ports:
      - "50314:80"
    environment:
      - VIRTUAL_HOST=openai.proxy.local.connectai.com
    networks:
      - connectai-minimal

  # ==================== 管理工具 ====================

  # Portainer (Docker 管理界面)
  portainer:
    image: portainer/portainer-ce:latest
    container_name: connectai_portainer_minimal
    restart: always
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    networks:
      - connectai-minimal


