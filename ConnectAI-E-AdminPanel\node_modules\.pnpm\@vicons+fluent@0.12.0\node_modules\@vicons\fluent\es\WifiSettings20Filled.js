import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.845 8.117A9.468 9.468 0 0 0 3.34 6.773a9.842 9.842 0 0 0-1.105 1.338a.75.75 0 1 0 1.233.855c.292-.42.611-.811.932-1.132a7.968 7.968 0 0 1 12.21 1.135a.75.75 0 1 0 1.235-.852zM7.114 11.05a4.13 4.13 0 0 1 4.174-1.015c-.5.36-.936.802-1.29 1.306c-.662.01-1.32.266-1.824.77a2.648 2.648 0 0 0-.547.814a.75.75 0 1 1-1.374-.602c.209-.476.498-.91.86-1.273zm7.476-2.389c.113.113.222.231.327.355a5.58 5.58 0 0 0-1.97.206a4.944 4.944 0 0 0-7.322 1.76a.75.75 0 1 1-1.338-.677A6.443 6.443 0 0 1 14.59 8.66zm-2.523 2.781a2 2 0 0 1-1.431 2.478l-.461.118a4.706 4.706 0 0 0 .01 1.016l.35.083a2 2 0 0 1 1.456 2.519l-.127.423c.257.203.537.377.835.518l.325-.345a2 2 0 0 1 2.91.002l.337.358c.292-.135.568-.302.822-.498l-.157-.556a2 2 0 0 1 1.431-2.478l.46-.118a4.7 4.7 0 0 0-.01-1.017l-.348-.082a2 2 0 0 1-1.456-2.519l.126-.422a4.326 4.326 0 0 0-.835-.518l-.325.343a2 2 0 0 1-2.91-.001l-.337-.358a4.316 4.316 0 0 0-.821.497l.156.557zM14.5 15.5a1 1 0 1 1 0-2a1 1 0 0 1 0 2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WifiSettings20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
