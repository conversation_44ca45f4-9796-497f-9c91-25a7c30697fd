import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.28 2.22a.75.75 0 1 0-1.06 1.06l10.397 10.398c-.894 1.346-2.226 2.408-3.727 3.24c-1.77.98-3.72 1.61-5.32 2.004a.75.75 0 0 0-.468 1.105A11.995 11.995 0 0 0 13.48 26c3.177 0 6.064-1.234 8.21-3.248l3.03 3.029a.75.75 0 0 0 1.06-1.061L3.28 2.22zm17.35 19.47a10.463 10.463 0 0 1-7.15 2.81c-3.518 0-6.634-1.73-8.54-4.39c1.462-.417 3.122-1.019 4.677-1.88c1.543-.855 3.017-1.985 4.078-3.474l6.934 6.934zm-6.756-10.998l1.213 1.213c.953-2.94.728-5.963.275-8.237C20.263 4.555 23.98 8.844 23.98 14c0 1.95-.531 3.776-1.457 5.34l1.09 1.091A11.944 11.944 0 0 0 25.48 14c0-6.3-4.853-11.464-11.024-11.96a.75.75 0 0 0-.788.93c.514 2.05.905 4.92.206 7.722z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherMoonOff28Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
