import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M26 40.031a2 2 0 1 1 0 4a2 2 0 0 1 0-4zm-7-2a2 2 0 1 1 0 4a2 2 0 0 1 0-4zm14 0a2 2 0 1 1 0 4a2 2 0 0 1 0-4zM25.999 12.01c6.337 0 9.932 4.194 10.455 9.26h.16c4.078 0 7.384 3.297 7.384 7.365S40.692 36 36.614 36h-21.23C11.306 36 8 32.703 8 28.635s3.306-7.365 7.384-7.365h.16c.526-5.099 4.118-9.26 10.455-9.26zm0 2.495c-4.261 0-7.975 3.448-7.975 8.21c0 .755-.656 1.347-1.408 1.347h-1.42c-2.594 0-4.697 2.114-4.697 4.721c0 2.608 2.103 4.722 4.697 4.722h21.606c2.594 0 4.697-2.114 4.697-4.722c0-2.607-2.103-4.72-4.697-4.72h-1.42c-.752 0-1.408-.593-1.408-1.348c0-4.823-3.714-8.21-7.975-8.21zm-17.29 5.798a1.244 1.244 0 0 1-.558 1.57l-.115.055l-2.312.962a1.244 1.244 0 0 1-1.067-2.243l.115-.055l2.312-.962a1.244 1.244 0 0 1 1.625.673zm13-9.64l-.062.019c-.924.302-1.784.702-2.57 1.19a5.5 5.5 0 0 0-6.826 7.95a9.275 9.275 0 0 0-2.126 1.086a7.883 7.883 0 0 1 11.584-10.246zm-16.129.179l.127.046l2.32.989c.629.266.931.995.674 1.628c-.24.592-.883.89-1.48.711l-.126-.045l-2.319-.99a1.26 1.26 0 0 1-.675-1.628c.24-.591.883-.89 1.48-.711zm7.369-6.174l.055.116l.96 2.306a1.25 1.25 0 0 1-2.255 1.072l-.055-.115l-.96-2.306a1.25 1.25 0 0 1 2.255-1.073zm9.048-.56c.602.25.909.91.73 1.521l-.046.13l-.992 2.276a1.264 1.264 0 0 1-2.38-.837l.046-.13l.991-2.276a1.264 1.264 0 0 1 1.651-.684z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherHailDay48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
