import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12.955 11.84c.363.364.662.81.87 1.283a.75.75 0 0 1-1.375.602a2.728 2.728 0 0 0-.556-.824a2.63 2.63 0 0 0-3.72 0a2.648 2.648 0 0 0-.547.814a.75.75 0 1 1-1.374-.601c.209-.477.498-.91.86-1.273a4.13 4.13 0 0 1 5.842 0zm-2.034 2.042a1.242 1.242 0 1 1-1.757 1.757a1.242 1.242 0 0 1 1.757-1.757z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wifi320Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
