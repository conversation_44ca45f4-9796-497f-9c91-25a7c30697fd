import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M22.55 23.964l5.743 5.743a1 1 0 0 0 1.414-1.414l-26-26a1 1 0 0 0-1.414 1.414L4.536 5.95A4.5 4.5 0 0 0 2 10v12a4.5 4.5 0 0 0 4.5 4.5h12a4.5 4.5 0 0 0 4.05-2.536zm-1.58-1.58A2.5 2.5 0 0 1 18.5 24.5h-12A2.5 2.5 0 0 1 4 22V10a2.5 2.5 0 0 1 2.115-2.47l14.856 14.855zM21 10v7.465l6.14 6.14l.06.045c1.154.865 2.8.042 2.8-1.4V9.75c0-1.442-1.646-2.265-2.8-1.4L23 11.5V10a4.5 4.5 0 0 0-4.5-4.5H9.035l2 2H18.5A2.5 2.5 0 0 1 21 10zm2 4l5-3.75v11.5L23 18v-4z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoOff32Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
