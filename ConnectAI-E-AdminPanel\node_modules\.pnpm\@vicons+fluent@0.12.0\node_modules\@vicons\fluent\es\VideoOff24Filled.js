import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.28 2.22a.75.75 0 1 0-1.06 1.06l1.567 1.567A3.25 3.25 0 0 0 2 7.75v8.5a3.25 3.25 0 0 0 3.25 3.25h7.5a3.251 3.251 0 0 0 3.168-2.521l4.801 4.802a.75.75 0 0 0 1.061-1.061L3.28 2.22zM17 13.818l4.504 4.505a1 1 0 0 0 .496-.864V6.54a1 1 0 0 0-1.648-.762L17 8.628v5.19zM7.682 4.5L16 12.818V7.75a3.25 3.25 0 0 0-3.25-3.25H7.682z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoOff24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
