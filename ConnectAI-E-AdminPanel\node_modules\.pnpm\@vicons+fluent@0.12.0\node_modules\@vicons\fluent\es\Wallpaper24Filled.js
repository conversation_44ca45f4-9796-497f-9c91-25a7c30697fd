import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M14.762 13.127l.138.137l5.6 5.876v-5.391a.75.75 0 0 1 1.493-.102l.007.102v5.502a2.75 2.75 0 0 1-2.582 2.745l-.168.005h-5.5a.75.75 0 0 1-.102-1.493l.102-.007h5.284l-5.582-5.857a2 2 0 0 0-2.772-.12l-.124.12l-5.578 5.857h5.272l.102.007a.75.75 0 0 1 0 1.486l-.102.007h-5.5l-.168-.005a2.75 2.75 0 0 1-2.577-2.57L2 19.25V13.75l.007-.102a.75.75 0 0 1 1.486 0l.007.102v5.404l5.607-5.888a4 4 0 0 1 5.655-.138zM10.25 1.999a.75.75 0 0 1 .102 1.493l-.102.007h-5.5a1.25 1.25 0 0 0-1.244 1.122L3.5 4.75v5.502a.75.75 0 0 1-1.493.102L2 10.251V4.75a2.75 2.75 0 0 1 2.582-2.745l.168-.005h5.5zm9 0l.168.005a2.75 2.75 0 0 1 2.577 2.57L22 4.75v5.502l-.007.102a.75.75 0 0 1-1.486 0l-.007-.102V4.75l-.006-.128a1.25 1.25 0 0 0-1.116-1.116L19.25 3.5h-5.5l-.102-.007a.75.75 0 0 1 0-1.486l.102-.007h5.5zm-3.247 4a2 2 0 1 1 0 4a2 2 0 0 1 0-4z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wallpaper24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
