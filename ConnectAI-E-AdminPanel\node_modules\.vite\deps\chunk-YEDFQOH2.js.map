{"version": 3, "sources": ["../../.pnpm/ssr-window@4.0.2/node_modules/ssr-window/ssr-window.esm.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/shared/utils.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/shared/get-support.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/shared/get-device.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/shared/get-browser.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/modules/resize/resize.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/modules/observer/observer.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/events-emitter.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/update/updateSize.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/update/updateSlides.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/update/updateAutoHeight.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/update/updateSlidesOffset.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/update/updateSlidesProgress.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/update/updateProgress.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/update/updateSlidesClasses.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/shared/process-lazy-preloader.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/update/updateActiveIndex.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/update/updateClickedSlide.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/update/index.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/translate/getTranslate.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/translate/setTranslate.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/translate/minTranslate.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/translate/maxTranslate.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/translate/translateTo.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/translate/index.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/transition/setTransition.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/transition/transitionEmit.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/transition/transitionStart.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/transition/transitionEnd.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/transition/index.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/slide/slideTo.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/slide/slideToLoop.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/slide/slideNext.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/slide/slidePrev.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/slide/slideReset.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/slide/slideToClosest.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/slide/slideToClickedSlide.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/slide/index.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/loop/loopCreate.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/loop/loopFix.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/loop/loopDestroy.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/loop/index.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/grab-cursor/setGrabCursor.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/grab-cursor/unsetGrabCursor.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/grab-cursor/index.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/events/onTouchStart.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/events/onTouchMove.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/events/onTouchEnd.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/events/onResize.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/events/onClick.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/events/onScroll.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/events/onLoad.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/events/index.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/breakpoints/setBreakpoint.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/breakpoints/getBreakpoint.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/breakpoints/index.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/classes/addClasses.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/classes/removeClasses.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/classes/index.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/check-overflow/index.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/defaults.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/moduleExtendParams.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/core/core.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/virtual/virtual.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/keyboard/keyboard.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/mousewheel/mousewheel.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/shared/create-element-if-not-defined.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/navigation/navigation.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/shared/classes-to-selector.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/pagination/pagination.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/scrollbar/scrollbar.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/parallax/parallax.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/zoom/zoom.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/controller/controller.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/a11y/a11y.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/history/history.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/hash-navigation/hash-navigation.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/autoplay/autoplay.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/thumbs/thumbs.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/free-mode/free-mode.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/grid/grid.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/manipulation/methods/appendSlide.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/manipulation/methods/prependSlide.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/manipulation/methods/addSlide.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/manipulation/methods/removeSlide.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/manipulation/methods/removeAllSlides.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/manipulation/manipulation.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/shared/effect-init.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/shared/effect-target.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/shared/effect-virtual-transition-end.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/effect-fade/effect-fade.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/effect-cube/effect-cube.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/shared/create-shadow.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/effect-flip/effect-flip.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/effect-coverflow/effect-coverflow.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/effect-creative/effect-creative.js", "../../.pnpm/swiper@9.2.0/node_modules/swiper/modules/effect-cards/effect-cards.js"], "sourcesContent": ["/**\n * SSR Window 4.0.2\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2021, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: December 13, 2021\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n    return (obj !== null &&\n        typeof obj === 'object' &&\n        'constructor' in obj &&\n        obj.constructor === Object);\n}\nfunction extend(target = {}, src = {}) {\n    Object.keys(src).forEach((key) => {\n        if (typeof target[key] === 'undefined')\n            target[key] = src[key];\n        else if (isObject(src[key]) &&\n            isObject(target[key]) &&\n            Object.keys(src[key]).length > 0) {\n            extend(target[key], src[key]);\n        }\n    });\n}\n\nconst ssrDocument = {\n    body: {},\n    addEventListener() { },\n    removeEventListener() { },\n    activeElement: {\n        blur() { },\n        nodeName: '',\n    },\n    querySelector() {\n        return null;\n    },\n    querySelectorAll() {\n        return [];\n    },\n    getElementById() {\n        return null;\n    },\n    createEvent() {\n        return {\n            initEvent() { },\n        };\n    },\n    createElement() {\n        return {\n            children: [],\n            childNodes: [],\n            style: {},\n            setAttribute() { },\n            getElementsByTagName() {\n                return [];\n            },\n        };\n    },\n    createElementNS() {\n        return {};\n    },\n    importNode() {\n        return null;\n    },\n    location: {\n        hash: '',\n        host: '',\n        hostname: '',\n        href: '',\n        origin: '',\n        pathname: '',\n        protocol: '',\n        search: '',\n    },\n};\nfunction getDocument() {\n    const doc = typeof document !== 'undefined' ? document : {};\n    extend(doc, ssrDocument);\n    return doc;\n}\n\nconst ssrWindow = {\n    document: ssrDocument,\n    navigator: {\n        userAgent: '',\n    },\n    location: {\n        hash: '',\n        host: '',\n        hostname: '',\n        href: '',\n        origin: '',\n        pathname: '',\n        protocol: '',\n        search: '',\n    },\n    history: {\n        replaceState() { },\n        pushState() { },\n        go() { },\n        back() { },\n    },\n    CustomEvent: function CustomEvent() {\n        return this;\n    },\n    addEventListener() { },\n    removeEventListener() { },\n    getComputedStyle() {\n        return {\n            getPropertyValue() {\n                return '';\n            },\n        };\n    },\n    Image() { },\n    Date() { },\n    screen: {},\n    setTimeout() { },\n    clearTimeout() { },\n    matchMedia() {\n        return {};\n    },\n    requestAnimationFrame(callback) {\n        if (typeof setTimeout === 'undefined') {\n            callback();\n            return null;\n        }\n        return setTimeout(callback, 0);\n    },\n    cancelAnimationFrame(id) {\n        if (typeof setTimeout === 'undefined') {\n            return;\n        }\n        clearTimeout(id);\n    },\n};\nfunction getWindow() {\n    const win = typeof window !== 'undefined' ? window : {};\n    extend(win, ssrWindow);\n    return win;\n}\n\nexport { extend, getDocument, getWindow, ssrDocument, ssrWindow };\n", "import { getWindow, getDocument } from 'ssr-window';\nfunction deleteProps(obj) {\n  const object = obj;\n  Object.keys(object).forEach(key => {\n    try {\n      object[key] = null;\n    } catch (e) {\n      // no getter for object\n    }\n    try {\n      delete object[key];\n    } catch (e) {\n      // something got wrong\n    }\n  });\n}\nfunction nextTick(callback, delay = 0) {\n  return setTimeout(callback, delay);\n}\nfunction now() {\n  return Date.now();\n}\nfunction getComputedStyle(el) {\n  const window = getWindow();\n  let style;\n  if (window.getComputedStyle) {\n    style = window.getComputedStyle(el, null);\n  }\n  if (!style && el.currentStyle) {\n    style = el.currentStyle;\n  }\n  if (!style) {\n    style = el.style;\n  }\n  return style;\n}\nfunction getTranslate(el, axis = 'x') {\n  const window = getWindow();\n  let matrix;\n  let curTransform;\n  let transformMatrix;\n  const curStyle = getComputedStyle(el, null);\n  if (window.WebKitCSSMatrix) {\n    curTransform = curStyle.transform || curStyle.webkitTransform;\n    if (curTransform.split(',').length > 6) {\n      curTransform = curTransform.split(', ').map(a => a.replace(',', '.')).join(', ');\n    }\n    // Some old versions of Webkit choke when 'none' is passed; pass\n    // empty string instead in this case\n    transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n  } else {\n    transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n    matrix = transformMatrix.toString().split(',');\n  }\n  if (axis === 'x') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m41;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[12]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[4]);\n  }\n  if (axis === 'y') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m42;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[13]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[5]);\n  }\n  return curTransform || 0;\n}\nfunction isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object';\n}\nfunction isNode(node) {\n  // eslint-disable-next-line\n  if (typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined') {\n    return node instanceof HTMLElement;\n  }\n  return node && (node.nodeType === 1 || node.nodeType === 11);\n}\nfunction extend(...args) {\n  const to = Object(args[0]);\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  for (let i = 1; i < args.length; i += 1) {\n    const nextSource = args[i];\n    if (nextSource !== undefined && nextSource !== null && !isNode(nextSource)) {\n      const keysArray = Object.keys(Object(nextSource)).filter(key => noExtend.indexOf(key) < 0);\n      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n        const nextKey = keysArray[nextIndex];\n        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n        if (desc !== undefined && desc.enumerable) {\n          if (isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else if (!isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            to[nextKey] = {};\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else {\n            to[nextKey] = nextSource[nextKey];\n          }\n        }\n      }\n    }\n  }\n  return to;\n}\nfunction setCSSProperty(el, varName, varValue) {\n  el.style.setProperty(varName, varValue);\n}\nfunction animateCSSModeScroll({\n  swiper,\n  targetPosition,\n  side\n}) {\n  const window = getWindow();\n  const startPosition = -swiper.translate;\n  let startTime = null;\n  let time;\n  const duration = swiper.params.speed;\n  swiper.wrapperEl.style.scrollSnapType = 'none';\n  window.cancelAnimationFrame(swiper.cssModeFrameID);\n  const dir = targetPosition > startPosition ? 'next' : 'prev';\n  const isOutOfBound = (current, target) => {\n    return dir === 'next' && current >= target || dir === 'prev' && current <= target;\n  };\n  const animate = () => {\n    time = new Date().getTime();\n    if (startTime === null) {\n      startTime = time;\n    }\n    const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n    const easeProgress = 0.5 - Math.cos(progress * Math.PI) / 2;\n    let currentPosition = startPosition + easeProgress * (targetPosition - startPosition);\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      currentPosition = targetPosition;\n    }\n    swiper.wrapperEl.scrollTo({\n      [side]: currentPosition\n    });\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.scrollSnapType = '';\n      setTimeout(() => {\n        swiper.wrapperEl.style.overflow = '';\n        swiper.wrapperEl.scrollTo({\n          [side]: currentPosition\n        });\n      });\n      window.cancelAnimationFrame(swiper.cssModeFrameID);\n      return;\n    }\n    swiper.cssModeFrameID = window.requestAnimationFrame(animate);\n  };\n  animate();\n}\nfunction getSlideTransformEl(slideEl) {\n  return slideEl.querySelector('.swiper-slide-transform') || slideEl.shadowEl && slideEl.shadowEl.querySelector('.swiper-slide-transform') || slideEl;\n}\nfunction findElementsInElements(elements = [], selector = '') {\n  const found = [];\n  elements.forEach(el => {\n    found.push(...el.querySelectorAll(selector));\n  });\n  return found;\n}\nfunction elementChildren(element, selector = '') {\n  return [...element.children].filter(el => el.matches(selector));\n}\nfunction createElement(tag, classes = []) {\n  const el = document.createElement(tag);\n  el.classList.add(...(Array.isArray(classes) ? classes : [classes]));\n  return el;\n}\nfunction elementOffset(el) {\n  const window = getWindow();\n  const document = getDocument();\n  const box = el.getBoundingClientRect();\n  const body = document.body;\n  const clientTop = el.clientTop || body.clientTop || 0;\n  const clientLeft = el.clientLeft || body.clientLeft || 0;\n  const scrollTop = el === window ? window.scrollY : el.scrollTop;\n  const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n  return {\n    top: box.top + scrollTop - clientTop,\n    left: box.left + scrollLeft - clientLeft\n  };\n}\nfunction elementPrevAll(el, selector) {\n  const prevEls = [];\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n    if (selector) {\n      if (prev.matches(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n    el = prev;\n  }\n  return prevEls;\n}\nfunction elementNextAll(el, selector) {\n  const nextEls = [];\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n    if (selector) {\n      if (next.matches(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n    el = next;\n  }\n  return nextEls;\n}\nfunction elementStyle(el, prop) {\n  const window = getWindow();\n  return window.getComputedStyle(el, null).getPropertyValue(prop);\n}\nfunction elementIndex(el) {\n  let child = el;\n  let i;\n  if (child) {\n    i = 0;\n    // eslint-disable-next-line\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n    return i;\n  }\n  return undefined;\n}\nfunction elementParents(el, selector) {\n  const parents = []; // eslint-disable-line\n  let parent = el.parentElement; // eslint-disable-line\n  while (parent) {\n    if (selector) {\n      if (parent.matches(selector)) parents.push(parent);\n    } else {\n      parents.push(parent);\n    }\n    parent = parent.parentElement;\n  }\n  return parents;\n}\nfunction elementTransitionEnd(el, callback) {\n  function fireCallBack(e) {\n    if (e.target !== el) return;\n    callback.call(el, e);\n    el.removeEventListener('transitionend', fireCallBack);\n  }\n  if (callback) {\n    el.addEventListener('transitionend', fireCallBack);\n  }\n}\nfunction elementOuterSize(el, size, includeMargins) {\n  const window = getWindow();\n  if (includeMargins) {\n    return el[size === 'width' ? 'offsetWidth' : 'offsetHeight'] + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-right' : 'margin-top')) + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-left' : 'margin-bottom'));\n  }\n  return el.offsetWidth;\n}\nexport { animateCSSModeScroll, deleteProps, nextTick, now, getTranslate, isObject, extend, getComputedStyle, setCSSProperty, getSlideTransformEl,\n// dom\nfindElementsInElements, createElement, elementChildren, elementOffset, elementPrevAll, elementNextAll, elementStyle, elementIndex, elementParents, elementTransitionEnd, elementOuterSize };", "import { getWindow, getDocument } from 'ssr-window';\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\nexport { getSupport };", "import { getWindow } from 'ssr-window';\nimport { getSupport } from './get-support.js';\nlet deviceCached;\nfunction calcDevice({\n  userAgent\n} = {}) {\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice(overrides = {}) {\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\nexport { getDevice };", "import { getWindow } from 'ssr-window';\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  return {\n    isSafari: needPerspectiveFix || isSafari(),\n    needPerspectiveFix,\n    isWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent)\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\nexport { getBrowser };", "import { getWindow } from 'ssr-window';\nexport default function Resize({\n  swiper,\n  on,\n  emit\n}) {\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(({\n          contentBoxSize,\n          contentRect,\n          target\n        }) => {\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}", "import { getWindow } from 'ssr-window';\nimport { elementParents } from '../../../shared/utils.js';\nexport default function Observer({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  const observers = [];\n  const window = getWindow();\n  const attach = (target, options = {}) => {\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: typeof options.childList === 'undefined' ? true : options.childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.el);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.el, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}", "/* eslint-disable no-underscore-dangle */\n\nexport default {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler(...args) {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit(...args) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};", "import { elementStyle } from '../../shared/utils.js';\nexport default function updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}", "import { elementChildren, elementOuterSize, elementStyle, setCSSProperty } from '../../shared/utils.js';\nexport default function updateSlides() {\n  const swiper = this;\n  function getDirectionLabel(property) {\n    if (swiper.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slidesLength);\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slidesLength, getDirectionLabel);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + params.spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[getDirectionLabel('width')] = `${swiper.virtualSize + params.spaceBetween}px`;\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid, getDirectionLabel);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (params.spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = `${spaceBetween}px`;\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (params.spaceBetween ? params.spaceBetween : 0);\n    });\n    allSlidesSize -= params.spaceBetween;\n    const maxSnap = allSlidesSize - swiperSize;\n    snapGrid = snapGrid.map(snap => {\n      if (snap < 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (params.spaceBetween ? params.spaceBetween : 0);\n    });\n    allSlidesSize -= params.spaceBetween;\n    if (allSlidesSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}", "export default function updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.getSlideIndexByData(index);\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}", "export default function updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset;\n  }\n}", "export default function updateSlidesProgress(translate = this && this.translate || 0) {\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n\n  // Visible Slides\n  slides.forEach(slideEl => {\n    slideEl.classList.remove(params.slideVisibleClass);\n  });\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + params.spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + params.spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n      slides[i].classList.add(params.slideVisibleClass);\n    }\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}", "export default function updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}", "import { elementChildren, elementNextAll, elementPrevAll } from '../../shared/utils.js';\nexport default function updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, `.${params.slideClass}${selector}, swiper-slide${selector}`)[0];\n  };\n  slides.forEach(slideEl => {\n    slideEl.classList.remove(params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n  });\n  let activeSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    activeSlide = slides[activeIndex];\n  }\n  if (activeSlide) {\n    // Active classes\n    activeSlide.classList.add(params.slideActiveClass);\n\n    // Next Slide\n    let nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n    if (params.loop && !nextSlide) {\n      nextSlide = slides[0];\n    }\n    if (nextSlide) {\n      nextSlide.classList.add(params.slideNextClass);\n    }\n    // Prev Slide\n    let prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n    if (params.loop && !prevSlide === 0) {\n      prevSlide = slides[slides.length - 1];\n    }\n    if (prevSlide) {\n      prevSlide.classList.add(params.slidePrevClass);\n    }\n  }\n  swiper.emitSlidesClasses();\n}", "export const processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    const lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nexport const preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex !== activeIndex && realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(slideIndexLastInView - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && i > slideIndexLastInView) unlazy(swiper, i);\n    }\n  }\n};", "import { preload } from '../../shared/process-lazy-preloader.js';\nexport function getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nexport default function updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.realIndex = getVirtualRealIndex(activeIndex);\n    }\n    return;\n  }\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (swiper.slides[activeIndex]) {\n    realIndex = parseInt(swiper.slides[activeIndex].getAttribute('data-swiper-slide-index') || activeIndex, 10);\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    snapIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (previousRealIndex !== realIndex) {\n    swiper.emit('realIndexChange');\n  }\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    swiper.emit('slideChange');\n  }\n}", "export default function updateClickedSlide(e) {\n  const swiper = this;\n  const params = swiper.params;\n  const slide = e.closest(`.${params.slideClass}, swiper-slide`);\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}", "import updateSize from './updateSize.js';\nimport updateSlides from './updateSlides.js';\nimport updateAutoHeight from './updateAutoHeight.js';\nimport updateSlidesOffset from './updateSlidesOffset.js';\nimport updateSlidesProgress from './updateSlidesProgress.js';\nimport updateProgress from './updateProgress.js';\nimport updateSlidesClasses from './updateSlidesClasses.js';\nimport updateActiveIndex from './updateActiveIndex.js';\nimport updateClickedSlide from './updateClickedSlide.js';\nexport default {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide\n};", "import { getTranslate } from '../../shared/utils.js';\nexport default function getSwiperTranslate(axis = this.isHorizontal() ? 'x' : 'y') {\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}", "export default function setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}", "export default function minTranslate() {\n  return -this.snapGrid[0];\n}", "export default function maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}", "import { animateCSSModeScroll } from '../../shared/utils.js';\nexport default function translateTo(translate = 0, speed = this.params.speed, runCallbacks = true, translateBounds = true, internal) {\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}", "import getTranslate from './getTranslate.js';\nimport setTranslate from './setTranslate.js';\nimport minTranslate from './minTranslate.js';\nimport maxTranslate from './maxTranslate.js';\nimport translateTo from './translateTo.js';\nexport default {\n  getTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};", "export default function setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n  }\n  swiper.emit('setTransition', duration, byController);\n}", "export default function transitionEmit({\n  swiper,\n  runCallbacks,\n  direction,\n  step\n}) {\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(`transition${step}`);\n  if (runCallbacks && activeIndex !== previousIndex) {\n    if (dir === 'reset') {\n      swiper.emit(`slideResetTransition${step}`);\n      return;\n    }\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}", "import transitionEmit from './transitionEmit.js';\nexport default function transitionStart(runCallbacks = true, direction) {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}", "import transitionEmit from './transitionEmit.js';\nexport default function transitionEnd(runCallbacks = true, direction) {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}", "import setTransition from './setTransition.js';\nimport transitionStart from './transitionStart.js';\nimport transitionEnd from './transitionEnd.js';\nexport default {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};", "import { animateCSSModeScroll } from '../../shared/utils.js';\nexport default function slideTo(index = 0, speed = this.params.speed, runCallbacks = true, internal, initial) {\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition || !enabled && !internal && !initial) {\n    return false;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && translate < swiper.translate && translate < swiper.minTranslate()) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // Update Index\n  if (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}", "export default function slideToLoop(index = 0, speed = this.params.speed, runCallbacks = true, internal) {\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      newIndex = swiper.getSlideIndexByData(newIndex);\n    }\n  }\n  return swiper.slideTo(newIndex, speed, runCallbacks, internal);\n}", "/* eslint no-unused-vars: \"off\" */\nexport default function slideNext(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled) return swiper;\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}", "/* eslint no-unused-vars: \"off\" */\nexport default function slidePrev(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled) return swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && params.cssMode) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}", "/* eslint no-unused-vars: \"off\" */\nexport default function slideReset(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}", "/* eslint no-unused-vars: \"off\" */\nexport default function slideToClosest(speed = this.params.speed, runCallbacks = true, internal, threshold = 0.5) {\n  const swiper = this;\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}", "import { elementChildren, nextTick } from '../../shared/utils.js';\nexport default function slideToClickedSlide() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.clickedIndex;\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      if (slideToIndex < swiper.loopedSlides - slidesPerView / 2 || slideToIndex > swiper.slides.length - swiper.loopedSlides + slidesPerView / 2) {\n        swiper.loopFix();\n        slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n        nextTick(() => {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}", "import slideTo from './slideTo.js';\nimport slideToLoop from './slideToLoop.js';\nimport slideNext from './slideNext.js';\nimport slidePrev from './slidePrev.js';\nimport slideReset from './slideReset.js';\nimport slideToClosest from './slideToClosest.js';\nimport slideToClickedSlide from './slideToClickedSlide.js';\nexport default {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide\n};", "import { elementChildren } from '../../shared/utils.js';\nexport default function loopCreate(slideRealIndex) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  slides.forEach((el, index) => {\n    el.setAttribute('data-swiper-slide-index', index);\n  });\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next'\n  });\n}", "export default function loopFix({\n  slideRealIndex,\n  slideTo = true,\n  direction,\n  setTranslate,\n  activeSlideIndex,\n  byC<PERSON><PERSON>er,\n  byMousewheel\n} = {}) {\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10));\n  let loopedSlides = params.loopedSlides || slidesPerView;\n  if (loopedSlides % params.slidesPerGroup !== 0) {\n    loopedSlides += params.slidesPerGroup - loopedSlides % params.slidesPerGroup;\n  }\n  swiper.loopedSlides = loopedSlides;\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  let activeIndex = swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(swiper.slides.filter(el => el.classList.contains(params.slideActiveClass))[0]);\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  // prepend last slides before start\n  if (activeSlideIndex < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeSlideIndex, params.slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeSlideIndex; i += 1) {\n      const index = i - Math.floor(i / slides.length) * slides.length;\n      prependSlidesIndexes.push(slides.length - index - 1);\n    }\n  } else if (activeSlideIndex /* + slidesPerView */ > swiper.slides.length - loopedSlides * 2) {\n    slidesAppended = Math.max(activeSlideIndex - (swiper.slides.length - loopedSlides * 2), params.slidesPerGroup);\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / slides.length) * slides.length;\n      appendSlidesIndexes.push(index);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      slidesEl.prepend(swiper.slides[index]);\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      slidesEl.append(swiper.slides[index]);\n    });\n  }\n  swiper.recalcSlides();\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + slidesPrepended, 0, false, true);\n          if (setTranslate) {\n            swiper.touches[swiper.isHorizontal() ? 'startX' : 'startY'] += diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          swiper.slideToLoop(slideRealIndex, 0, false, true);\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touches[swiper.isHorizontal() ? 'startX' : 'startY'] += diff;\n          }\n        }\n      } else {\n        swiper.slideToLoop(slideRealIndex, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      slideTo: false,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix(loopParams);\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix(loopParams);\n    }\n  }\n  swiper.emit('loopFix');\n}", "export default function loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}", "import loopCreate from './loopCreate.js';\nimport loopFix from './loopFix.js';\nimport loopDestroy from './loopDestroy.js';\nexport default {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};", "export default function setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}", "export default function unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}", "import setGrabCursor from './setGrabCursor.js';\nimport unsetGrabCursor from './unsetGrabCursor.js';\nexport default {\n  setGrabCursor,\n  unsetGrabCursor\n};", "import { getWindow, getDocument } from 'ssr-window';\nimport { now } from '../../shared/utils.js';\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base = this) {\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nexport default function onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  const window = getWindow();\n  const data = swiper.touchEventsData;\n  data.evCache.push(event);\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!swiper.wrapperEl.contains(targetEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = event.composedPath ? event.composedPath() : event.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  const edgeSwipeDetection = params.edgeSwipeDetection || params.iOSEdgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold || params.iOSEdgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n    } else {\n      return;\n    }\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (swiper.params.freeMode && swiper.params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}", "import { getDocument } from 'ssr-window';\nimport { now } from '../../shared/utils.js';\nexport default function onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pointerIndex = data.evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n  if (pointerIndex >= 0) data.evCache[pointerIndex] = e;\n  const targetTouch = data.evCache.length > 1 ? data.evCache[0] : e;\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        prevX: swiper.touches.currentX,\n        prevY: swiper.touches.currentY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate()) {\n      return;\n    }\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  if (e.targetTouches && e.targetTouches.length > 1) return;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || swiper.zoom && swiper.params.zoom && swiper.params.zoom.enabled && data.evCache.length > 1) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  if (!data.isMoved) {\n    if (isLoop) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  if (data.isMoved && prevTouchesDirection !== swiper.touchesDirection && isLoop && Math.abs(diff) >= 1) {\n    // need another loop fix\n    swiper.loopFix({\n      direction: swiper.swipeDirection,\n      setTranslate: true\n    });\n    loopFixed = true;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && !loopFixed && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.size / 2 : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && !loopFixed && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.size / 2 : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (swiper.params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}", "import { now, nextTick } from '../../shared/utils.js';\nexport default function onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const pointerIndex = data.evCache.findIndex(cachedEv => cachedEv.pointerId === event.pointerId);\n  if (pointerIndex >= 0) {\n    data.evCache.splice(pointerIndex, 1);\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave'].includes(event.type)) {\n    const proceed = event.type === 'pointercancel' && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 || data.currentTranslate === data.startTranslate) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (swiper.params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}", "let timeout;\nexport default function onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}", "export default function onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}", "export default function onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}", "import { processLazyPreloader } from '../../shared/process-lazy-preloader.js';\nexport default function onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  swiper.update();\n}", "import { getDocument } from 'ssr-window';\nimport onTouchStart from './onTouchStart.js';\nimport onTouchMove from './onTouchMove.js';\nimport onTouchEnd from './onTouchEnd.js';\nimport onResize from './onResize.js';\nimport onClick from './onClick.js';\nimport onScroll from './onScroll.js';\nimport onLoad from './onLoad.js';\nlet dummyEventAttached = false;\nfunction dummyEventListener() {}\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n\n  // Touch Events\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const document = getDocument();\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  if (!dummyEventAttached) {\n    document.addEventListener('touchstart', dummyEventListener);\n    dummyEventAttached = true;\n  }\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nexport default {\n  attachEvents,\n  detachEvents\n};", "import { extend } from '../../shared/utils.js';\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nexport default function setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n\n  // Get breakpoint for window width and update parameters\n  const breakpoint = swiper.getBreakpoint(breakpoints, swiper.params.breakpointsBase, swiper.el);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (needsReLoop && initialized) {\n    swiper.loopDestroy();\n    swiper.loopCreate(realIndex);\n    swiper.updateSlides();\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}", "import { getWindow } from 'ssr-window';\nexport default function getBreakpoint(breakpoints, base = 'window', containerEl) {\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}", "import setBreakpoint from './setBreakpoint.js';\nimport getBreakpoint from './getBreakpoint.js';\nexport default {\n  setBreakpoint,\n  getBreakpoint\n};", "function prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nexport default function addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}", "export default function removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}", "import addClasses from './addClasses.js';\nimport removeClasses from './removeClasses.js';\nexport default {\n  addClasses,\n  removeClasses\n};", "function checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nexport default {\n  checkOverflow\n};", "export default {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopedSlides: null,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};", "import { extend } from '../shared/utils.js';\nexport default function moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj = {}) {\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (['navigation', 'pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        auto: true\n      };\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}", "/* eslint no-param-reassign: \"off\" */\nimport { getDocument } from 'ssr-window';\nimport { extend, deleteProps, createElement, elementChildren, elementStyle, elementIndex } from '../shared/utils.js';\nimport { getSupport } from '../shared/get-support.js';\nimport { getDevice } from '../shared/get-device.js';\nimport { getBrowser } from '../shared/get-browser.js';\nimport Resize from './modules/resize/resize.js';\nimport Observer from './modules/observer/observer.js';\nimport eventsEmitter from './events-emitter.js';\nimport update from './update/index.js';\nimport translate from './translate/index.js';\nimport transition from './transition/index.js';\nimport slide from './slide/index.js';\nimport loop from './loop/index.js';\nimport grabCursor from './grab-cursor/index.js';\nimport events from './events/index.js';\nimport breakpoints from './breakpoints/index.js';\nimport classes from './classes/index.js';\nimport checkOverflow from './check-overflow/index.js';\nimport defaults from './defaults.js';\nimport moduleExtendParams from './moduleExtendParams.js';\nimport { processLazyPreloader, preload } from '../shared/process-lazy-preloader.js';\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events,\n  breakpoints,\n  checkOverflow,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor(...args) {\n    let el;\n    let params;\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        evCache: []\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.filter(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index)[0]);\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic(view = 'current', exact = false) {\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex].swiperSlideSize;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (swiper.params.freeMode && swiper.params.freeMode.enabled) {\n      setTranslate();\n      if (swiper.params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((swiper.params.slidesPerView === 'auto' || swiper.params.slidesPerView > 1) && swiper.isEnd && !swiper.params.centeredSlides) {\n        translated = swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection, needUpdate = true) {\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.shadowEl) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement ? el : wrapperEl,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate();\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy(deleteInstance = true, cleanStyles = true) {\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      el.removeAttribute('style');\n      wrapperEl.removeAttribute('style');\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      swiper.el.swiper = null;\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\nexport default Swiper;", "import { getDocument } from 'ssr-window';\nimport { createElement, elementChildren, setCSSProperty } from '../../shared/utils.js';\nexport default function Virtual({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  extendParams({\n    virtual: {\n      enabled: false,\n      slides: [],\n      cache: true,\n      renderSlide: null,\n      renderExternal: null,\n      renderExternalUpdate: true,\n      addSlidesBefore: 0,\n      addSlidesAfter: 0\n    }\n  });\n  let cssModeTimeout;\n  const document = getDocument();\n  swiper.virtual = {\n    cache: {},\n    from: undefined,\n    to: undefined,\n    slides: [],\n    offset: 0,\n    slidesGrid: []\n  };\n  const tempDOM = document.createElement('div');\n  function renderSlide(slide, index) {\n    const params = swiper.params.virtual;\n    if (params.cache && swiper.virtual.cache[index]) {\n      return swiper.virtual.cache[index];\n    }\n    // eslint-disable-next-line\n    let slideEl;\n    if (params.renderSlide) {\n      slideEl = params.renderSlide.call(swiper, slide, index);\n      if (typeof slideEl === 'string') {\n        tempDOM.innerHTML = slideEl;\n        slideEl = tempDOM.children[0];\n      }\n    } else if (swiper.isElement) {\n      slideEl = createElement('swiper-slide');\n    } else {\n      slideEl = createElement('div', swiper.params.slideClass);\n    }\n    slideEl.setAttribute('data-swiper-slide-index', index);\n    if (!params.renderSlide) {\n      slideEl.innerHTML = slide;\n    }\n    if (params.cache) swiper.virtual.cache[index] = slideEl;\n    return slideEl;\n  }\n  function update(force) {\n    const {\n      slidesPerView,\n      slidesPerGroup,\n      centeredSlides,\n      loop: isLoop\n    } = swiper.params;\n    const {\n      addSlidesBefore,\n      addSlidesAfter\n    } = swiper.params.virtual;\n    const {\n      from: previousFrom,\n      to: previousTo,\n      slides,\n      slidesGrid: previousSlidesGrid,\n      offset: previousOffset\n    } = swiper.virtual;\n    if (!swiper.params.cssMode) {\n      swiper.updateActiveIndex();\n    }\n    const activeIndex = swiper.activeIndex || 0;\n    let offsetProp;\n    if (swiper.rtlTranslate) offsetProp = 'right';else offsetProp = swiper.isHorizontal() ? 'left' : 'top';\n    let slidesAfter;\n    let slidesBefore;\n    if (centeredSlides) {\n      slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesAfter;\n      slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesBefore;\n    } else {\n      slidesAfter = slidesPerView + (slidesPerGroup - 1) + addSlidesAfter;\n      slidesBefore = (isLoop ? slidesPerView : slidesPerGroup) + addSlidesBefore;\n    }\n    let from = activeIndex - slidesBefore;\n    let to = activeIndex + slidesAfter;\n    if (!isLoop) {\n      from = Math.max(from, 0);\n      to = Math.min(to, slides.length - 1);\n    }\n    let offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n    if (isLoop && activeIndex >= slidesBefore) {\n      from -= slidesBefore;\n      if (!centeredSlides) offset += swiper.slidesGrid[0];\n    } else if (isLoop && activeIndex < slidesBefore) {\n      from = -slidesBefore;\n      if (centeredSlides) offset += swiper.slidesGrid[0];\n    }\n    Object.assign(swiper.virtual, {\n      from,\n      to,\n      offset,\n      slidesGrid: swiper.slidesGrid,\n      slidesBefore,\n      slidesAfter\n    });\n    function onRendered() {\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n      emit('virtualUpdate');\n    }\n    if (previousFrom === from && previousTo === to && !force) {\n      if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n        swiper.slides.forEach(slideEl => {\n          slideEl.style[offsetProp] = `${offset}px`;\n        });\n      }\n      swiper.updateProgress();\n      emit('virtualUpdate');\n      return;\n    }\n    if (swiper.params.virtual.renderExternal) {\n      swiper.params.virtual.renderExternal.call(swiper, {\n        offset,\n        from,\n        to,\n        slides: function getSlides() {\n          const slidesToRender = [];\n          for (let i = from; i <= to; i += 1) {\n            slidesToRender.push(slides[i]);\n          }\n          return slidesToRender;\n        }()\n      });\n      if (swiper.params.virtual.renderExternalUpdate) {\n        onRendered();\n      } else {\n        emit('virtualUpdate');\n      }\n      return;\n    }\n    const prependIndexes = [];\n    const appendIndexes = [];\n    const getSlideIndex = index => {\n      let slideIndex = index;\n      if (index < 0) {\n        slideIndex = slides.length + index;\n      } else if (slideIndex >= slides.length) {\n        // eslint-disable-next-line\n        slideIndex = slideIndex - slides.length;\n      }\n      return slideIndex;\n    };\n    if (force) {\n      swiper.slidesEl.querySelectorAll(`.${swiper.params.slideClass}, swiper-slide`).forEach(slideEl => {\n        slideEl.remove();\n      });\n    } else {\n      for (let i = previousFrom; i <= previousTo; i += 1) {\n        if (i < from || i > to) {\n          const slideIndex = getSlideIndex(i);\n          swiper.slidesEl.querySelectorAll(`.${swiper.params.slideClass}[data-swiper-slide-index=\"${slideIndex}\"], swiper-slide[data-swiper-slide-index=\"${slideIndex}\"]`).forEach(slideEl => {\n            slideEl.remove();\n          });\n        }\n      }\n    }\n    const loopFrom = isLoop ? -slides.length : 0;\n    const loopTo = isLoop ? slides.length * 2 : slides.length;\n    for (let i = loopFrom; i < loopTo; i += 1) {\n      if (i >= from && i <= to) {\n        const slideIndex = getSlideIndex(i);\n        if (typeof previousTo === 'undefined' || force) {\n          appendIndexes.push(slideIndex);\n        } else {\n          if (i > previousTo) appendIndexes.push(slideIndex);\n          if (i < previousFrom) prependIndexes.push(slideIndex);\n        }\n      }\n    }\n    appendIndexes.forEach(index => {\n      swiper.slidesEl.append(renderSlide(slides[index], index));\n    });\n    if (isLoop) {\n      for (let i = prependIndexes.length - 1; i >= 0; i -= 1) {\n        const index = prependIndexes[i];\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      }\n    } else {\n      prependIndexes.sort((a, b) => b - a);\n      prependIndexes.forEach(index => {\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      });\n    }\n    elementChildren(swiper.slidesEl, '.swiper-slide, swiper-slide').forEach(slideEl => {\n      slideEl.style[offsetProp] = `${offset}px`;\n    });\n    onRendered();\n  }\n  function appendSlide(slides) {\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.push(slides[i]);\n      }\n    } else {\n      swiper.virtual.slides.push(slides);\n    }\n    update(true);\n  }\n  function prependSlide(slides) {\n    const activeIndex = swiper.activeIndex;\n    let newActiveIndex = activeIndex + 1;\n    let numberOfNewSlides = 1;\n    if (Array.isArray(slides)) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.unshift(slides[i]);\n      }\n      newActiveIndex = activeIndex + slides.length;\n      numberOfNewSlides = slides.length;\n    } else {\n      swiper.virtual.slides.unshift(slides);\n    }\n    if (swiper.params.virtual.cache) {\n      const cache = swiper.virtual.cache;\n      const newCache = {};\n      Object.keys(cache).forEach(cachedIndex => {\n        const cachedEl = cache[cachedIndex];\n        const cachedElIndex = cachedEl.getAttribute('data-swiper-slide-index');\n        if (cachedElIndex) {\n          cachedEl.setAttribute('data-swiper-slide-index', parseInt(cachedElIndex, 10) + numberOfNewSlides);\n        }\n        newCache[parseInt(cachedIndex, 10) + numberOfNewSlides] = cachedEl;\n      });\n      swiper.virtual.cache = newCache;\n    }\n    update(true);\n    swiper.slideTo(newActiveIndex, 0);\n  }\n  function removeSlide(slidesIndexes) {\n    if (typeof slidesIndexes === 'undefined' || slidesIndexes === null) return;\n    let activeIndex = swiper.activeIndex;\n    if (Array.isArray(slidesIndexes)) {\n      for (let i = slidesIndexes.length - 1; i >= 0; i -= 1) {\n        swiper.virtual.slides.splice(slidesIndexes[i], 1);\n        if (swiper.params.virtual.cache) {\n          delete swiper.virtual.cache[slidesIndexes[i]];\n        }\n        if (slidesIndexes[i] < activeIndex) activeIndex -= 1;\n        activeIndex = Math.max(activeIndex, 0);\n      }\n    } else {\n      swiper.virtual.slides.splice(slidesIndexes, 1);\n      if (swiper.params.virtual.cache) {\n        delete swiper.virtual.cache[slidesIndexes];\n      }\n      if (slidesIndexes < activeIndex) activeIndex -= 1;\n      activeIndex = Math.max(activeIndex, 0);\n    }\n    update(true);\n    swiper.slideTo(activeIndex, 0);\n  }\n  function removeAllSlides() {\n    swiper.virtual.slides = [];\n    if (swiper.params.virtual.cache) {\n      swiper.virtual.cache = {};\n    }\n    update(true);\n    swiper.slideTo(0, 0);\n  }\n  on('beforeInit', () => {\n    if (!swiper.params.virtual.enabled) return;\n    let domSlidesAssigned;\n    if (typeof swiper.passedParams.virtual.slides === 'undefined') {\n      const slides = [...swiper.slidesEl.children].filter(el => el.matches(`.${swiper.params.slideClass}, swiper-slide`));\n      if (slides && slides.length) {\n        swiper.virtual.slides = [...slides];\n        domSlidesAssigned = true;\n        slides.forEach((slideEl, slideIndex) => {\n          slideEl.setAttribute('data-swiper-slide-index', slideIndex);\n          swiper.virtual.cache[slideIndex] = slideEl;\n          slideEl.remove();\n        });\n      }\n    }\n    if (!domSlidesAssigned) {\n      swiper.virtual.slides = swiper.params.virtual.slides;\n    }\n    swiper.classNames.push(`${swiper.params.containerModifierClass}virtual`);\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n    if (!swiper.params.initialSlide) {\n      update();\n    }\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode && !swiper._immediateVirtual) {\n      clearTimeout(cssModeTimeout);\n      cssModeTimeout = setTimeout(() => {\n        update();\n      }, 100);\n    } else {\n      update();\n    }\n  });\n  on('init update resize', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode) {\n      setCSSProperty(swiper.wrapperEl, '--swiper-virtual-size', `${swiper.virtualSize}px`);\n    }\n  });\n  Object.assign(swiper.virtual, {\n    appendSlide,\n    prependSlide,\n    removeSlide,\n    removeAllSlides,\n    update\n  });\n}", "/* eslint-disable consistent-return */\nimport { getWindow, getDocument } from 'ssr-window';\nimport { elementOffset, elementParents } from '../../shared/utils.js';\nexport default function Keyboard({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  const document = getDocument();\n  const window = getWindow();\n  swiper.keyboard = {\n    enabled: false\n  };\n  extendParams({\n    keyboard: {\n      enabled: false,\n      onlyInViewport: true,\n      pageUpDown: true\n    }\n  });\n  function handle(event) {\n    if (!swiper.enabled) return;\n    const {\n      rtlTranslate: rtl\n    } = swiper;\n    let e = event;\n    if (e.originalEvent) e = e.originalEvent; // jquery fix\n    const kc = e.keyCode || e.charCode;\n    const pageUpDown = swiper.params.keyboard.pageUpDown;\n    const isPageUp = pageUpDown && kc === 33;\n    const isPageDown = pageUpDown && kc === 34;\n    const isArrowLeft = kc === 37;\n    const isArrowRight = kc === 39;\n    const isArrowUp = kc === 38;\n    const isArrowDown = kc === 40;\n    // Directions locks\n    if (!swiper.allowSlideNext && (swiper.isHorizontal() && isArrowRight || swiper.isVertical() && isArrowDown || isPageDown)) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && (swiper.isHorizontal() && isArrowLeft || swiper.isVertical() && isArrowUp || isPageUp)) {\n      return false;\n    }\n    if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n      return undefined;\n    }\n    if (document.activeElement && document.activeElement.nodeName && (document.activeElement.nodeName.toLowerCase() === 'input' || document.activeElement.nodeName.toLowerCase() === 'textarea')) {\n      return undefined;\n    }\n    if (swiper.params.keyboard.onlyInViewport && (isPageUp || isPageDown || isArrowLeft || isArrowRight || isArrowUp || isArrowDown)) {\n      let inView = false;\n      // Check that swiper should be inside of visible area of window\n      if (elementParents(swiper.el, `.${swiper.params.slideClass}, swiper-slide`).length > 0 && elementParents(swiper.el, `.${swiper.params.slideActiveClass}`).length === 0) {\n        return undefined;\n      }\n      const el = swiper.el;\n      const swiperWidth = el.clientWidth;\n      const swiperHeight = el.clientHeight;\n      const windowWidth = window.innerWidth;\n      const windowHeight = window.innerHeight;\n      const swiperOffset = elementOffset(el);\n      if (rtl) swiperOffset.left -= el.scrollLeft;\n      const swiperCoord = [[swiperOffset.left, swiperOffset.top], [swiperOffset.left + swiperWidth, swiperOffset.top], [swiperOffset.left, swiperOffset.top + swiperHeight], [swiperOffset.left + swiperWidth, swiperOffset.top + swiperHeight]];\n      for (let i = 0; i < swiperCoord.length; i += 1) {\n        const point = swiperCoord[i];\n        if (point[0] >= 0 && point[0] <= windowWidth && point[1] >= 0 && point[1] <= windowHeight) {\n          if (point[0] === 0 && point[1] === 0) continue; // eslint-disable-line\n          inView = true;\n        }\n      }\n      if (!inView) return undefined;\n    }\n    if (swiper.isHorizontal()) {\n      if (isPageUp || isPageDown || isArrowLeft || isArrowRight) {\n        if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n      }\n      if ((isPageDown || isArrowRight) && !rtl || (isPageUp || isArrowLeft) && rtl) swiper.slideNext();\n      if ((isPageUp || isArrowLeft) && !rtl || (isPageDown || isArrowRight) && rtl) swiper.slidePrev();\n    } else {\n      if (isPageUp || isPageDown || isArrowUp || isArrowDown) {\n        if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n      }\n      if (isPageDown || isArrowDown) swiper.slideNext();\n      if (isPageUp || isArrowUp) swiper.slidePrev();\n    }\n    emit('keyPress', kc);\n    return undefined;\n  }\n  function enable() {\n    if (swiper.keyboard.enabled) return;\n    document.addEventListener('keydown', handle);\n    swiper.keyboard.enabled = true;\n  }\n  function disable() {\n    if (!swiper.keyboard.enabled) return;\n    document.removeEventListener('keydown', handle);\n    swiper.keyboard.enabled = false;\n  }\n  on('init', () => {\n    if (swiper.params.keyboard.enabled) {\n      enable();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.keyboard.enabled) {\n      disable();\n    }\n  });\n  Object.assign(swiper.keyboard, {\n    enable,\n    disable\n  });\n}", "/* eslint-disable consistent-return */\nimport { getWindow } from 'ssr-window';\nimport { now, nextTick } from '../../shared/utils.js';\nexport default function Mousewheel({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  const window = getWindow();\n  extendParams({\n    mousewheel: {\n      enabled: false,\n      releaseOnEdges: false,\n      invert: false,\n      forceToAxis: false,\n      sensitivity: 1,\n      eventsTarget: 'container',\n      thresholdDelta: null,\n      thresholdTime: null\n    }\n  });\n  swiper.mousewheel = {\n    enabled: false\n  };\n  let timeout;\n  let lastScrollTime = now();\n  let lastEventBeforeSnap;\n  const recentWheelEvents = [];\n  function normalize(e) {\n    // Reasonable defaults\n    const PIXEL_STEP = 10;\n    const LINE_HEIGHT = 40;\n    const PAGE_HEIGHT = 800;\n    let sX = 0;\n    let sY = 0; // spinX, spinY\n    let pX = 0;\n    let pY = 0; // pixelX, pixelY\n\n    // Legacy\n    if ('detail' in e) {\n      sY = e.detail;\n    }\n    if ('wheelDelta' in e) {\n      sY = -e.wheelDelta / 120;\n    }\n    if ('wheelDeltaY' in e) {\n      sY = -e.wheelDeltaY / 120;\n    }\n    if ('wheelDeltaX' in e) {\n      sX = -e.wheelDeltaX / 120;\n    }\n\n    // side scrolling on FF with DOMMouseScroll\n    if ('axis' in e && e.axis === e.HORIZONTAL_AXIS) {\n      sX = sY;\n      sY = 0;\n    }\n    pX = sX * PIXEL_STEP;\n    pY = sY * PIXEL_STEP;\n    if ('deltaY' in e) {\n      pY = e.deltaY;\n    }\n    if ('deltaX' in e) {\n      pX = e.deltaX;\n    }\n    if (e.shiftKey && !pX) {\n      // if user scrolls with shift he wants horizontal scroll\n      pX = pY;\n      pY = 0;\n    }\n    if ((pX || pY) && e.deltaMode) {\n      if (e.deltaMode === 1) {\n        // delta in LINE units\n        pX *= LINE_HEIGHT;\n        pY *= LINE_HEIGHT;\n      } else {\n        // delta in PAGE units\n        pX *= PAGE_HEIGHT;\n        pY *= PAGE_HEIGHT;\n      }\n    }\n\n    // Fall-back if spin cannot be determined\n    if (pX && !sX) {\n      sX = pX < 1 ? -1 : 1;\n    }\n    if (pY && !sY) {\n      sY = pY < 1 ? -1 : 1;\n    }\n    return {\n      spinX: sX,\n      spinY: sY,\n      pixelX: pX,\n      pixelY: pY\n    };\n  }\n  function handleMouseEnter() {\n    if (!swiper.enabled) return;\n    swiper.mouseEntered = true;\n  }\n  function handleMouseLeave() {\n    if (!swiper.enabled) return;\n    swiper.mouseEntered = false;\n  }\n  function animateSlider(newEvent) {\n    if (swiper.params.mousewheel.thresholdDelta && newEvent.delta < swiper.params.mousewheel.thresholdDelta) {\n      // Prevent if delta of wheel scroll delta is below configured threshold\n      return false;\n    }\n    if (swiper.params.mousewheel.thresholdTime && now() - lastScrollTime < swiper.params.mousewheel.thresholdTime) {\n      // Prevent if time between scrolls is below configured threshold\n      return false;\n    }\n\n    // If the movement is NOT big enough and\n    // if the last time the user scrolled was too close to the current one (avoid continuously triggering the slider):\n    //   Don't go any further (avoid insignificant scroll movement).\n    if (newEvent.delta >= 6 && now() - lastScrollTime < 60) {\n      // Return false as a default\n      return true;\n    }\n    // If user is scrolling towards the end:\n    //   If the slider hasn't hit the latest slide or\n    //   if the slider is a loop and\n    //   if the slider isn't moving right now:\n    //     Go to next slide and\n    //     emit a scroll event.\n    // Else (the user is scrolling towards the beginning) and\n    // if the slider hasn't hit the first slide or\n    // if the slider is a loop and\n    // if the slider isn't moving right now:\n    //   Go to prev slide and\n    //   emit a scroll event.\n    if (newEvent.direction < 0) {\n      if ((!swiper.isEnd || swiper.params.loop) && !swiper.animating) {\n        swiper.slideNext();\n        emit('scroll', newEvent.raw);\n      }\n    } else if ((!swiper.isBeginning || swiper.params.loop) && !swiper.animating) {\n      swiper.slidePrev();\n      emit('scroll', newEvent.raw);\n    }\n    // If you got here is because an animation has been triggered so store the current time\n    lastScrollTime = new window.Date().getTime();\n    // Return false as a default\n    return false;\n  }\n  function releaseScroll(newEvent) {\n    const params = swiper.params.mousewheel;\n    if (newEvent.direction < 0) {\n      if (swiper.isEnd && !swiper.params.loop && params.releaseOnEdges) {\n        // Return true to animate scroll on edges\n        return true;\n      }\n    } else if (swiper.isBeginning && !swiper.params.loop && params.releaseOnEdges) {\n      // Return true to animate scroll on edges\n      return true;\n    }\n    return false;\n  }\n  function handle(event) {\n    let e = event;\n    let disableParentSwiper = true;\n    if (!swiper.enabled) return;\n    const params = swiper.params.mousewheel;\n    if (swiper.params.cssMode) {\n      e.preventDefault();\n    }\n    let targetEl = swiper.el;\n    if (swiper.params.mousewheel.eventsTarget !== 'container') {\n      targetEl = document.querySelector(swiper.params.mousewheel.eventsTarget);\n    }\n    const targetElContainsTarget = targetEl && targetEl.contains(e.target);\n    if (!swiper.mouseEntered && !targetElContainsTarget && !params.releaseOnEdges) return true;\n    if (e.originalEvent) e = e.originalEvent; // jquery fix\n    let delta = 0;\n    const rtlFactor = swiper.rtlTranslate ? -1 : 1;\n    const data = normalize(e);\n    if (params.forceToAxis) {\n      if (swiper.isHorizontal()) {\n        if (Math.abs(data.pixelX) > Math.abs(data.pixelY)) delta = -data.pixelX * rtlFactor;else return true;\n      } else if (Math.abs(data.pixelY) > Math.abs(data.pixelX)) delta = -data.pixelY;else return true;\n    } else {\n      delta = Math.abs(data.pixelX) > Math.abs(data.pixelY) ? -data.pixelX * rtlFactor : -data.pixelY;\n    }\n    if (delta === 0) return true;\n    if (params.invert) delta = -delta;\n\n    // Get the scroll positions\n    let positions = swiper.getTranslate() + delta * params.sensitivity;\n    if (positions >= swiper.minTranslate()) positions = swiper.minTranslate();\n    if (positions <= swiper.maxTranslate()) positions = swiper.maxTranslate();\n\n    // When loop is true:\n    //     the disableParentSwiper will be true.\n    // When loop is false:\n    //     if the scroll positions is not on edge,\n    //     then the disableParentSwiper will be true.\n    //     if the scroll on edge positions,\n    //     then the disableParentSwiper will be false.\n    disableParentSwiper = swiper.params.loop ? true : !(positions === swiper.minTranslate() || positions === swiper.maxTranslate());\n    if (disableParentSwiper && swiper.params.nested) e.stopPropagation();\n    if (!swiper.params.freeMode || !swiper.params.freeMode.enabled) {\n      // Register the new event in a variable which stores the relevant data\n      const newEvent = {\n        time: now(),\n        delta: Math.abs(delta),\n        direction: Math.sign(delta),\n        raw: event\n      };\n\n      // Keep the most recent events\n      if (recentWheelEvents.length >= 2) {\n        recentWheelEvents.shift(); // only store the last N events\n      }\n\n      const prevEvent = recentWheelEvents.length ? recentWheelEvents[recentWheelEvents.length - 1] : undefined;\n      recentWheelEvents.push(newEvent);\n\n      // If there is at least one previous recorded event:\n      //   If direction has changed or\n      //   if the scroll is quicker than the previous one:\n      //     Animate the slider.\n      // Else (this is the first time the wheel is moved):\n      //     Animate the slider.\n      if (prevEvent) {\n        if (newEvent.direction !== prevEvent.direction || newEvent.delta > prevEvent.delta || newEvent.time > prevEvent.time + 150) {\n          animateSlider(newEvent);\n        }\n      } else {\n        animateSlider(newEvent);\n      }\n\n      // If it's time to release the scroll:\n      //   Return now so you don't hit the preventDefault.\n      if (releaseScroll(newEvent)) {\n        return true;\n      }\n    } else {\n      // Freemode or scrollContainer:\n\n      // If we recently snapped after a momentum scroll, then ignore wheel events\n      // to give time for the deceleration to finish. Stop ignoring after 500 msecs\n      // or if it's a new scroll (larger delta or inverse sign as last event before\n      // an end-of-momentum snap).\n      const newEvent = {\n        time: now(),\n        delta: Math.abs(delta),\n        direction: Math.sign(delta)\n      };\n      const ignoreWheelEvents = lastEventBeforeSnap && newEvent.time < lastEventBeforeSnap.time + 500 && newEvent.delta <= lastEventBeforeSnap.delta && newEvent.direction === lastEventBeforeSnap.direction;\n      if (!ignoreWheelEvents) {\n        lastEventBeforeSnap = undefined;\n        let position = swiper.getTranslate() + delta * params.sensitivity;\n        const wasBeginning = swiper.isBeginning;\n        const wasEnd = swiper.isEnd;\n        if (position >= swiper.minTranslate()) position = swiper.minTranslate();\n        if (position <= swiper.maxTranslate()) position = swiper.maxTranslate();\n        swiper.setTransition(0);\n        swiper.setTranslate(position);\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n        if (!wasBeginning && swiper.isBeginning || !wasEnd && swiper.isEnd) {\n          swiper.updateSlidesClasses();\n        }\n        if (swiper.params.loop) {\n          swiper.loopFix({\n            direction: newEvent.direction < 0 ? 'next' : 'prev',\n            byMousewheel: true\n          });\n        }\n        if (swiper.params.freeMode.sticky) {\n          // When wheel scrolling starts with sticky (aka snap) enabled, then detect\n          // the end of a momentum scroll by storing recent (N=15?) wheel events.\n          // 1. do all N events have decreasing or same (absolute value) delta?\n          // 2. did all N events arrive in the last M (M=500?) msecs?\n          // 3. does the earliest event have an (absolute value) delta that's\n          //    at least P (P=1?) larger than the most recent event's delta?\n          // 4. does the latest event have a delta that's smaller than Q (Q=6?) pixels?\n          // If 1-4 are \"yes\" then we're near the end of a momentum scroll deceleration.\n          // Snap immediately and ignore remaining wheel events in this scroll.\n          // See comment above for \"remaining wheel events in this scroll\" determination.\n          // If 1-4 aren't satisfied, then wait to snap until 500ms after the last event.\n          clearTimeout(timeout);\n          timeout = undefined;\n          if (recentWheelEvents.length >= 15) {\n            recentWheelEvents.shift(); // only store the last N events\n          }\n\n          const prevEvent = recentWheelEvents.length ? recentWheelEvents[recentWheelEvents.length - 1] : undefined;\n          const firstEvent = recentWheelEvents[0];\n          recentWheelEvents.push(newEvent);\n          if (prevEvent && (newEvent.delta > prevEvent.delta || newEvent.direction !== prevEvent.direction)) {\n            // Increasing or reverse-sign delta means the user started scrolling again. Clear the wheel event log.\n            recentWheelEvents.splice(0);\n          } else if (recentWheelEvents.length >= 15 && newEvent.time - firstEvent.time < 500 && firstEvent.delta - newEvent.delta >= 1 && newEvent.delta <= 6) {\n            // We're at the end of the deceleration of a momentum scroll, so there's no need\n            // to wait for more events. Snap ASAP on the next tick.\n            // Also, because there's some remaining momentum we'll bias the snap in the\n            // direction of the ongoing scroll because it's better UX for the scroll to snap\n            // in the same direction as the scroll instead of reversing to snap.  Therefore,\n            // if it's already scrolled more than 20% in the current direction, keep going.\n            const snapToThreshold = delta > 0 ? 0.8 : 0.2;\n            lastEventBeforeSnap = newEvent;\n            recentWheelEvents.splice(0);\n            timeout = nextTick(() => {\n              swiper.slideToClosest(swiper.params.speed, true, undefined, snapToThreshold);\n            }, 0); // no delay; move on next tick\n          }\n\n          if (!timeout) {\n            // if we get here, then we haven't detected the end of a momentum scroll, so\n            // we'll consider a scroll \"complete\" when there haven't been any wheel events\n            // for 500ms.\n            timeout = nextTick(() => {\n              const snapToThreshold = 0.5;\n              lastEventBeforeSnap = newEvent;\n              recentWheelEvents.splice(0);\n              swiper.slideToClosest(swiper.params.speed, true, undefined, snapToThreshold);\n            }, 500);\n          }\n        }\n\n        // Emit event\n        if (!ignoreWheelEvents) emit('scroll', e);\n\n        // Stop autoplay\n        if (swiper.params.autoplay && swiper.params.autoplayDisableOnInteraction) swiper.autoplay.stop();\n        // Return page scroll on edge positions\n        if (position === swiper.minTranslate() || position === swiper.maxTranslate()) return true;\n      }\n    }\n    if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n    return false;\n  }\n  function events(method) {\n    let targetEl = swiper.el;\n    if (swiper.params.mousewheel.eventsTarget !== 'container') {\n      targetEl = document.querySelector(swiper.params.mousewheel.eventsTarget);\n    }\n    targetEl[method]('mouseenter', handleMouseEnter);\n    targetEl[method]('mouseleave', handleMouseLeave);\n    targetEl[method]('wheel', handle);\n  }\n  function enable() {\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.removeEventListener('wheel', handle);\n      return true;\n    }\n    if (swiper.mousewheel.enabled) return false;\n    events('addEventListener');\n    swiper.mousewheel.enabled = true;\n    return true;\n  }\n  function disable() {\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.addEventListener(event, handle);\n      return true;\n    }\n    if (!swiper.mousewheel.enabled) return false;\n    events('removeEventListener');\n    swiper.mousewheel.enabled = false;\n    return true;\n  }\n  on('init', () => {\n    if (!swiper.params.mousewheel.enabled && swiper.params.cssMode) {\n      disable();\n    }\n    if (swiper.params.mousewheel.enabled) enable();\n  });\n  on('destroy', () => {\n    if (swiper.params.cssMode) {\n      enable();\n    }\n    if (swiper.mousewheel.enabled) disable();\n  });\n  Object.assign(swiper.mousewheel, {\n    enable,\n    disable\n  });\n}", "import { createElement, elementChildren } from './utils.js';\nexport default function createElementIfNotDefined(swiper, originalParams, params, checkProps) {\n  if (swiper.params.createElements) {\n    Object.keys(checkProps).forEach(key => {\n      if (!params[key] && params.auto === true) {\n        let element = elementChildren(swiper.el, `.${checkProps[key]}`)[0];\n        if (!element) {\n          element = createElement('div', checkProps[key]);\n          element.className = checkProps[key];\n          swiper.el.append(element);\n        }\n        params[key] = element;\n        originalParams[key] = element;\n      }\n    });\n  }\n  return params;\n}", "import createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\nexport default function Navigation({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  extendParams({\n    navigation: {\n      nextEl: null,\n      prevEl: null,\n      hideOnClick: false,\n      disabledClass: 'swiper-button-disabled',\n      hiddenClass: 'swiper-button-hidden',\n      lockClass: 'swiper-button-lock',\n      navigationDisabledClass: 'swiper-navigation-disabled'\n    }\n  });\n  swiper.navigation = {\n    nextEl: null,\n    prevEl: null\n  };\n  const makeElementsArray = el => {\n    if (!Array.isArray(el)) el = [el].filter(e => !!e);\n    return el;\n  };\n  function getEl(el) {\n    let res;\n    if (el && typeof el === 'string' && swiper.isElement) {\n      res = swiper.el.shadowRoot.querySelector(el);\n      if (res) return res;\n    }\n    if (el) {\n      if (typeof el === 'string') res = [...document.querySelectorAll(el)];\n      if (swiper.params.uniqueNavElements && typeof el === 'string' && res.length > 1 && swiper.el.querySelectorAll(el).length === 1) {\n        res = swiper.el.querySelector(el);\n      }\n    }\n    if (el && !res) return el;\n    // if (Array.isArray(res) && res.length === 1) res = res[0];\n    return res;\n  }\n  function toggleEl(el, disabled) {\n    const params = swiper.params.navigation;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (subEl) {\n        subEl.classList[disabled ? 'add' : 'remove'](...params.disabledClass.split(' '));\n        if (subEl.tagName === 'BUTTON') subEl.disabled = disabled;\n        if (swiper.params.watchOverflow && swiper.enabled) {\n          subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n        }\n      }\n    });\n  }\n  function update() {\n    // Update Navigation Buttons\n    const {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    if (swiper.params.loop) {\n      toggleEl(prevEl, false);\n      toggleEl(nextEl, false);\n      return;\n    }\n    toggleEl(prevEl, swiper.isBeginning && !swiper.params.rewind);\n    toggleEl(nextEl, swiper.isEnd && !swiper.params.rewind);\n  }\n  function onPrevClick(e) {\n    e.preventDefault();\n    if (swiper.isBeginning && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slidePrev();\n    emit('navigationPrev');\n  }\n  function onNextClick(e) {\n    e.preventDefault();\n    if (swiper.isEnd && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slideNext();\n    emit('navigationNext');\n  }\n  function init() {\n    const params = swiper.params.navigation;\n    swiper.params.navigation = createElementIfNotDefined(swiper, swiper.originalParams.navigation, swiper.params.navigation, {\n      nextEl: 'swiper-button-next',\n      prevEl: 'swiper-button-prev'\n    });\n    if (!(params.nextEl || params.prevEl)) return;\n    let nextEl = getEl(params.nextEl);\n    let prevEl = getEl(params.prevEl);\n    Object.assign(swiper.navigation, {\n      nextEl,\n      prevEl\n    });\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const initButton = (el, dir) => {\n      if (el) {\n        el.addEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      }\n      if (!swiper.enabled && el) {\n        el.classList.add(...params.lockClass.split(' '));\n      }\n    };\n    nextEl.forEach(el => initButton(el, 'next'));\n    prevEl.forEach(el => initButton(el, 'prev'));\n  }\n  function destroy() {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const destroyButton = (el, dir) => {\n      el.removeEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      el.classList.remove(...swiper.params.navigation.disabledClass.split(' '));\n    };\n    nextEl.forEach(el => destroyButton(el, 'next'));\n    prevEl.forEach(el => destroyButton(el, 'prev'));\n  }\n  on('init', () => {\n    if (swiper.params.navigation.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      update();\n    }\n  });\n  on('toEdge fromEdge lock unlock', () => {\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    [...nextEl, ...prevEl].filter(el => !!el).forEach(el => el.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.navigation.lockClass));\n  });\n  on('click', (_s, e) => {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const targetEl = e.target;\n    if (swiper.params.navigation.hideOnClick && !prevEl.includes(targetEl) && !nextEl.includes(targetEl)) {\n      if (swiper.pagination && swiper.params.pagination && swiper.params.pagination.clickable && (swiper.pagination.el === targetEl || swiper.pagination.el.contains(targetEl))) return;\n      let isHidden;\n      if (nextEl.length) {\n        isHidden = nextEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      } else if (prevEl.length) {\n        isHidden = prevEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      }\n      if (isHidden === true) {\n        emit('navigationShow');\n      } else {\n        emit('navigationHide');\n      }\n      [...nextEl, ...prevEl].filter(el => !!el).forEach(el => el.classList.toggle(swiper.params.navigation.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    init();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    destroy();\n  };\n  Object.assign(swiper.navigation, {\n    enable,\n    disable,\n    update,\n    init,\n    destroy\n  });\n}", "export default function classesToSelector(classes = '') {\n  return `.${classes.trim().replace(/([\\.:!+\\/])/g, '\\\\$1') // eslint-disable-line\n  .replace(/ /g, '.')}`;\n}", "import classesToSelector from '../../shared/classes-to-selector.js';\nimport createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\nimport { elementIndex, elementOuterSize, elementParents } from '../../shared/utils.js';\nexport default function Pagination({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets',\n      // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: number => number,\n      formatFractionTotal: number => number,\n      bulletClass: `${pfx}-bullet`,\n      bulletActiveClass: `${pfx}-bullet-active`,\n      modifierClass: `${pfx}-`,\n      currentClass: `${pfx}-current`,\n      totalClass: `${pfx}-total`,\n      hiddenClass: `${pfx}-hidden`,\n      progressbarFillClass: `${pfx}-progressbar-fill`,\n      progressbarOppositeClass: `${pfx}-progressbar-opposite`,\n      clickableClass: `${pfx}-clickable`,\n      lockClass: `${pfx}-lock`,\n      horizontalClass: `${pfx}-horizontal`,\n      verticalClass: `${pfx}-vertical`,\n      paginationDisabledClass: `${pfx}-disabled`\n    }\n  });\n  swiper.pagination = {\n    el: null,\n    bullets: []\n  };\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n  const makeElementsArray = el => {\n    if (!Array.isArray(el)) el = [el].filter(e => !!e);\n    return el;\n  };\n  function isPaginationDisabled() {\n    return !swiper.params.pagination.el || !swiper.pagination.el || Array.isArray(swiper.pagination.el) && swiper.pagination.el.length === 0;\n  }\n  function setSideBullets(bulletEl, position) {\n    const {\n      bulletActiveClass\n    } = swiper.params.pagination;\n    if (!bulletEl) return;\n    bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n    if (bulletEl) {\n      bulletEl.classList.add(`${bulletActiveClass}-${position}`);\n      bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n      if (bulletEl) {\n        bulletEl.classList.add(`${bulletActiveClass}-${position}-${position}`);\n      }\n    }\n  }\n  function onBulletClick(e) {\n    const bulletEl = e.target.closest(classesToSelector(swiper.params.pagination.bulletClass));\n    if (!bulletEl) {\n      return;\n    }\n    e.preventDefault();\n    const index = elementIndex(bulletEl) * swiper.params.slidesPerGroup;\n    if (swiper.params.loop) {\n      if (swiper.realIndex === index) return;\n      if (index < swiper.loopedSlides || index > swiper.slides.length - swiper.loopedSlides) {\n        swiper.loopFix({\n          direction: index < swiper.loopedSlides ? 'prev' : 'next',\n          activeSlideIndex: index,\n          slideTo: false\n        });\n      }\n      swiper.slideToLoop(index);\n    } else {\n      swiper.slideTo(index);\n    }\n  }\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    // Current/Total\n    let current;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const total = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      current = swiper.params.slidesPerGroup > 1 ? Math.floor(swiper.realIndex / swiper.params.slidesPerGroup) : swiper.realIndex;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n    } else {\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        bulletSize = elementOuterSize(bullets[0], swiper.isHorizontal() ? 'width' : 'height', true);\n        el.forEach(subEl => {\n          subEl.style[swiper.isHorizontal() ? 'width' : 'height'] = `${bulletSize * (params.dynamicMainBullets + 4)}px`;\n        });\n        if (params.dynamicMainBullets > 1 && swiper.previousIndex !== undefined) {\n          dynamicBulletIndex += current - (swiper.previousIndex || 0);\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.forEach(bulletEl => {\n        const classesToRemove = [...['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(suffix => `${params.bulletActiveClass}${suffix}`)].map(s => typeof s === 'string' && s.includes(' ') ? s.split(' ') : s).flat();\n        bulletEl.classList.remove(...classesToRemove);\n      });\n      if (el.length > 1) {\n        bullets.forEach(bullet => {\n          const bulletIndex = elementIndex(bullet);\n          if (bulletIndex === current) {\n            bullet.classList.add(...params.bulletActiveClass.split(' '));\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              bullet.classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n            if (bulletIndex === firstIndex) {\n              setSideBullets(bullet, 'prev');\n            }\n            if (bulletIndex === lastIndex) {\n              setSideBullets(bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const bullet = bullets[current];\n        if (bullet) {\n          bullet.classList.add(...params.bulletActiveClass.split(' '));\n        }\n        if (params.dynamicBullets) {\n          const firstDisplayedBullet = bullets[firstIndex];\n          const lastDisplayedBullet = bullets[lastIndex];\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            if (bullets[i]) {\n              bullets[i].classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n          }\n          setSideBullets(firstDisplayedBullet, 'prev');\n          setSideBullets(lastDisplayedBullet, 'next');\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset = (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.forEach(bullet => {\n          bullet.style[swiper.isHorizontal() ? offsetProp : 'top'] = `${bulletsOffset}px`;\n        });\n      }\n    }\n    el.forEach((subEl, subElIndex) => {\n      if (params.type === 'fraction') {\n        subEl.querySelectorAll(classesToSelector(params.currentClass)).forEach(fractionEl => {\n          fractionEl.textContent = params.formatFractionCurrent(current + 1);\n        });\n        subEl.querySelectorAll(classesToSelector(params.totalClass)).forEach(totalEl => {\n          totalEl.textContent = params.formatFractionTotal(total);\n        });\n      }\n      if (params.type === 'progressbar') {\n        let progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        const scale = (current + 1) / total;\n        let scaleX = 1;\n        let scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        subEl.querySelectorAll(classesToSelector(params.progressbarFillClass)).forEach(progressEl => {\n          progressEl.style.transform = `translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`;\n          progressEl.style.transitionDuration = `${swiper.params.speed}ms`;\n        });\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        subEl.innerHTML = params.renderCustom(swiper, current + 1, total);\n        if (subElIndex === 0) emit('paginationRender', subEl);\n      } else {\n        if (subElIndex === 0) emit('paginationRender', subEl);\n        emit('paginationUpdate', subEl);\n      }\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n      }\n    });\n  }\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.freeMode && swiper.params.freeMode.enabled && numberOfBullets > slidesLength) {\n        numberOfBullets = slidesLength;\n      }\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          paginationHTML += `<${params.bulletElement} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML = `<span class=\"${params.currentClass}\"></span>` + ' / ' + `<span class=\"${params.totalClass}\"></span>`;\n      }\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n    }\n    swiper.pagination.bullets = [];\n    el.forEach(subEl => {\n      if (params.type !== 'custom') {\n        subEl.innerHTML = paginationHTML || '';\n      }\n      if (params.type === 'bullets') {\n        swiper.pagination.bullets.push(...subEl.querySelectorAll(classesToSelector(params.bulletClass)));\n      }\n    });\n    if (params.type !== 'custom') {\n      emit('paginationRender', el[0]);\n    }\n  }\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(swiper, swiper.originalParams.pagination, swiper.params.pagination, {\n      el: 'swiper-pagination'\n    });\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.shadowRoot.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = [...document.querySelectorAll(params.el)];\n    }\n    if (!el) {\n      el = params.el;\n    }\n    if (!el || el.length === 0) return;\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && Array.isArray(el) && el.length > 1) {\n      el = [...swiper.el.querySelectorAll(params.el)];\n      // check if it belongs to another nested Swiper\n      if (el.length > 1) {\n        el = el.filter(subEl => {\n          if (elementParents(subEl, '.swiper')[0] !== swiper.el) return false;\n          return true;\n        })[0];\n      }\n    }\n    if (Array.isArray(el) && el.length === 1) el = el[0];\n    Object.assign(swiper.pagination, {\n      el\n    });\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (params.type === 'bullets' && params.clickable) {\n        subEl.classList.add(params.clickableClass);\n      }\n      subEl.classList.add(params.modifierClass + params.type);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        subEl.classList.add(`${params.modifierClass}${params.type}-dynamic`);\n        dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        subEl.classList.add(params.progressbarOppositeClass);\n      }\n      if (params.clickable) {\n        subEl.addEventListener('click', onBulletClick);\n      }\n      if (!swiper.enabled) {\n        subEl.classList.add(params.lockClass);\n      }\n    });\n  }\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => {\n        subEl.classList.remove(params.hiddenClass);\n        subEl.classList.remove(params.modifierClass + params.type);\n        subEl.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n        if (params.clickable) {\n          subEl.removeEventListener('click', onBulletClick);\n        }\n      });\n    }\n    if (swiper.pagination.bullets) swiper.pagination.bullets.forEach(subEl => subEl.classList.remove(...params.bulletActiveClass.split(' ')));\n  }\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    update();\n  });\n  on('snapGridLengthChange', () => {\n    render();\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.pagination.lockClass));\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    let {\n      el\n    } = swiper.pagination;\n    if (!Array.isArray(el)) el = [el].filter(element => !!element);\n    if (swiper.params.pagination.el && swiper.params.pagination.hideOnClick && el && el.length > 0 && !targetEl.classList.contains(swiper.params.pagination.bulletClass)) {\n      if (swiper.navigation && (swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl || swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl)) return;\n      const isHidden = el[0].classList.contains(swiper.params.pagination.hiddenClass);\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n      el.forEach(subEl => subEl.classList.toggle(swiper.params.pagination.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.remove(swiper.params.pagination.paginationDisabledClass));\n    }\n    init();\n    render();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.add(swiper.params.pagination.paginationDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy\n  });\n}", "import { getDocument } from 'ssr-window';\nimport { createElement, elementOffset, nextTick } from '../../shared/utils.js';\nimport createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\nexport default function Scrollbar({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  const document = getDocument();\n  let isTouched = false;\n  let timeout = null;\n  let dragTimeout = null;\n  let dragStartPos;\n  let dragSize;\n  let trackSize;\n  let divider;\n  extendParams({\n    scrollbar: {\n      el: null,\n      dragSize: 'auto',\n      hide: false,\n      draggable: false,\n      snapOnRelease: true,\n      lockClass: 'swiper-scrollbar-lock',\n      dragClass: 'swiper-scrollbar-drag',\n      scrollbarDisabledClass: 'swiper-scrollbar-disabled',\n      horizontalClass: `swiper-scrollbar-horizontal`,\n      verticalClass: `swiper-scrollbar-vertical`\n    }\n  });\n  swiper.scrollbar = {\n    el: null,\n    dragEl: null\n  };\n  function setTranslate() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const {\n      scrollbar,\n      rtlTranslate: rtl\n    } = swiper;\n    const {\n      dragEl,\n      el\n    } = scrollbar;\n    const params = swiper.params.scrollbar;\n    const progress = swiper.params.loop ? swiper.progressLoop : swiper.progress;\n    let newSize = dragSize;\n    let newPos = (trackSize - dragSize) * progress;\n    if (rtl) {\n      newPos = -newPos;\n      if (newPos > 0) {\n        newSize = dragSize - newPos;\n        newPos = 0;\n      } else if (-newPos + dragSize > trackSize) {\n        newSize = trackSize + newPos;\n      }\n    } else if (newPos < 0) {\n      newSize = dragSize + newPos;\n      newPos = 0;\n    } else if (newPos + dragSize > trackSize) {\n      newSize = trackSize - newPos;\n    }\n    if (swiper.isHorizontal()) {\n      dragEl.style.transform = `translate3d(${newPos}px, 0, 0)`;\n      dragEl.style.width = `${newSize}px`;\n    } else {\n      dragEl.style.transform = `translate3d(0px, ${newPos}px, 0)`;\n      dragEl.style.height = `${newSize}px`;\n    }\n    if (params.hide) {\n      clearTimeout(timeout);\n      el.style.opacity = 1;\n      timeout = setTimeout(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n  }\n  function setTransition(duration) {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    swiper.scrollbar.dragEl.style.transitionDuration = `${duration}ms`;\n  }\n  function updateSize() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const {\n      scrollbar\n    } = swiper;\n    const {\n      dragEl,\n      el\n    } = scrollbar;\n    dragEl.style.width = '';\n    dragEl.style.height = '';\n    trackSize = swiper.isHorizontal() ? el.offsetWidth : el.offsetHeight;\n    divider = swiper.size / (swiper.virtualSize + swiper.params.slidesOffsetBefore - (swiper.params.centeredSlides ? swiper.snapGrid[0] : 0));\n    if (swiper.params.scrollbar.dragSize === 'auto') {\n      dragSize = trackSize * divider;\n    } else {\n      dragSize = parseInt(swiper.params.scrollbar.dragSize, 10);\n    }\n    if (swiper.isHorizontal()) {\n      dragEl.style.width = `${dragSize}px`;\n    } else {\n      dragEl.style.height = `${dragSize}px`;\n    }\n    if (divider >= 1) {\n      el.style.display = 'none';\n    } else {\n      el.style.display = '';\n    }\n    if (swiper.params.scrollbar.hide) {\n      el.style.opacity = 0;\n    }\n    if (swiper.params.watchOverflow && swiper.enabled) {\n      scrollbar.el.classList[swiper.isLocked ? 'add' : 'remove'](swiper.params.scrollbar.lockClass);\n    }\n  }\n  function getPointerPosition(e) {\n    return swiper.isHorizontal() ? e.clientX : e.clientY;\n  }\n  function setDragPosition(e) {\n    const {\n      scrollbar,\n      rtlTranslate: rtl\n    } = swiper;\n    const {\n      el\n    } = scrollbar;\n    let positionRatio;\n    positionRatio = (getPointerPosition(e) - elementOffset(el)[swiper.isHorizontal() ? 'left' : 'top'] - (dragStartPos !== null ? dragStartPos : dragSize / 2)) / (trackSize - dragSize);\n    positionRatio = Math.max(Math.min(positionRatio, 1), 0);\n    if (rtl) {\n      positionRatio = 1 - positionRatio;\n    }\n    const position = swiper.minTranslate() + (swiper.maxTranslate() - swiper.minTranslate()) * positionRatio;\n    swiper.updateProgress(position);\n    swiper.setTranslate(position);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  function onDragStart(e) {\n    const params = swiper.params.scrollbar;\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el,\n      dragEl\n    } = scrollbar;\n    isTouched = true;\n    dragStartPos = e.target === dragEl ? getPointerPosition(e) - e.target.getBoundingClientRect()[swiper.isHorizontal() ? 'left' : 'top'] : null;\n    e.preventDefault();\n    e.stopPropagation();\n    wrapperEl.style.transitionDuration = '100ms';\n    dragEl.style.transitionDuration = '100ms';\n    setDragPosition(e);\n    clearTimeout(dragTimeout);\n    el.style.transitionDuration = '0ms';\n    if (params.hide) {\n      el.style.opacity = 1;\n    }\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = 'none';\n    }\n    emit('scrollbarDragStart', e);\n  }\n  function onDragMove(e) {\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el,\n      dragEl\n    } = scrollbar;\n    if (!isTouched) return;\n    if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n    setDragPosition(e);\n    wrapperEl.style.transitionDuration = '0ms';\n    el.style.transitionDuration = '0ms';\n    dragEl.style.transitionDuration = '0ms';\n    emit('scrollbarDragMove', e);\n  }\n  function onDragEnd(e) {\n    const params = swiper.params.scrollbar;\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el\n    } = scrollbar;\n    if (!isTouched) return;\n    isTouched = false;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = '';\n      wrapperEl.style.transitionDuration = '';\n    }\n    if (params.hide) {\n      clearTimeout(dragTimeout);\n      dragTimeout = nextTick(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n    emit('scrollbarDragEnd', e);\n    if (params.snapOnRelease) {\n      swiper.slideToClosest();\n    }\n  }\n  function events(method) {\n    const {\n      scrollbar,\n      params\n    } = swiper;\n    const el = scrollbar.el;\n    if (!el) return;\n    const target = el;\n    const activeListener = params.passiveListeners ? {\n      passive: false,\n      capture: false\n    } : false;\n    const passiveListener = params.passiveListeners ? {\n      passive: true,\n      capture: false\n    } : false;\n    if (!target) return;\n    const eventMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n    target[eventMethod]('pointerdown', onDragStart, activeListener);\n    document[eventMethod]('pointermove', onDragMove, activeListener);\n    document[eventMethod]('pointerup', onDragEnd, passiveListener);\n  }\n  function enableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('on');\n  }\n  function disableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('off');\n  }\n  function init() {\n    const {\n      scrollbar,\n      el: swiperEl\n    } = swiper;\n    swiper.params.scrollbar = createElementIfNotDefined(swiper, swiper.originalParams.scrollbar, swiper.params.scrollbar, {\n      el: 'swiper-scrollbar'\n    });\n    const params = swiper.params.scrollbar;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.shadowRoot.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = document.querySelectorAll(params.el);\n    } else if (!el) {\n      el = params.el;\n    }\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && el.length > 1 && swiperEl.querySelectorAll(params.el).length === 1) {\n      el = swiperEl.querySelector(params.el);\n    }\n    if (el.length > 0) el = el[0];\n    el.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    let dragEl;\n    if (el) {\n      dragEl = el.querySelector(`.${swiper.params.scrollbar.dragClass}`);\n      if (!dragEl) {\n        dragEl = createElement('div', swiper.params.scrollbar.dragClass);\n        el.append(dragEl);\n      }\n    }\n    Object.assign(scrollbar, {\n      el,\n      dragEl\n    });\n    if (params.draggable) {\n      enableDraggable();\n    }\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.scrollbar.lockClass);\n    }\n  }\n  function destroy() {\n    const params = swiper.params.scrollbar;\n    const el = swiper.scrollbar.el;\n    if (el) {\n      el.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    }\n    disableDraggable();\n  }\n  on('init', () => {\n    if (swiper.params.scrollbar.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      updateSize();\n      setTranslate();\n    }\n  });\n  on('update resize observerUpdate lock unlock', () => {\n    updateSize();\n  });\n  on('setTranslate', () => {\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    setTransition(duration);\n  });\n  on('enable disable', () => {\n    const {\n      el\n    } = swiper.scrollbar;\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.scrollbar.lockClass);\n    }\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.scrollbar.scrollbarDisabledClass);\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.remove(swiper.params.scrollbar.scrollbarDisabledClass);\n    }\n    init();\n    updateSize();\n    setTranslate();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.scrollbar.scrollbarDisabledClass);\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.add(swiper.params.scrollbar.scrollbarDisabledClass);\n    }\n    destroy();\n  };\n  Object.assign(swiper.scrollbar, {\n    enable,\n    disable,\n    updateSize,\n    setTranslate,\n    init,\n    destroy\n  });\n}", "import { elementChildren } from '../../shared/utils.js';\nexport default function Parallax({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    parallax: {\n      enabled: false\n    }\n  });\n  const setTransform = (el, progress) => {\n    const {\n      rtl\n    } = swiper;\n    const rtlFactor = rtl ? -1 : 1;\n    const p = el.getAttribute('data-swiper-parallax') || '0';\n    let x = el.getAttribute('data-swiper-parallax-x');\n    let y = el.getAttribute('data-swiper-parallax-y');\n    const scale = el.getAttribute('data-swiper-parallax-scale');\n    const opacity = el.getAttribute('data-swiper-parallax-opacity');\n    const rotate = el.getAttribute('data-swiper-parallax-rotate');\n    if (x || y) {\n      x = x || '0';\n      y = y || '0';\n    } else if (swiper.isHorizontal()) {\n      x = p;\n      y = '0';\n    } else {\n      y = p;\n      x = '0';\n    }\n    if (x.indexOf('%') >= 0) {\n      x = `${parseInt(x, 10) * progress * rtlFactor}%`;\n    } else {\n      x = `${x * progress * rtlFactor}px`;\n    }\n    if (y.indexOf('%') >= 0) {\n      y = `${parseInt(y, 10) * progress}%`;\n    } else {\n      y = `${y * progress}px`;\n    }\n    if (typeof opacity !== 'undefined' && opacity !== null) {\n      const currentOpacity = opacity - (opacity - 1) * (1 - Math.abs(progress));\n      el.style.opacity = currentOpacity;\n    }\n    let transform = `translate3d(${x}, ${y}, 0px)`;\n    if (typeof scale !== 'undefined' && scale !== null) {\n      const currentScale = scale - (scale - 1) * (1 - Math.abs(progress));\n      transform += ` scale(${currentScale})`;\n    }\n    if (rotate && typeof rotate !== 'undefined' && rotate !== null) {\n      const currentRotate = rotate * progress * -1;\n      transform += ` rotate(${currentRotate}deg)`;\n    }\n    el.style.transform = transform;\n  };\n  const setTranslate = () => {\n    const {\n      el,\n      slides,\n      progress,\n      snapGrid\n    } = swiper;\n    elementChildren(el, '[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]').forEach(subEl => {\n      setTransform(subEl, progress);\n    });\n    slides.forEach((slideEl, slideIndex) => {\n      let slideProgress = slideEl.progress;\n      if (swiper.params.slidesPerGroup > 1 && swiper.params.slidesPerView !== 'auto') {\n        slideProgress += Math.ceil(slideIndex / 2) - progress * (snapGrid.length - 1);\n      }\n      slideProgress = Math.min(Math.max(slideProgress, -1), 1);\n      slideEl.querySelectorAll('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale], [data-swiper-parallax-rotate]').forEach(subEl => {\n        setTransform(subEl, slideProgress);\n      });\n    });\n  };\n  const setTransition = (duration = swiper.params.speed) => {\n    const {\n      el\n    } = swiper;\n    el.querySelectorAll('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]').forEach(parallaxEl => {\n      let parallaxDuration = parseInt(parallaxEl.getAttribute('data-swiper-parallax-duration'), 10) || duration;\n      if (duration === 0) parallaxDuration = 0;\n      parallaxEl.style.transitionDuration = `${parallaxDuration}ms`;\n    });\n  };\n  on('beforeInit', () => {\n    if (!swiper.params.parallax.enabled) return;\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n  });\n  on('init', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTransition', (_swiper, duration) => {\n    if (!swiper.params.parallax.enabled) return;\n    setTransition(duration);\n  });\n}", "import { getWindow } from 'ssr-window';\nimport { elementChildren, elementOffset, elementParents, getTranslate } from '../../shared/utils.js';\nexport default function Zoom({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  const window = getWindow();\n  extendParams({\n    zoom: {\n      enabled: false,\n      maxRatio: 3,\n      minRatio: 1,\n      toggle: true,\n      containerClass: 'swiper-zoom-container',\n      zoomedSlideClass: 'swiper-slide-zoomed'\n    }\n  });\n  swiper.zoom = {\n    enabled: false\n  };\n  let currentScale = 1;\n  let isScaling = false;\n  let fakeGestureTouched;\n  let fakeGestureMoved;\n  const evCache = [];\n  const gesture = {\n    originX: 0,\n    originY: 0,\n    slideEl: undefined,\n    slideWidth: undefined,\n    slideHeight: undefined,\n    imageEl: undefined,\n    imageWrapEl: undefined,\n    maxRatio: 3\n  };\n  const image = {\n    isTouched: undefined,\n    isMoved: undefined,\n    currentX: undefined,\n    currentY: undefined,\n    minX: undefined,\n    minY: undefined,\n    maxX: undefined,\n    maxY: undefined,\n    width: undefined,\n    height: undefined,\n    startX: undefined,\n    startY: undefined,\n    touchesStart: {},\n    touchesCurrent: {}\n  };\n  const velocity = {\n    x: undefined,\n    y: undefined,\n    prevPositionX: undefined,\n    prevPositionY: undefined,\n    prevTime: undefined\n  };\n  let scale = 1;\n  Object.defineProperty(swiper.zoom, 'scale', {\n    get() {\n      return scale;\n    },\n    set(value) {\n      if (scale !== value) {\n        const imageEl = gesture.imageEl;\n        const slideEl = gesture.slideEl;\n        emit('zoomChange', value, imageEl, slideEl);\n      }\n      scale = value;\n    }\n  });\n  function getDistanceBetweenTouches() {\n    if (evCache.length < 2) return 1;\n    const x1 = evCache[0].pageX;\n    const y1 = evCache[0].pageY;\n    const x2 = evCache[1].pageX;\n    const y2 = evCache[1].pageY;\n    const distance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);\n    return distance;\n  }\n  function getScaleOrigin() {\n    if (evCache.length < 2) return {\n      x: null,\n      y: null\n    };\n    const box = gesture.imageEl.getBoundingClientRect();\n    return [(evCache[0].pageX + (evCache[1].pageX - evCache[0].pageX) / 2 - box.x) / currentScale, (evCache[0].pageY + (evCache[1].pageY - evCache[0].pageY) / 2 - box.y) / currentScale];\n  }\n  function getSlideSelector() {\n    return swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  }\n  function eventWithinSlide(e) {\n    const slideSelector = getSlideSelector();\n    if (e.target.matches(slideSelector)) return true;\n    if (swiper.slides.filter(slideEl => slideEl.contains(e.target)).length > 0) return true;\n    return false;\n  }\n  function eventWithinZoomContainer(e) {\n    const selector = `.${swiper.params.zoom.containerClass}`;\n    if (e.target.matches(selector)) return true;\n    if ([...swiper.el.querySelectorAll(selector)].filter(containerEl => containerEl.contains(e.target)).length > 0) return true;\n    return false;\n  }\n\n  // Events\n  function onGestureStart(e) {\n    if (e.pointerType === 'mouse') {\n      evCache.splice(0, evCache.length);\n    }\n    if (!eventWithinSlide(e)) return;\n    const params = swiper.params.zoom;\n    fakeGestureTouched = false;\n    fakeGestureMoved = false;\n    evCache.push(e);\n    if (evCache.length < 2) {\n      return;\n    }\n    fakeGestureTouched = true;\n    gesture.scaleStart = getDistanceBetweenTouches();\n    if (!gesture.slideEl) {\n      gesture.slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n      if (!gesture.slideEl) gesture.slideEl = swiper.slides[swiper.activeIndex];\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n      if (!gesture.imageWrapEl) {\n        gesture.imageEl = undefined;\n        return;\n      }\n      gesture.maxRatio = gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    }\n    if (gesture.imageEl) {\n      const [originX, originY] = getScaleOrigin();\n      gesture.originX = originX;\n      gesture.originY = originY;\n      gesture.imageEl.style.transitionDuration = '0ms';\n    }\n    isScaling = true;\n  }\n  function onGestureChange(e) {\n    if (!eventWithinSlide(e)) return;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const pointerIndex = evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n    if (pointerIndex >= 0) evCache[pointerIndex] = e;\n    if (evCache.length < 2) {\n      return;\n    }\n    fakeGestureMoved = true;\n    gesture.scaleMove = getDistanceBetweenTouches();\n    if (!gesture.imageEl) {\n      return;\n    }\n    zoom.scale = gesture.scaleMove / gesture.scaleStart * currentScale;\n    if (zoom.scale > gesture.maxRatio) {\n      zoom.scale = gesture.maxRatio - 1 + (zoom.scale - gesture.maxRatio + 1) ** 0.5;\n    }\n    if (zoom.scale < params.minRatio) {\n      zoom.scale = params.minRatio + 1 - (params.minRatio - zoom.scale + 1) ** 0.5;\n    }\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n  }\n  function onGestureEnd(e) {\n    if (!eventWithinSlide(e)) return;\n    if (e.pointerType === 'mouse' && e.type === 'pointerout') return;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const pointerIndex = evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n    if (pointerIndex >= 0) evCache.splice(pointerIndex, 1);\n    if (!fakeGestureTouched || !fakeGestureMoved) {\n      return;\n    }\n    fakeGestureTouched = false;\n    fakeGestureMoved = false;\n    if (!gesture.imageEl) return;\n    zoom.scale = Math.max(Math.min(zoom.scale, gesture.maxRatio), params.minRatio);\n    gesture.imageEl.style.transitionDuration = `${swiper.params.speed}ms`;\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n    currentScale = zoom.scale;\n    isScaling = false;\n    if (zoom.scale > 1 && gesture.slideEl) {\n      gesture.slideEl.classList.add(`${params.zoomedSlideClass}`);\n    } else if (zoom.scale <= 1 && gesture.slideEl) {\n      gesture.slideEl.classList.remove(`${params.zoomedSlideClass}`);\n    }\n    if (zoom.scale === 1) {\n      gesture.originX = 0;\n      gesture.originY = 0;\n      gesture.slideEl = undefined;\n    }\n  }\n  function onTouchStart(e) {\n    const device = swiper.device;\n    if (!gesture.imageEl) return;\n    if (image.isTouched) return;\n    if (device.android && e.cancelable) e.preventDefault();\n    image.isTouched = true;\n    const event = evCache.length > 0 ? evCache[0] : e;\n    image.touchesStart.x = event.pageX;\n    image.touchesStart.y = event.pageY;\n  }\n  function onTouchMove(e) {\n    if (!eventWithinSlide(e) || !eventWithinZoomContainer(e)) return;\n    const zoom = swiper.zoom;\n    if (!gesture.imageEl) return;\n    if (!image.isTouched || !gesture.slideEl) return;\n    if (!image.isMoved) {\n      image.width = gesture.imageEl.offsetWidth;\n      image.height = gesture.imageEl.offsetHeight;\n      image.startX = getTranslate(gesture.imageWrapEl, 'x') || 0;\n      image.startY = getTranslate(gesture.imageWrapEl, 'y') || 0;\n      gesture.slideWidth = gesture.slideEl.offsetWidth;\n      gesture.slideHeight = gesture.slideEl.offsetHeight;\n      gesture.imageWrapEl.style.transitionDuration = '0ms';\n    }\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n    if (scaledWidth < gesture.slideWidth && scaledHeight < gesture.slideHeight) return;\n    image.minX = Math.min(gesture.slideWidth / 2 - scaledWidth / 2, 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(gesture.slideHeight / 2 - scaledHeight / 2, 0);\n    image.maxY = -image.minY;\n    image.touchesCurrent.x = evCache.length > 0 ? evCache[0].pageX : e.pageX;\n    image.touchesCurrent.y = evCache.length > 0 ? evCache[0].pageY : e.pageY;\n    const touchesDiff = Math.max(Math.abs(image.touchesCurrent.x - image.touchesStart.x), Math.abs(image.touchesCurrent.y - image.touchesStart.y));\n    if (touchesDiff > 5) {\n      swiper.allowClick = false;\n    }\n    if (!image.isMoved && !isScaling) {\n      if (swiper.isHorizontal() && (Math.floor(image.minX) === Math.floor(image.startX) && image.touchesCurrent.x < image.touchesStart.x || Math.floor(image.maxX) === Math.floor(image.startX) && image.touchesCurrent.x > image.touchesStart.x)) {\n        image.isTouched = false;\n        return;\n      }\n      if (!swiper.isHorizontal() && (Math.floor(image.minY) === Math.floor(image.startY) && image.touchesCurrent.y < image.touchesStart.y || Math.floor(image.maxY) === Math.floor(image.startY) && image.touchesCurrent.y > image.touchesStart.y)) {\n        image.isTouched = false;\n        return;\n      }\n    }\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n    e.stopPropagation();\n    image.isMoved = true;\n    const scaleRatio = (zoom.scale - currentScale) / (gesture.maxRatio - swiper.params.zoom.minRatio);\n    const {\n      originX,\n      originY\n    } = gesture;\n    image.currentX = image.touchesCurrent.x - image.touchesStart.x + image.startX + scaleRatio * (image.width - originX * 2);\n    image.currentY = image.touchesCurrent.y - image.touchesStart.y + image.startY + scaleRatio * (image.height - originY * 2);\n    if (image.currentX < image.minX) {\n      image.currentX = image.minX + 1 - (image.minX - image.currentX + 1) ** 0.8;\n    }\n    if (image.currentX > image.maxX) {\n      image.currentX = image.maxX - 1 + (image.currentX - image.maxX + 1) ** 0.8;\n    }\n    if (image.currentY < image.minY) {\n      image.currentY = image.minY + 1 - (image.minY - image.currentY + 1) ** 0.8;\n    }\n    if (image.currentY > image.maxY) {\n      image.currentY = image.maxY - 1 + (image.currentY - image.maxY + 1) ** 0.8;\n    }\n\n    // Velocity\n    if (!velocity.prevPositionX) velocity.prevPositionX = image.touchesCurrent.x;\n    if (!velocity.prevPositionY) velocity.prevPositionY = image.touchesCurrent.y;\n    if (!velocity.prevTime) velocity.prevTime = Date.now();\n    velocity.x = (image.touchesCurrent.x - velocity.prevPositionX) / (Date.now() - velocity.prevTime) / 2;\n    velocity.y = (image.touchesCurrent.y - velocity.prevPositionY) / (Date.now() - velocity.prevTime) / 2;\n    if (Math.abs(image.touchesCurrent.x - velocity.prevPositionX) < 2) velocity.x = 0;\n    if (Math.abs(image.touchesCurrent.y - velocity.prevPositionY) < 2) velocity.y = 0;\n    velocity.prevPositionX = image.touchesCurrent.x;\n    velocity.prevPositionY = image.touchesCurrent.y;\n    velocity.prevTime = Date.now();\n    gesture.imageWrapEl.style.transform = `translate3d(${image.currentX}px, ${image.currentY}px,0)`;\n  }\n  function onTouchEnd() {\n    const zoom = swiper.zoom;\n    if (!gesture.imageEl) return;\n    if (!image.isTouched || !image.isMoved) {\n      image.isTouched = false;\n      image.isMoved = false;\n      return;\n    }\n    image.isTouched = false;\n    image.isMoved = false;\n    let momentumDurationX = 300;\n    let momentumDurationY = 300;\n    const momentumDistanceX = velocity.x * momentumDurationX;\n    const newPositionX = image.currentX + momentumDistanceX;\n    const momentumDistanceY = velocity.y * momentumDurationY;\n    const newPositionY = image.currentY + momentumDistanceY;\n\n    // Fix duration\n    if (velocity.x !== 0) momentumDurationX = Math.abs((newPositionX - image.currentX) / velocity.x);\n    if (velocity.y !== 0) momentumDurationY = Math.abs((newPositionY - image.currentY) / velocity.y);\n    const momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n    image.currentX = newPositionX;\n    image.currentY = newPositionY;\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n    image.minX = Math.min(gesture.slideWidth / 2 - scaledWidth / 2, 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(gesture.slideHeight / 2 - scaledHeight / 2, 0);\n    image.maxY = -image.minY;\n    image.currentX = Math.max(Math.min(image.currentX, image.maxX), image.minX);\n    image.currentY = Math.max(Math.min(image.currentY, image.maxY), image.minY);\n    gesture.imageWrapEl.style.transitionDuration = `${momentumDuration}ms`;\n    gesture.imageWrapEl.style.transform = `translate3d(${image.currentX}px, ${image.currentY}px,0)`;\n  }\n  function onTransitionEnd() {\n    const zoom = swiper.zoom;\n    if (gesture.slideEl && swiper.activeIndex !== swiper.slides.indexOf(gesture.slideEl)) {\n      if (gesture.imageEl) {\n        gesture.imageEl.style.transform = 'translate3d(0,0,0) scale(1)';\n      }\n      if (gesture.imageWrapEl) {\n        gesture.imageWrapEl.style.transform = 'translate3d(0,0,0)';\n      }\n      gesture.slideEl.classList.remove(`${swiper.params.zoom.zoomedSlideClass}`);\n      zoom.scale = 1;\n      currentScale = 1;\n      gesture.slideEl = undefined;\n      gesture.imageEl = undefined;\n      gesture.imageWrapEl = undefined;\n      gesture.originX = 0;\n      gesture.originY = 0;\n    }\n  }\n  function zoomIn(e) {\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n    if (!gesture.slideEl) {\n      if (e && e.target) {\n        gesture.slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n      }\n      if (!gesture.slideEl) {\n        if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n          gesture.slideEl = elementChildren(swiper.slidesEl, `.${swiper.params.slideActiveClass}`)[0];\n        } else {\n          gesture.slideEl = swiper.slides[swiper.activeIndex];\n        }\n      }\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n    }\n    if (!gesture.imageEl || !gesture.imageWrapEl) return;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.touchAction = 'none';\n    }\n    gesture.slideEl.classList.add(`${params.zoomedSlideClass}`);\n    let touchX;\n    let touchY;\n    let offsetX;\n    let offsetY;\n    let diffX;\n    let diffY;\n    let translateX;\n    let translateY;\n    let imageWidth;\n    let imageHeight;\n    let scaledWidth;\n    let scaledHeight;\n    let translateMinX;\n    let translateMinY;\n    let translateMaxX;\n    let translateMaxY;\n    let slideWidth;\n    let slideHeight;\n    if (typeof image.touchesStart.x === 'undefined' && e) {\n      touchX = e.pageX;\n      touchY = e.pageY;\n    } else {\n      touchX = image.touchesStart.x;\n      touchY = image.touchesStart.y;\n    }\n    const forceZoomRatio = typeof e === 'number' ? e : null;\n    if (currentScale === 1 && forceZoomRatio) {\n      touchX = undefined;\n      touchY = undefined;\n    }\n    zoom.scale = forceZoomRatio || gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    currentScale = forceZoomRatio || gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    if (e && !(currentScale === 1 && forceZoomRatio)) {\n      slideWidth = gesture.slideEl.offsetWidth;\n      slideHeight = gesture.slideEl.offsetHeight;\n      offsetX = elementOffset(gesture.slideEl).left + window.scrollX;\n      offsetY = elementOffset(gesture.slideEl).top + window.scrollY;\n      diffX = offsetX + slideWidth / 2 - touchX;\n      diffY = offsetY + slideHeight / 2 - touchY;\n      imageWidth = gesture.imageEl.offsetWidth;\n      imageHeight = gesture.imageEl.offsetHeight;\n      scaledWidth = imageWidth * zoom.scale;\n      scaledHeight = imageHeight * zoom.scale;\n      translateMinX = Math.min(slideWidth / 2 - scaledWidth / 2, 0);\n      translateMinY = Math.min(slideHeight / 2 - scaledHeight / 2, 0);\n      translateMaxX = -translateMinX;\n      translateMaxY = -translateMinY;\n      translateX = diffX * zoom.scale;\n      translateY = diffY * zoom.scale;\n      if (translateX < translateMinX) {\n        translateX = translateMinX;\n      }\n      if (translateX > translateMaxX) {\n        translateX = translateMaxX;\n      }\n      if (translateY < translateMinY) {\n        translateY = translateMinY;\n      }\n      if (translateY > translateMaxY) {\n        translateY = translateMaxY;\n      }\n    } else {\n      translateX = 0;\n      translateY = 0;\n    }\n    if (forceZoomRatio && zoom.scale === 1) {\n      gesture.originX = 0;\n      gesture.originY = 0;\n    }\n    gesture.imageWrapEl.style.transitionDuration = '300ms';\n    gesture.imageWrapEl.style.transform = `translate3d(${translateX}px, ${translateY}px,0)`;\n    gesture.imageEl.style.transitionDuration = '300ms';\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n  }\n  function zoomOut() {\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n    if (!gesture.slideEl) {\n      if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n        gesture.slideEl = elementChildren(swiper.slidesEl, `.${swiper.params.slideActiveClass}`)[0];\n      } else {\n        gesture.slideEl = swiper.slides[swiper.activeIndex];\n      }\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n    }\n    if (!gesture.imageEl || !gesture.imageWrapEl) return;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style.overflow = '';\n      swiper.wrapperEl.style.touchAction = '';\n    }\n    zoom.scale = 1;\n    currentScale = 1;\n    gesture.imageWrapEl.style.transitionDuration = '300ms';\n    gesture.imageWrapEl.style.transform = 'translate3d(0,0,0)';\n    gesture.imageEl.style.transitionDuration = '300ms';\n    gesture.imageEl.style.transform = 'translate3d(0,0,0) scale(1)';\n    gesture.slideEl.classList.remove(`${params.zoomedSlideClass}`);\n    gesture.slideEl = undefined;\n    gesture.originX = 0;\n    gesture.originY = 0;\n  }\n\n  // Toggle Zoom\n  function zoomToggle(e) {\n    const zoom = swiper.zoom;\n    if (zoom.scale && zoom.scale !== 1) {\n      // Zoom Out\n      zoomOut();\n    } else {\n      // Zoom In\n      zoomIn(e);\n    }\n  }\n  function getListeners() {\n    const passiveListener = swiper.params.passiveListeners ? {\n      passive: true,\n      capture: false\n    } : false;\n    const activeListenerWithCapture = swiper.params.passiveListeners ? {\n      passive: false,\n      capture: true\n    } : true;\n    return {\n      passiveListener,\n      activeListenerWithCapture\n    };\n  }\n\n  // Attach/Detach Events\n  function enable() {\n    const zoom = swiper.zoom;\n    if (zoom.enabled) return;\n    zoom.enabled = true;\n    const {\n      passiveListener,\n      activeListenerWithCapture\n    } = getListeners();\n\n    // Scale image\n    swiper.wrapperEl.addEventListener('pointerdown', onGestureStart, passiveListener);\n    swiper.wrapperEl.addEventListener('pointermove', onGestureChange, activeListenerWithCapture);\n    ['pointerup', 'pointercancel', 'pointerout'].forEach(eventName => {\n      swiper.wrapperEl.addEventListener(eventName, onGestureEnd, passiveListener);\n    });\n\n    // Move image\n    swiper.wrapperEl.addEventListener('pointermove', onTouchMove, activeListenerWithCapture);\n  }\n  function disable() {\n    const zoom = swiper.zoom;\n    if (!zoom.enabled) return;\n    zoom.enabled = false;\n    const {\n      passiveListener,\n      activeListenerWithCapture\n    } = getListeners();\n\n    // Scale image\n    swiper.wrapperEl.removeEventListener('pointerdown', onGestureStart, passiveListener);\n    swiper.wrapperEl.removeEventListener('pointermove', onGestureChange, activeListenerWithCapture);\n    ['pointerup', 'pointercancel', 'pointerout'].forEach(eventName => {\n      swiper.wrapperEl.removeEventListener(eventName, onGestureEnd, passiveListener);\n    });\n\n    // Move image\n    swiper.wrapperEl.removeEventListener('pointermove', onTouchMove, activeListenerWithCapture);\n  }\n  on('init', () => {\n    if (swiper.params.zoom.enabled) {\n      enable();\n    }\n  });\n  on('destroy', () => {\n    disable();\n  });\n  on('touchStart', (_s, e) => {\n    if (!swiper.zoom.enabled) return;\n    onTouchStart(e);\n  });\n  on('touchEnd', (_s, e) => {\n    if (!swiper.zoom.enabled) return;\n    onTouchEnd(e);\n  });\n  on('doubleTap', (_s, e) => {\n    if (!swiper.animating && swiper.params.zoom.enabled && swiper.zoom.enabled && swiper.params.zoom.toggle) {\n      zoomToggle(e);\n    }\n  });\n  on('transitionEnd', () => {\n    if (swiper.zoom.enabled && swiper.params.zoom.enabled) {\n      onTransitionEnd();\n    }\n  });\n  on('slideChange', () => {\n    if (swiper.zoom.enabled && swiper.params.zoom.enabled && swiper.params.cssMode) {\n      onTransitionEnd();\n    }\n  });\n  Object.assign(swiper.zoom, {\n    enable,\n    disable,\n    in: zoomIn,\n    out: zoomOut,\n    toggle: zoomToggle\n  });\n}", "/* eslint no-bitwise: [\"error\", { \"allow\": [\">>\"] }] */\nimport { elementTransitionEnd, nextTick } from '../../shared/utils.js';\nexport default function Controller({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    controller: {\n      control: undefined,\n      inverse: false,\n      by: 'slide' // or 'container'\n    }\n  });\n\n  swiper.controller = {\n    control: undefined\n  };\n  function LinearSpline(x, y) {\n    const binarySearch = function search() {\n      let maxIndex;\n      let minIndex;\n      let guess;\n      return (array, val) => {\n        minIndex = -1;\n        maxIndex = array.length;\n        while (maxIndex - minIndex > 1) {\n          guess = maxIndex + minIndex >> 1;\n          if (array[guess] <= val) {\n            minIndex = guess;\n          } else {\n            maxIndex = guess;\n          }\n        }\n        return maxIndex;\n      };\n    }();\n    this.x = x;\n    this.y = y;\n    this.lastIndex = x.length - 1;\n    // Given an x value (x2), return the expected y2 value:\n    // (x1,y1) is the known point before given value,\n    // (x3,y3) is the known point after given value.\n    let i1;\n    let i3;\n    this.interpolate = function interpolate(x2) {\n      if (!x2) return 0;\n\n      // Get the indexes of x1 and x3 (the array indexes before and after given x2):\n      i3 = binarySearch(this.x, x2);\n      i1 = i3 - 1;\n\n      // We have our indexes i1 & i3, so we can calculate already:\n      // y2 := ((x2−x1) × (y3−y1)) ÷ (x3−x1) + y1\n      return (x2 - this.x[i1]) * (this.y[i3] - this.y[i1]) / (this.x[i3] - this.x[i1]) + this.y[i1];\n    };\n    return this;\n  }\n  // xxx: for now i will just save one spline function to to\n  function getInterpolateFunction(c) {\n    if (!swiper.controller.spline) {\n      swiper.controller.spline = swiper.params.loop ? new LinearSpline(swiper.slidesGrid, c.slidesGrid) : new LinearSpline(swiper.snapGrid, c.snapGrid);\n    }\n  }\n  function setTranslate(_t, byController) {\n    const controlled = swiper.controller.control;\n    let multiplier;\n    let controlledTranslate;\n    const Swiper = swiper.constructor;\n    function setControlledTranslate(c) {\n      if (c.destroyed) return;\n\n      // this will create an Interpolate function based on the snapGrids\n      // x is the Grid of the scrolled scroller and y will be the controlled scroller\n      // it makes sense to create this only once and recall it for the interpolation\n      // the function does a lot of value caching for performance\n      const translate = swiper.rtlTranslate ? -swiper.translate : swiper.translate;\n      if (swiper.params.controller.by === 'slide') {\n        getInterpolateFunction(c);\n        // i am not sure why the values have to be multiplicated this way, tried to invert the snapGrid\n        // but it did not work out\n        controlledTranslate = -swiper.controller.spline.interpolate(-translate);\n      }\n      if (!controlledTranslate || swiper.params.controller.by === 'container') {\n        multiplier = (c.maxTranslate() - c.minTranslate()) / (swiper.maxTranslate() - swiper.minTranslate());\n        controlledTranslate = (translate - swiper.minTranslate()) * multiplier + c.minTranslate();\n      }\n      if (swiper.params.controller.inverse) {\n        controlledTranslate = c.maxTranslate() - controlledTranslate;\n      }\n      c.updateProgress(controlledTranslate);\n      c.setTranslate(controlledTranslate, swiper);\n      c.updateActiveIndex();\n      c.updateSlidesClasses();\n    }\n    if (Array.isArray(controlled)) {\n      for (let i = 0; i < controlled.length; i += 1) {\n        if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n          setControlledTranslate(controlled[i]);\n        }\n      }\n    } else if (controlled instanceof Swiper && byController !== controlled) {\n      setControlledTranslate(controlled);\n    }\n  }\n  function setTransition(duration, byController) {\n    const Swiper = swiper.constructor;\n    const controlled = swiper.controller.control;\n    let i;\n    function setControlledTransition(c) {\n      if (c.destroyed) return;\n      c.setTransition(duration, swiper);\n      if (duration !== 0) {\n        c.transitionStart();\n        if (c.params.autoHeight) {\n          nextTick(() => {\n            c.updateAutoHeight();\n          });\n        }\n        elementTransitionEnd(c.wrapperEl, () => {\n          if (!controlled) return;\n          c.transitionEnd();\n        });\n      }\n    }\n    if (Array.isArray(controlled)) {\n      for (i = 0; i < controlled.length; i += 1) {\n        if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n          setControlledTransition(controlled[i]);\n        }\n      }\n    } else if (controlled instanceof Swiper && byController !== controlled) {\n      setControlledTransition(controlled);\n    }\n  }\n  function removeSpline() {\n    if (!swiper.controller.control) return;\n    if (swiper.controller.spline) {\n      swiper.controller.spline = undefined;\n      delete swiper.controller.spline;\n    }\n  }\n  on('beforeInit', () => {\n    if (typeof window !== 'undefined' && (\n    // eslint-disable-line\n    typeof swiper.params.controller.control === 'string' || swiper.params.controller.control instanceof HTMLElement)) {\n      const controlElement = document.querySelector(swiper.params.controller.control);\n      if (controlElement && controlElement.swiper) {\n        swiper.controller.control = controlElement.swiper;\n      } else if (controlElement) {\n        const onControllerSwiper = e => {\n          swiper.controller.control = e.detail[0];\n          swiper.update();\n          controlElement.removeEventListener('init', onControllerSwiper);\n        };\n        controlElement.addEventListener('init', onControllerSwiper);\n      }\n      return;\n    }\n    swiper.controller.control = swiper.params.controller.control;\n  });\n  on('update', () => {\n    removeSpline();\n  });\n  on('resize', () => {\n    removeSpline();\n  });\n  on('observerUpdate', () => {\n    removeSpline();\n  });\n  on('setTranslate', (_s, translate, byController) => {\n    if (!swiper.controller.control) return;\n    swiper.controller.setTranslate(translate, byController);\n  });\n  on('setTransition', (_s, duration, byController) => {\n    if (!swiper.controller.control) return;\n    swiper.controller.setTransition(duration, byController);\n  });\n  Object.assign(swiper.controller, {\n    setTranslate,\n    setTransition\n  });\n}", "import classesToSelector from '../../shared/classes-to-selector.js';\nimport { createElement, elementIndex } from '../../shared/utils.js';\nexport default function A11y({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    a11y: {\n      enabled: true,\n      notificationClass: 'swiper-notification',\n      prevSlideMessage: 'Previous slide',\n      nextSlideMessage: 'Next slide',\n      firstSlideMessage: 'This is the first slide',\n      lastSlideMessage: 'This is the last slide',\n      paginationBulletMessage: 'Go to slide {{index}}',\n      slideLabelMessage: '{{index}} / {{slidesLength}}',\n      containerMessage: null,\n      containerRoleDescriptionMessage: null,\n      itemRoleDescriptionMessage: null,\n      slideRole: 'group',\n      id: null\n    }\n  });\n  swiper.a11y = {\n    clicked: false\n  };\n  let liveRegion = null;\n  function notify(message) {\n    const notification = liveRegion;\n    if (notification.length === 0) return;\n    notification.innerHTML = '';\n    notification.innerHTML = message;\n  }\n  const makeElementsArray = el => {\n    if (!Array.isArray(el)) el = [el].filter(e => !!e);\n    return el;\n  };\n  function getRandomNumber(size = 16) {\n    const randomChar = () => Math.round(16 * Math.random()).toString(16);\n    return 'x'.repeat(size).replace(/x/g, randomChar);\n  }\n  function makeElFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '0');\n    });\n  }\n  function makeElNotFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '-1');\n    });\n  }\n  function addElRole(el, role) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('role', role);\n    });\n  }\n  function addElRoleDescription(el, description) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-roledescription', description);\n    });\n  }\n  function addElControls(el, controls) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-controls', controls);\n    });\n  }\n  function addElLabel(el, label) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-label', label);\n    });\n  }\n  function addElId(el, id) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('id', id);\n    });\n  }\n  function addElLive(el, live) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-live', live);\n    });\n  }\n  function disableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', true);\n    });\n  }\n  function enableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', false);\n    });\n  }\n  function onEnterOrSpaceKey(e) {\n    if (e.keyCode !== 13 && e.keyCode !== 32) return;\n    const params = swiper.params.a11y;\n    const targetEl = e.target;\n    if (swiper.pagination && swiper.pagination.el && (targetEl === swiper.pagination.el || swiper.pagination.el.contains(e.target))) {\n      if (!e.target.matches(classesToSelector(swiper.params.pagination.bulletClass))) return;\n    }\n    if (swiper.navigation && swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl) {\n      if (!(swiper.isEnd && !swiper.params.loop)) {\n        swiper.slideNext();\n      }\n      if (swiper.isEnd) {\n        notify(params.lastSlideMessage);\n      } else {\n        notify(params.nextSlideMessage);\n      }\n    }\n    if (swiper.navigation && swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl) {\n      if (!(swiper.isBeginning && !swiper.params.loop)) {\n        swiper.slidePrev();\n      }\n      if (swiper.isBeginning) {\n        notify(params.firstSlideMessage);\n      } else {\n        notify(params.prevSlideMessage);\n      }\n    }\n    if (swiper.pagination && targetEl.matches(classesToSelector(swiper.params.pagination.bulletClass))) {\n      targetEl.click();\n    }\n  }\n  function updateNavigation() {\n    if (swiper.params.loop || swiper.params.rewind || !swiper.navigation) return;\n    const {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    if (prevEl) {\n      if (swiper.isBeginning) {\n        disableEl(prevEl);\n        makeElNotFocusable(prevEl);\n      } else {\n        enableEl(prevEl);\n        makeElFocusable(prevEl);\n      }\n    }\n    if (nextEl) {\n      if (swiper.isEnd) {\n        disableEl(nextEl);\n        makeElNotFocusable(nextEl);\n      } else {\n        enableEl(nextEl);\n        makeElFocusable(nextEl);\n      }\n    }\n  }\n  function hasPagination() {\n    return swiper.pagination && swiper.pagination.bullets && swiper.pagination.bullets.length;\n  }\n  function hasClickablePagination() {\n    return hasPagination() && swiper.params.pagination.clickable;\n  }\n  function updatePagination() {\n    const params = swiper.params.a11y;\n    if (!hasPagination()) return;\n    swiper.pagination.bullets.forEach(bulletEl => {\n      if (swiper.params.pagination.clickable) {\n        makeElFocusable(bulletEl);\n        if (!swiper.params.pagination.renderBullet) {\n          addElRole(bulletEl, 'button');\n          addElLabel(bulletEl, params.paginationBulletMessage.replace(/\\{\\{index\\}\\}/, elementIndex(bulletEl) + 1));\n        }\n      }\n      if (bulletEl.matches(classesToSelector(swiper.params.pagination.bulletActiveClass))) {\n        bulletEl.setAttribute('aria-current', 'true');\n      } else {\n        bulletEl.removeAttribute('aria-current');\n      }\n    });\n  }\n  const initNavEl = (el, wrapperId, message) => {\n    makeElFocusable(el);\n    if (el.tagName !== 'BUTTON') {\n      addElRole(el, 'button');\n      el.addEventListener('keydown', onEnterOrSpaceKey);\n    }\n    addElLabel(el, message);\n    addElControls(el, wrapperId);\n  };\n  const handlePointerDown = () => {\n    swiper.a11y.clicked = true;\n  };\n  const handlePointerUp = () => {\n    requestAnimationFrame(() => {\n      requestAnimationFrame(() => {\n        if (!swiper.destroyed) {\n          swiper.a11y.clicked = false;\n        }\n      });\n    });\n  };\n  const handleFocus = e => {\n    if (swiper.a11y.clicked) return;\n    const slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n    if (!slideEl || !swiper.slides.includes(slideEl)) return;\n    const isActive = swiper.slides.indexOf(slideEl) === swiper.activeIndex;\n    const isVisible = swiper.params.watchSlidesProgress && swiper.visibleSlides && swiper.visibleSlides.includes(slideEl);\n    if (isActive || isVisible) return;\n    if (e.sourceCapabilities && e.sourceCapabilities.firesTouchEvents) return;\n    if (swiper.isHorizontal()) {\n      swiper.el.scrollLeft = 0;\n    } else {\n      swiper.el.scrollTop = 0;\n    }\n    swiper.slideTo(swiper.slides.indexOf(slideEl), 0);\n  };\n  const initSlides = () => {\n    const params = swiper.params.a11y;\n    if (params.itemRoleDescriptionMessage) {\n      addElRoleDescription(swiper.slides, params.itemRoleDescriptionMessage);\n    }\n    if (params.slideRole) {\n      addElRole(swiper.slides, params.slideRole);\n    }\n    const slidesLength = swiper.slides.length;\n    if (params.slideLabelMessage) {\n      swiper.slides.forEach((slideEl, index) => {\n        const slideIndex = swiper.params.loop ? parseInt(slideEl.getAttribute('data-swiper-slide-index'), 10) : index;\n        const ariaLabelMessage = params.slideLabelMessage.replace(/\\{\\{index\\}\\}/, slideIndex + 1).replace(/\\{\\{slidesLength\\}\\}/, slidesLength);\n        addElLabel(slideEl, ariaLabelMessage);\n      });\n    }\n  };\n  const init = () => {\n    const params = swiper.params.a11y;\n    swiper.el.append(liveRegion);\n\n    // Container\n    const containerEl = swiper.el;\n    if (params.containerRoleDescriptionMessage) {\n      addElRoleDescription(containerEl, params.containerRoleDescriptionMessage);\n    }\n    if (params.containerMessage) {\n      addElLabel(containerEl, params.containerMessage);\n    }\n\n    // Wrapper\n    const wrapperEl = swiper.wrapperEl;\n    const wrapperId = params.id || wrapperEl.getAttribute('id') || `swiper-wrapper-${getRandomNumber(16)}`;\n    const live = swiper.params.autoplay && swiper.params.autoplay.enabled ? 'off' : 'polite';\n    addElId(wrapperEl, wrapperId);\n    addElLive(wrapperEl, live);\n\n    // Slide\n    initSlides();\n\n    // Navigation\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => initNavEl(el, wrapperId, params.nextSlideMessage));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => initNavEl(el, wrapperId, params.prevSlideMessage));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = Array.isArray(swiper.pagination.el) ? swiper.pagination.el : [swiper.pagination.el];\n      paginationEl.forEach(el => {\n        el.addEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n\n    // Tab focus\n    swiper.el.addEventListener('focus', handleFocus, true);\n    swiper.el.addEventListener('pointerdown', handlePointerDown, true);\n    swiper.el.addEventListener('pointerup', handlePointerUp, true);\n  };\n  function destroy() {\n    if (liveRegion && liveRegion.length > 0) liveRegion.remove();\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = Array.isArray(swiper.pagination.el) ? swiper.pagination.el : [swiper.pagination.el];\n      paginationEl.forEach(el => {\n        el.removeEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n\n    // Tab focus\n    swiper.el.removeEventListener('focus', handleFocus, true);\n    swiper.el.removeEventListener('pointerdown', handlePointerDown, true);\n    swiper.el.removeEventListener('pointerup', handlePointerUp, true);\n  }\n  on('beforeInit', () => {\n    liveRegion = createElement('span', swiper.params.a11y.notificationClass);\n    liveRegion.setAttribute('aria-live', 'assertive');\n    liveRegion.setAttribute('aria-atomic', 'true');\n    if (swiper.isElement) {\n      liveRegion.setAttribute('slot', 'container-end');\n    }\n  });\n  on('afterInit', () => {\n    if (!swiper.params.a11y.enabled) return;\n    init();\n  });\n  on('slidesLengthChange snapGridLengthChange slidesGridLengthChange', () => {\n    if (!swiper.params.a11y.enabled) return;\n    initSlides();\n  });\n  on('fromEdge toEdge afterInit lock unlock', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updateNavigation();\n  });\n  on('paginationUpdate', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updatePagination();\n  });\n  on('destroy', () => {\n    if (!swiper.params.a11y.enabled) return;\n    destroy();\n  });\n}", "import { getWindow } from 'ssr-window';\nexport default function History({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    history: {\n      enabled: false,\n      root: '',\n      replaceState: false,\n      key: 'slides',\n      keepQuery: false\n    }\n  });\n  let initialized = false;\n  let paths = {};\n  const slugify = text => {\n    return text.toString().replace(/\\s+/g, '-').replace(/[^\\w-]+/g, '').replace(/--+/g, '-').replace(/^-+/, '').replace(/-+$/, '');\n  };\n  const getPathValues = urlOverride => {\n    const window = getWindow();\n    let location;\n    if (urlOverride) {\n      location = new URL(urlOverride);\n    } else {\n      location = window.location;\n    }\n    const pathArray = location.pathname.slice(1).split('/').filter(part => part !== '');\n    const total = pathArray.length;\n    const key = pathArray[total - 2];\n    const value = pathArray[total - 1];\n    return {\n      key,\n      value\n    };\n  };\n  const setHistory = (key, index) => {\n    const window = getWindow();\n    if (!initialized || !swiper.params.history.enabled) return;\n    let location;\n    if (swiper.params.url) {\n      location = new URL(swiper.params.url);\n    } else {\n      location = window.location;\n    }\n    const slide = swiper.slides[index];\n    let value = slugify(slide.getAttribute('data-history'));\n    if (swiper.params.history.root.length > 0) {\n      let root = swiper.params.history.root;\n      if (root[root.length - 1] === '/') root = root.slice(0, root.length - 1);\n      value = `${root}/${key ? `${key}/` : ''}${value}`;\n    } else if (!location.pathname.includes(key)) {\n      value = `${key ? `${key}/` : ''}${value}`;\n    }\n    if (swiper.params.history.keepQuery) {\n      value += location.search;\n    }\n    const currentState = window.history.state;\n    if (currentState && currentState.value === value) {\n      return;\n    }\n    if (swiper.params.history.replaceState) {\n      window.history.replaceState({\n        value\n      }, null, value);\n    } else {\n      window.history.pushState({\n        value\n      }, null, value);\n    }\n  };\n  const scrollToSlide = (speed, value, runCallbacks) => {\n    if (value) {\n      for (let i = 0, length = swiper.slides.length; i < length; i += 1) {\n        const slide = swiper.slides[i];\n        const slideHistory = slugify(slide.getAttribute('data-history'));\n        if (slideHistory === value) {\n          const index = swiper.getSlideIndex(slide);\n          swiper.slideTo(index, speed, runCallbacks);\n        }\n      }\n    } else {\n      swiper.slideTo(0, speed, runCallbacks);\n    }\n  };\n  const setHistoryPopState = () => {\n    paths = getPathValues(swiper.params.url);\n    scrollToSlide(swiper.params.speed, paths.value, false);\n  };\n  const init = () => {\n    const window = getWindow();\n    if (!swiper.params.history) return;\n    if (!window.history || !window.history.pushState) {\n      swiper.params.history.enabled = false;\n      swiper.params.hashNavigation.enabled = true;\n      return;\n    }\n    initialized = true;\n    paths = getPathValues(swiper.params.url);\n    if (!paths.key && !paths.value) {\n      if (!swiper.params.history.replaceState) {\n        window.addEventListener('popstate', setHistoryPopState);\n      }\n      return;\n    }\n    scrollToSlide(0, paths.value, swiper.params.runCallbacksOnInit);\n    if (!swiper.params.history.replaceState) {\n      window.addEventListener('popstate', setHistoryPopState);\n    }\n  };\n  const destroy = () => {\n    const window = getWindow();\n    if (!swiper.params.history.replaceState) {\n      window.removeEventListener('popstate', setHistoryPopState);\n    }\n  };\n  on('init', () => {\n    if (swiper.params.history.enabled) {\n      init();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.params.history.enabled) {\n      destroy();\n    }\n  });\n  on('transitionEnd _freeModeNoMomentumRelease', () => {\n    if (initialized) {\n      setHistory(swiper.params.history.key, swiper.activeIndex);\n    }\n  });\n  on('slideChange', () => {\n    if (initialized && swiper.params.cssMode) {\n      setHistory(swiper.params.history.key, swiper.activeIndex);\n    }\n  });\n}", "import { getWindow, getDocument } from 'ssr-window';\nimport { elementChildren } from '../../shared/utils.js';\nexport default function HashNavigation({\n  swiper,\n  extendParams,\n  emit,\n  on\n}) {\n  let initialized = false;\n  const document = getDocument();\n  const window = getWindow();\n  extendParams({\n    hashNavigation: {\n      enabled: false,\n      replaceState: false,\n      watchState: false\n    }\n  });\n  const onHashChange = () => {\n    emit('hashChange');\n    const newHash = document.location.hash.replace('#', '');\n    const activeSlideHash = swiper.slides[swiper.activeIndex].getAttribute('data-hash');\n    if (newHash !== activeSlideHash) {\n      const newIndex = swiper.getSlideIndex(elementChildren(swiper.slidesEl, `.${swiper.params.slideClass}[data-hash=\"${newHash}\"], swiper-slide[data-hash=\"${newHash}\"]`)[0]);\n      if (typeof newIndex === 'undefined') return;\n      swiper.slideTo(newIndex);\n    }\n  };\n  const setHash = () => {\n    if (!initialized || !swiper.params.hashNavigation.enabled) return;\n    if (swiper.params.hashNavigation.replaceState && window.history && window.history.replaceState) {\n      window.history.replaceState(null, null, `#${swiper.slides[swiper.activeIndex].getAttribute('data-hash')}` || '');\n      emit('hashSet');\n    } else {\n      const slide = swiper.slides[swiper.activeIndex];\n      const hash = slide.getAttribute('data-hash') || slide.getAttribute('data-history');\n      document.location.hash = hash || '';\n      emit('hashSet');\n    }\n  };\n  const init = () => {\n    if (!swiper.params.hashNavigation.enabled || swiper.params.history && swiper.params.history.enabled) return;\n    initialized = true;\n    const hash = document.location.hash.replace('#', '');\n    if (hash) {\n      const speed = 0;\n      for (let i = 0, length = swiper.slides.length; i < length; i += 1) {\n        const slide = swiper.slides[i];\n        const slideHash = slide.getAttribute('data-hash') || slide.getAttribute('data-history');\n        if (slideHash === hash) {\n          const index = swiper.getSlideIndex(slide);\n          swiper.slideTo(index, speed, swiper.params.runCallbacksOnInit, true);\n        }\n      }\n    }\n    if (swiper.params.hashNavigation.watchState) {\n      window.addEventListener('hashchange', onHashChange);\n    }\n  };\n  const destroy = () => {\n    if (swiper.params.hashNavigation.watchState) {\n      window.removeEventListener('hashchange', onHashChange);\n    }\n  };\n  on('init', () => {\n    if (swiper.params.hashNavigation.enabled) {\n      init();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.params.hashNavigation.enabled) {\n      destroy();\n    }\n  });\n  on('transitionEnd _freeModeNoMomentumRelease', () => {\n    if (initialized) {\n      setHash();\n    }\n  });\n  on('slideChange', () => {\n    if (initialized && swiper.params.cssMode) {\n      setHash();\n    }\n  });\n}", "/* eslint no-underscore-dangle: \"off\" */\n/* eslint no-use-before-define: \"off\" */\nimport { getDocument } from 'ssr-window';\nexport default function Autoplay({\n  swiper,\n  extendParams,\n  on,\n  emit,\n  params\n}) {\n  swiper.autoplay = {\n    running: false,\n    paused: false,\n    timeLeft: 0\n  };\n  extendParams({\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: true,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n      pauseOnMouseEnter: false\n    }\n  });\n  let timeout;\n  let raf;\n  let autoplayDelayTotal = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayDelayCurrent = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayTimeLeft;\n  let autoplayStartTime = new Date().getTime;\n  let wasPaused;\n  let isTouched;\n  let pausedByTouch;\n  let touchStartTimeout;\n  let slideChanged;\n  let pausedByInteraction;\n  function onTransitionEnd(e) {\n    if (!swiper || swiper.destroyed || !swiper.wrapperEl) return;\n    if (e.target !== swiper.wrapperEl) return;\n    swiper.wrapperEl.removeEventListener('transitionend', onTransitionEnd);\n    resume();\n  }\n  const calcTimeLeft = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) {\n      wasPaused = true;\n    } else if (wasPaused) {\n      autoplayDelayCurrent = autoplayTimeLeft;\n      wasPaused = false;\n    }\n    const timeLeft = swiper.autoplay.paused ? autoplayTimeLeft : autoplayStartTime + autoplayDelayCurrent - new Date().getTime();\n    swiper.autoplay.timeLeft = timeLeft;\n    emit('autoplayTimeLeft', timeLeft, timeLeft / autoplayDelayTotal);\n    raf = requestAnimationFrame(() => {\n      calcTimeLeft();\n    });\n  };\n  const getSlideDelay = () => {\n    let activeSlideEl;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      activeSlideEl = swiper.slides.filter(slideEl => slideEl.classList.contains('swiper-slide-active'))[0];\n    } else {\n      activeSlideEl = swiper.slides[swiper.activeIndex];\n    }\n    if (!activeSlideEl) return undefined;\n    const currentSlideDelay = parseInt(activeSlideEl.getAttribute('data-swiper-autoplay'), 10);\n    return currentSlideDelay;\n  };\n  const run = delayForce => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    cancelAnimationFrame(raf);\n    calcTimeLeft();\n    let delay = typeof delayForce === 'undefined' ? swiper.params.autoplay.delay : delayForce;\n    autoplayDelayTotal = swiper.params.autoplay.delay;\n    autoplayDelayCurrent = swiper.params.autoplay.delay;\n    const currentSlideDelay = getSlideDelay();\n    if (!Number.isNaN(currentSlideDelay) && currentSlideDelay > 0 && typeof delayForce === 'undefined') {\n      delay = currentSlideDelay;\n      autoplayDelayTotal = currentSlideDelay;\n      autoplayDelayCurrent = currentSlideDelay;\n    }\n    autoplayTimeLeft = delay;\n    const speed = swiper.params.speed;\n    const proceed = () => {\n      if (!swiper || swiper.destroyed) return;\n      if (swiper.params.autoplay.reverseDirection) {\n        if (!swiper.isBeginning || swiper.params.loop || swiper.params.rewind) {\n          swiper.slidePrev(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(swiper.slides.length - 1, speed, true, true);\n          emit('autoplay');\n        }\n      } else {\n        if (!swiper.isEnd || swiper.params.loop || swiper.params.rewind) {\n          swiper.slideNext(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, speed, true, true);\n          emit('autoplay');\n        }\n      }\n      if (swiper.params.cssMode) {\n        autoplayStartTime = new Date().getTime();\n        requestAnimationFrame(() => {\n          run();\n        });\n      }\n    };\n    if (delay > 0) {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        proceed();\n      }, delay);\n    } else {\n      requestAnimationFrame(() => {\n        proceed();\n      });\n    }\n\n    // eslint-disable-next-line\n    return delay;\n  };\n  const start = () => {\n    swiper.autoplay.running = true;\n    run();\n    emit('autoplayStart');\n  };\n  const stop = () => {\n    swiper.autoplay.running = false;\n    clearTimeout(timeout);\n    cancelAnimationFrame(raf);\n    emit('autoplayStop');\n  };\n  const pause = (internal, reset) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    clearTimeout(timeout);\n    if (!internal) {\n      pausedByInteraction = true;\n    }\n    const proceed = () => {\n      emit('autoplayPause');\n      if (swiper.params.autoplay.waitForTransition) {\n        swiper.wrapperEl.addEventListener('transitionend', onTransitionEnd);\n      } else {\n        resume();\n      }\n    };\n    swiper.autoplay.paused = true;\n    if (reset) {\n      if (slideChanged) {\n        autoplayTimeLeft = swiper.params.autoplay.delay;\n      }\n      slideChanged = false;\n      proceed();\n      return;\n    }\n    const delay = autoplayTimeLeft || swiper.params.autoplay.delay;\n    autoplayTimeLeft = delay - (new Date().getTime() - autoplayStartTime);\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) return;\n    if (autoplayTimeLeft < 0) autoplayTimeLeft = 0;\n    proceed();\n  };\n  const resume = () => {\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop || swiper.destroyed || !swiper.autoplay.running) return;\n    autoplayStartTime = new Date().getTime();\n    if (pausedByInteraction) {\n      pausedByInteraction = false;\n      run(autoplayTimeLeft);\n    } else {\n      run();\n    }\n    swiper.autoplay.paused = false;\n    emit('autoplayResume');\n  };\n  const onVisibilityChange = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    const document = getDocument();\n    if (document.visibilityState === 'hidden') {\n      pausedByInteraction = true;\n      pause(true);\n    }\n    if (document.visibilityState === 'visible') {\n      resume();\n    }\n  };\n  const onPointerEnter = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByInteraction = true;\n    pause(true);\n  };\n  const onPointerLeave = e => {\n    if (e.pointerType !== 'mouse') return;\n    if (swiper.autoplay.paused) {\n      resume();\n    }\n  };\n  const attachMouseEvents = () => {\n    if (swiper.params.autoplay.pauseOnMouseEnter) {\n      swiper.el.addEventListener('pointerenter', onPointerEnter);\n      swiper.el.addEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const detachMouseEvents = () => {\n    swiper.el.removeEventListener('pointerenter', onPointerEnter);\n    swiper.el.removeEventListener('pointerleave', onPointerLeave);\n  };\n  const attachDocumentEvents = () => {\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n  };\n  const detachDocumentEvents = () => {\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n  };\n  on('init', () => {\n    if (swiper.params.autoplay.enabled) {\n      attachMouseEvents();\n      attachDocumentEvents();\n      autoplayStartTime = new Date().getTime();\n      start();\n    }\n  });\n  on('destroy', () => {\n    detachMouseEvents();\n    detachDocumentEvents();\n    if (swiper.autoplay.running) {\n      stop();\n    }\n  });\n  on('beforeTransitionStart', (_s, speed, internal) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (internal || !swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('sliderFirstMove', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.params.autoplay.disableOnInteraction) {\n      stop();\n      return;\n    }\n    isTouched = true;\n    pausedByTouch = false;\n    pausedByInteraction = false;\n    touchStartTimeout = setTimeout(() => {\n      pausedByInteraction = true;\n      pausedByTouch = true;\n      pause(true);\n    }, 200);\n  });\n  on('touchEnd', () => {\n    if (swiper.destroyed || !swiper.autoplay.running || !isTouched) return;\n    clearTimeout(touchStartTimeout);\n    clearTimeout(timeout);\n    if (swiper.params.autoplay.disableOnInteraction) {\n      pausedByTouch = false;\n      isTouched = false;\n      return;\n    }\n    if (pausedByTouch && swiper.params.cssMode) resume();\n    pausedByTouch = false;\n    isTouched = false;\n  });\n  on('slideChange', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    slideChanged = true;\n  });\n  Object.assign(swiper.autoplay, {\n    start,\n    stop,\n    pause,\n    resume\n  });\n}", "import { getDocument } from 'ssr-window';\nimport { elementChildren, isObject } from '../../shared/utils.js';\nexport default function Thumb({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    thumbs: {\n      swiper: null,\n      multipleActiveThumbs: true,\n      autoScrollOffset: 0,\n      slideThumbActiveClass: 'swiper-slide-thumb-active',\n      thumbsContainerClass: 'swiper-thumbs'\n    }\n  });\n  let initialized = false;\n  let swiperCreated = false;\n  swiper.thumbs = {\n    swiper: null\n  };\n  function onThumbClick() {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n    const clickedIndex = thumbsSwiper.clickedIndex;\n    const clickedSlide = thumbsSwiper.clickedSlide;\n    if (clickedSlide && clickedSlide.classList.contains(swiper.params.thumbs.slideThumbActiveClass)) return;\n    if (typeof clickedIndex === 'undefined' || clickedIndex === null) return;\n    let slideToIndex;\n    if (thumbsSwiper.params.loop) {\n      slideToIndex = parseInt(thumbsSwiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      slideToIndex = clickedIndex;\n    }\n    if (swiper.params.loop) {\n      swiper.slideToLoop(slideToIndex);\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  }\n  function init() {\n    const {\n      thumbs: thumbsParams\n    } = swiper.params;\n    if (initialized) return false;\n    initialized = true;\n    const SwiperClass = swiper.constructor;\n    if (thumbsParams.swiper instanceof SwiperClass) {\n      swiper.thumbs.swiper = thumbsParams.swiper;\n      Object.assign(swiper.thumbs.swiper.originalParams, {\n        watchSlidesProgress: true,\n        slideToClickedSlide: false\n      });\n      Object.assign(swiper.thumbs.swiper.params, {\n        watchSlidesProgress: true,\n        slideToClickedSlide: false\n      });\n      swiper.thumbs.swiper.update();\n    } else if (isObject(thumbsParams.swiper)) {\n      const thumbsSwiperParams = Object.assign({}, thumbsParams.swiper);\n      Object.assign(thumbsSwiperParams, {\n        watchSlidesProgress: true,\n        slideToClickedSlide: false\n      });\n      swiper.thumbs.swiper = new SwiperClass(thumbsSwiperParams);\n      swiperCreated = true;\n    }\n    swiper.thumbs.swiper.el.classList.add(swiper.params.thumbs.thumbsContainerClass);\n    swiper.thumbs.swiper.on('tap', onThumbClick);\n    return true;\n  }\n  function update(initial) {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n    const slidesPerView = thumbsSwiper.params.slidesPerView === 'auto' ? thumbsSwiper.slidesPerViewDynamic() : thumbsSwiper.params.slidesPerView;\n\n    // Activate thumbs\n    let thumbsToActivate = 1;\n    const thumbActiveClass = swiper.params.thumbs.slideThumbActiveClass;\n    if (swiper.params.slidesPerView > 1 && !swiper.params.centeredSlides) {\n      thumbsToActivate = swiper.params.slidesPerView;\n    }\n    if (!swiper.params.thumbs.multipleActiveThumbs) {\n      thumbsToActivate = 1;\n    }\n    thumbsToActivate = Math.floor(thumbsToActivate);\n    thumbsSwiper.slides.forEach(slideEl => slideEl.classList.remove(thumbActiveClass));\n    if (thumbsSwiper.params.loop || thumbsSwiper.params.virtual && thumbsSwiper.params.virtual.enabled) {\n      for (let i = 0; i < thumbsToActivate; i += 1) {\n        elementChildren(thumbsSwiper.slidesEl, `[data-swiper-slide-index=\"${swiper.realIndex + i}\"]`).forEach(slideEl => {\n          slideEl.classList.add(thumbActiveClass);\n        });\n      }\n    } else {\n      for (let i = 0; i < thumbsToActivate; i += 1) {\n        if (thumbsSwiper.slides[swiper.realIndex + i]) {\n          thumbsSwiper.slides[swiper.realIndex + i].classList.add(thumbActiveClass);\n        }\n      }\n    }\n    const autoScrollOffset = swiper.params.thumbs.autoScrollOffset;\n    const useOffset = autoScrollOffset && !thumbsSwiper.params.loop;\n    if (swiper.realIndex !== thumbsSwiper.realIndex || useOffset) {\n      const currentThumbsIndex = thumbsSwiper.activeIndex;\n      let newThumbsIndex;\n      let direction;\n      if (thumbsSwiper.params.loop) {\n        const newThumbsSlide = thumbsSwiper.slides.filter(slideEl => slideEl.getAttribute('data-swiper-slide-index') === `${swiper.realIndex}`)[0];\n        newThumbsIndex = thumbsSwiper.slides.indexOf(newThumbsSlide);\n        direction = swiper.activeIndex > swiper.previousIndex ? 'next' : 'prev';\n      } else {\n        newThumbsIndex = swiper.realIndex;\n        direction = newThumbsIndex > swiper.previousIndex ? 'next' : 'prev';\n      }\n      if (useOffset) {\n        newThumbsIndex += direction === 'next' ? autoScrollOffset : -1 * autoScrollOffset;\n      }\n      if (thumbsSwiper.visibleSlidesIndexes && thumbsSwiper.visibleSlidesIndexes.indexOf(newThumbsIndex) < 0) {\n        if (thumbsSwiper.params.centeredSlides) {\n          if (newThumbsIndex > currentThumbsIndex) {\n            newThumbsIndex = newThumbsIndex - Math.floor(slidesPerView / 2) + 1;\n          } else {\n            newThumbsIndex = newThumbsIndex + Math.floor(slidesPerView / 2) - 1;\n          }\n        } else if (newThumbsIndex > currentThumbsIndex && thumbsSwiper.params.slidesPerGroup === 1) {\n          // newThumbsIndex = newThumbsIndex - slidesPerView + 1;\n        }\n        thumbsSwiper.slideTo(newThumbsIndex, initial ? 0 : undefined);\n      }\n    }\n  }\n  on('beforeInit', () => {\n    const {\n      thumbs\n    } = swiper.params;\n    if (!thumbs || !thumbs.swiper) return;\n    if (typeof thumbs.swiper === 'string' || thumbs.swiper instanceof HTMLElement) {\n      const document = getDocument();\n      const getThumbsElementAndInit = () => {\n        const thumbsElement = typeof thumbs.swiper === 'string' ? document.querySelector(thumbs.swiper) : thumbs.swiper;\n        if (thumbsElement && thumbsElement.swiper) {\n          thumbs.swiper = thumbsElement.swiper;\n          init();\n          update(true);\n        } else if (thumbsElement) {\n          const onThumbsSwiper = e => {\n            thumbs.swiper = e.detail[0];\n            thumbsElement.removeEventListener('init', onThumbsSwiper);\n            init();\n            update(true);\n            thumbs.swiper.update();\n            swiper.update();\n          };\n          thumbsElement.addEventListener('init', onThumbsSwiper);\n        }\n        return thumbsElement;\n      };\n      const watchForThumbsToAppear = () => {\n        if (swiper.destroyed) return;\n        const thumbsElement = getThumbsElementAndInit();\n        if (!thumbsElement) {\n          requestAnimationFrame(watchForThumbsToAppear);\n        }\n      };\n      requestAnimationFrame(watchForThumbsToAppear);\n    } else {\n      init();\n      update(true);\n    }\n  });\n  on('slideChange update resize observerUpdate', () => {\n    update();\n  });\n  on('setTransition', (_s, duration) => {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n    thumbsSwiper.setTransition(duration);\n  });\n  on('beforeDestroy', () => {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n    if (swiperCreated) {\n      thumbsSwiper.destroy();\n    }\n  });\n  Object.assign(swiper.thumbs, {\n    init,\n    update\n  });\n}", "import { elementTransitionEnd, now } from '../../shared/utils.js';\nexport default function freeMode({\n  swiper,\n  extendParams,\n  emit,\n  once\n}) {\n  extendParams({\n    freeMode: {\n      enabled: false,\n      momentum: true,\n      momentumRatio: 1,\n      momentumBounce: true,\n      momentumBounceRatio: 1,\n      momentumVelocityRatio: 1,\n      sticky: false,\n      minimumVelocity: 0.02\n    }\n  });\n  function onTouchStart() {\n    const translate = swiper.getTranslate();\n    swiper.setTranslate(translate);\n    swiper.setTransition(0);\n    swiper.touchEventsData.velocities.length = 0;\n    swiper.freeMode.onTouchEnd({\n      currentPos: swiper.rtl ? swiper.translate : -swiper.translate\n    });\n  }\n  function onTouchMove() {\n    const {\n      touchEventsData: data,\n      touches\n    } = swiper;\n    // Velocity\n    if (data.velocities.length === 0) {\n      data.velocities.push({\n        position: touches[swiper.isHorizontal() ? 'startX' : 'startY'],\n        time: data.touchStartTime\n      });\n    }\n    data.velocities.push({\n      position: touches[swiper.isHorizontal() ? 'currentX' : 'currentY'],\n      time: now()\n    });\n  }\n  function onTouchEnd({\n    currentPos\n  }) {\n    const {\n      params,\n      wrapperEl,\n      rtlTranslate: rtl,\n      snapGrid,\n      touchEventsData: data\n    } = swiper;\n    // Time diff\n    const touchEndTime = now();\n    const timeDiff = touchEndTime - data.touchStartTime;\n    if (currentPos < -swiper.minTranslate()) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (currentPos > -swiper.maxTranslate()) {\n      if (swiper.slides.length < snapGrid.length) {\n        swiper.slideTo(snapGrid.length - 1);\n      } else {\n        swiper.slideTo(swiper.slides.length - 1);\n      }\n      return;\n    }\n    if (params.freeMode.momentum) {\n      if (data.velocities.length > 1) {\n        const lastMoveEvent = data.velocities.pop();\n        const velocityEvent = data.velocities.pop();\n        const distance = lastMoveEvent.position - velocityEvent.position;\n        const time = lastMoveEvent.time - velocityEvent.time;\n        swiper.velocity = distance / time;\n        swiper.velocity /= 2;\n        if (Math.abs(swiper.velocity) < params.freeMode.minimumVelocity) {\n          swiper.velocity = 0;\n        }\n        // this implies that the user stopped moving a finger then released.\n        // There would be no events with distance zero, so the last event is stale.\n        if (time > 150 || now() - lastMoveEvent.time > 300) {\n          swiper.velocity = 0;\n        }\n      } else {\n        swiper.velocity = 0;\n      }\n      swiper.velocity *= params.freeMode.momentumVelocityRatio;\n      data.velocities.length = 0;\n      let momentumDuration = 1000 * params.freeMode.momentumRatio;\n      const momentumDistance = swiper.velocity * momentumDuration;\n      let newPosition = swiper.translate + momentumDistance;\n      if (rtl) newPosition = -newPosition;\n      let doBounce = false;\n      let afterBouncePosition;\n      const bounceAmount = Math.abs(swiper.velocity) * 20 * params.freeMode.momentumBounceRatio;\n      let needsLoopFix;\n      if (newPosition < swiper.maxTranslate()) {\n        if (params.freeMode.momentumBounce) {\n          if (newPosition + swiper.maxTranslate() < -bounceAmount) {\n            newPosition = swiper.maxTranslate() - bounceAmount;\n          }\n          afterBouncePosition = swiper.maxTranslate();\n          doBounce = true;\n          data.allowMomentumBounce = true;\n        } else {\n          newPosition = swiper.maxTranslate();\n        }\n        if (params.loop && params.centeredSlides) needsLoopFix = true;\n      } else if (newPosition > swiper.minTranslate()) {\n        if (params.freeMode.momentumBounce) {\n          if (newPosition - swiper.minTranslate() > bounceAmount) {\n            newPosition = swiper.minTranslate() + bounceAmount;\n          }\n          afterBouncePosition = swiper.minTranslate();\n          doBounce = true;\n          data.allowMomentumBounce = true;\n        } else {\n          newPosition = swiper.minTranslate();\n        }\n        if (params.loop && params.centeredSlides) needsLoopFix = true;\n      } else if (params.freeMode.sticky) {\n        let nextSlide;\n        for (let j = 0; j < snapGrid.length; j += 1) {\n          if (snapGrid[j] > -newPosition) {\n            nextSlide = j;\n            break;\n          }\n        }\n        if (Math.abs(snapGrid[nextSlide] - newPosition) < Math.abs(snapGrid[nextSlide - 1] - newPosition) || swiper.swipeDirection === 'next') {\n          newPosition = snapGrid[nextSlide];\n        } else {\n          newPosition = snapGrid[nextSlide - 1];\n        }\n        newPosition = -newPosition;\n      }\n      if (needsLoopFix) {\n        once('transitionEnd', () => {\n          swiper.loopFix();\n        });\n      }\n      // Fix duration\n      if (swiper.velocity !== 0) {\n        if (rtl) {\n          momentumDuration = Math.abs((-newPosition - swiper.translate) / swiper.velocity);\n        } else {\n          momentumDuration = Math.abs((newPosition - swiper.translate) / swiper.velocity);\n        }\n        if (params.freeMode.sticky) {\n          // If freeMode.sticky is active and the user ends a swipe with a slow-velocity\n          // event, then durations can be 20+ seconds to slide one (or zero!) slides.\n          // It's easy to see this when simulating touch with mouse events. To fix this,\n          // limit single-slide swipes to the default slide duration. This also has the\n          // nice side effect of matching slide speed if the user stopped moving before\n          // lifting finger or mouse vs. moving slowly before lifting the finger/mouse.\n          // For faster swipes, also apply limits (albeit higher ones).\n          const moveDistance = Math.abs((rtl ? -newPosition : newPosition) - swiper.translate);\n          const currentSlideSize = swiper.slidesSizesGrid[swiper.activeIndex];\n          if (moveDistance < currentSlideSize) {\n            momentumDuration = params.speed;\n          } else if (moveDistance < 2 * currentSlideSize) {\n            momentumDuration = params.speed * 1.5;\n          } else {\n            momentumDuration = params.speed * 2.5;\n          }\n        }\n      } else if (params.freeMode.sticky) {\n        swiper.slideToClosest();\n        return;\n      }\n      if (params.freeMode.momentumBounce && doBounce) {\n        swiper.updateProgress(afterBouncePosition);\n        swiper.setTransition(momentumDuration);\n        swiper.setTranslate(newPosition);\n        swiper.transitionStart(true, swiper.swipeDirection);\n        swiper.animating = true;\n        elementTransitionEnd(wrapperEl, () => {\n          if (!swiper || swiper.destroyed || !data.allowMomentumBounce) return;\n          emit('momentumBounce');\n          swiper.setTransition(params.speed);\n          setTimeout(() => {\n            swiper.setTranslate(afterBouncePosition);\n            elementTransitionEnd(wrapperEl, () => {\n              if (!swiper || swiper.destroyed) return;\n              swiper.transitionEnd();\n            });\n          }, 0);\n        });\n      } else if (swiper.velocity) {\n        emit('_freeModeNoMomentumRelease');\n        swiper.updateProgress(newPosition);\n        swiper.setTransition(momentumDuration);\n        swiper.setTranslate(newPosition);\n        swiper.transitionStart(true, swiper.swipeDirection);\n        if (!swiper.animating) {\n          swiper.animating = true;\n          elementTransitionEnd(wrapperEl, () => {\n            if (!swiper || swiper.destroyed) return;\n            swiper.transitionEnd();\n          });\n        }\n      } else {\n        swiper.updateProgress(newPosition);\n      }\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    } else if (params.freeMode.sticky) {\n      swiper.slideToClosest();\n      return;\n    } else if (params.freeMode) {\n      emit('_freeModeNoMomentumRelease');\n    }\n    if (!params.freeMode.momentum || timeDiff >= params.longSwipesMs) {\n      swiper.updateProgress();\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n  }\n  Object.assign(swiper, {\n    freeMode: {\n      onTouchStart,\n      onTouchMove,\n      onTouchEnd\n    }\n  });\n}", "export default function Grid({\n  swiper,\n  extendParams\n}) {\n  extendParams({\n    grid: {\n      rows: 1,\n      fill: 'column'\n    }\n  });\n  let slidesNumberEvenToRows;\n  let slidesPerRow;\n  let numFullColumns;\n  const initSlides = slidesLength => {\n    const {\n      slidesPerView\n    } = swiper.params;\n    const {\n      rows,\n      fill\n    } = swiper.params.grid;\n    slidesPerRow = slidesNumberEvenToRows / rows;\n    numFullColumns = Math.floor(slidesLength / rows);\n    if (Math.floor(slidesLength / rows) === slidesLength / rows) {\n      slidesNumberEvenToRows = slidesLength;\n    } else {\n      slidesNumberEvenToRows = Math.ceil(slidesLength / rows) * rows;\n    }\n    if (slidesPerView !== 'auto' && fill === 'row') {\n      slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, slidesPerView * rows);\n    }\n  };\n  const updateSlide = (i, slide, slidesLength, getDirectionLabel) => {\n    const {\n      slidesPerGroup,\n      spaceBetween\n    } = swiper.params;\n    const {\n      rows,\n      fill\n    } = swiper.params.grid;\n    // Set slides order\n    let newSlideOrderIndex;\n    let column;\n    let row;\n    if (fill === 'row' && slidesPerGroup > 1) {\n      const groupIndex = Math.floor(i / (slidesPerGroup * rows));\n      const slideIndexInGroup = i - rows * slidesPerGroup * groupIndex;\n      const columnsInGroup = groupIndex === 0 ? slidesPerGroup : Math.min(Math.ceil((slidesLength - groupIndex * rows * slidesPerGroup) / rows), slidesPerGroup);\n      row = Math.floor(slideIndexInGroup / columnsInGroup);\n      column = slideIndexInGroup - row * columnsInGroup + groupIndex * slidesPerGroup;\n      newSlideOrderIndex = column + row * slidesNumberEvenToRows / rows;\n      slide.style.order = newSlideOrderIndex;\n    } else if (fill === 'column') {\n      column = Math.floor(i / rows);\n      row = i - column * rows;\n      if (column > numFullColumns || column === numFullColumns && row === rows - 1) {\n        row += 1;\n        if (row >= rows) {\n          row = 0;\n          column += 1;\n        }\n      }\n    } else {\n      row = Math.floor(i / slidesPerRow);\n      column = i - row * slidesPerRow;\n    }\n    slide.style[getDirectionLabel('margin-top')] = row !== 0 ? spaceBetween && `${spaceBetween}px` : '';\n  };\n  const updateWrapperSize = (slideSize, snapGrid, getDirectionLabel) => {\n    const {\n      spaceBetween,\n      centeredSlides,\n      roundLengths\n    } = swiper.params;\n    const {\n      rows\n    } = swiper.params.grid;\n    swiper.virtualSize = (slideSize + spaceBetween) * slidesNumberEvenToRows;\n    swiper.virtualSize = Math.ceil(swiper.virtualSize / rows) - spaceBetween;\n    swiper.wrapperEl.style[getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n    if (centeredSlides) {\n      const newSlidesGrid = [];\n      for (let i = 0; i < snapGrid.length; i += 1) {\n        let slidesGridItem = snapGrid[i];\n        if (roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n        if (snapGrid[i] < swiper.virtualSize + snapGrid[0]) newSlidesGrid.push(slidesGridItem);\n      }\n      snapGrid.splice(0, snapGrid.length);\n      snapGrid.push(...newSlidesGrid);\n    }\n  };\n  swiper.grid = {\n    initSlides,\n    updateSlide,\n    updateWrapperSize\n  };\n}", "export default function appendSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  const appendElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      tempDOM.innerHTML = slideEl;\n      slidesEl.append(tempDOM.children[0]);\n      tempDOM.innerHTML = '';\n    } else {\n      slidesEl.append(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) appendElement(slides[i]);\n    }\n  } else {\n    appendElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n}", "export default function prependSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndex + 1;\n  const prependElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      tempDOM.innerHTML = slideEl;\n      slidesEl.prepend(tempDOM.children[0]);\n      tempDOM.innerHTML = '';\n    } else {\n      slidesEl.prepend(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) prependElement(slides[i]);\n    }\n    newActiveIndex = activeIndex + slides.length;\n  } else {\n    prependElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  swiper.slideTo(newActiveIndex, 0, false);\n}", "export default function addSlide(index, slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n    swiper.recalcSlides();\n  }\n  const baseLength = swiper.slides.length;\n  if (index <= 0) {\n    swiper.prependSlide(slides);\n    return;\n  }\n  if (index >= baseLength) {\n    swiper.appendSlide(slides);\n    return;\n  }\n  let newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n  const slidesBuffer = [];\n  for (let i = baseLength - 1; i >= index; i -= 1) {\n    const currentSlide = swiper.slides[i];\n    currentSlide.remove();\n    slidesBuffer.unshift(currentSlide);\n  }\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) slidesEl.append(slides[i]);\n    }\n    newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n  } else {\n    slidesEl.append(slides);\n  }\n  for (let i = 0; i < slidesBuffer.length; i += 1) {\n    slidesEl.append(slidesBuffer[i]);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}", "export default function removeSlide(slidesIndexes) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndexBuffer;\n  let indexToRemove;\n  if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n    for (let i = 0; i < slidesIndexes.length; i += 1) {\n      indexToRemove = slidesIndexes[i];\n      if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n      if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    }\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  } else {\n    indexToRemove = slidesIndexes;\n    if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n    if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}", "export default function removeAllSlides() {\n  const swiper = this;\n  const slidesIndexes = [];\n  for (let i = 0; i < swiper.slides.length; i += 1) {\n    slidesIndexes.push(i);\n  }\n  swiper.removeSlide(slidesIndexes);\n}", "import appendSlide from './methods/appendSlide.js';\nimport prependSlide from './methods/prependSlide.js';\nimport addSlide from './methods/addSlide.js';\nimport removeSlide from './methods/removeSlide.js';\nimport removeAllSlides from './methods/removeAllSlides.js';\nexport default function Manipulation({\n  swiper\n}) {\n  Object.assign(swiper, {\n    appendSlide: appendSlide.bind(swiper),\n    prependSlide: prependSlide.bind(swiper),\n    addSlide: addSlide.bind(swiper),\n    removeSlide: removeSlide.bind(swiper),\n    removeAllSlides: removeAllSlides.bind(swiper)\n  });\n}", "export default function effectInit(params) {\n  const {\n    effect,\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams,\n    perspective,\n    recreateShadows,\n    getEffectParams\n  } = params;\n  on('beforeInit', () => {\n    if (swiper.params.effect !== effect) return;\n    swiper.classNames.push(`${swiper.params.containerModifierClass}${effect}`);\n    if (perspective && perspective()) {\n      swiper.classNames.push(`${swiper.params.containerModifierClass}3d`);\n    }\n    const overwriteParamsResult = overwriteParams ? overwriteParams() : {};\n    Object.assign(swiper.params, overwriteParamsResult);\n    Object.assign(swiper.originalParams, overwriteParamsResult);\n  });\n  on('setTranslate', () => {\n    if (swiper.params.effect !== effect) return;\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    if (swiper.params.effect !== effect) return;\n    setTransition(duration);\n  });\n  on('transitionEnd', () => {\n    if (swiper.params.effect !== effect) return;\n    if (recreateShadows) {\n      if (!getEffectParams || !getEffectParams().slideShadows) return;\n      // remove shadows\n      swiper.slides.forEach(slideEl => {\n        slideEl.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => shadowEl.remove());\n      });\n      // create new one\n      recreateShadows();\n    }\n  });\n  let requireUpdateOnVirtual;\n  on('virtualUpdate', () => {\n    if (swiper.params.effect !== effect) return;\n    if (!swiper.slides.length) {\n      requireUpdateOnVirtual = true;\n    }\n    requestAnimationFrame(() => {\n      if (requireUpdateOnVirtual && swiper.slides && swiper.slides.length) {\n        setTranslate();\n        requireUpdateOnVirtual = false;\n      }\n    });\n  });\n}", "import { getSlideTransformEl } from './utils.js';\nexport default function effectTarget(effectParams, slideEl) {\n  const transformEl = getSlideTransformEl(slideEl);\n  if (transformEl !== slideEl) {\n    transformEl.style.backfaceVisibility = 'hidden';\n    transformEl.style['-webkit-backface-visibility'] = 'hidden';\n  }\n  return transformEl;\n}", "import { elementTransitionEnd } from './utils.js';\nexport default function effectVirtualTransitionEnd({\n  swiper,\n  duration,\n  transformElements,\n  allSlides\n}) {\n  const {\n    activeIndex\n  } = swiper;\n  const getSlide = el => {\n    if (!el.parentElement) {\n      // assume shadow root\n      const slide = swiper.slides.filter(slideEl => slideEl.shadowEl && slideEl.shadowEl === el.parentNode)[0];\n      return slide;\n    }\n    return el.parentElement;\n  };\n  if (swiper.params.virtualTranslate && duration !== 0) {\n    let eventTriggered = false;\n    let transitionEndTarget;\n    if (allSlides) {\n      transitionEndTarget = transformElements;\n    } else {\n      transitionEndTarget = transformElements.filter(transformEl => {\n        const el = transformEl.classList.contains('swiper-slide-transform') ? getSlide(transformEl) : transformEl;\n        return swiper.getSlideIndex(el) === activeIndex;\n      });\n    }\n    transitionEndTarget.forEach(el => {\n      elementTransitionEnd(el, () => {\n        if (eventTriggered) return;\n        if (!swiper || swiper.destroyed) return;\n        eventTriggered = true;\n        swiper.animating = false;\n        const evt = new window.CustomEvent('transitionend', {\n          bubbles: true,\n          cancelable: true\n        });\n        swiper.wrapperEl.dispatchEvent(evt);\n      });\n    });\n  }\n}", "import effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport effectVirtualTransitionEnd from '../../shared/effect-virtual-transition-end.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\nexport default function EffectFade({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    fadeEffect: {\n      crossFade: false\n    }\n  });\n  const setTranslate = () => {\n    const {\n      slides\n    } = swiper;\n    const params = swiper.params.fadeEffect;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = swiper.slides[i];\n      const offset = slideEl.swiperSlideOffset;\n      let tx = -offset;\n      if (!swiper.params.virtualTranslate) tx -= swiper.translate;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n      }\n      const slideOpacity = swiper.params.fadeEffect.crossFade ? Math.max(1 - Math.abs(slideEl.progress), 0) : 1 + Math.min(Math.max(slideEl.progress, -1), 0);\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.opacity = slideOpacity;\n      targetEl.style.transform = `translate3d(${tx}px, ${ty}px, 0px)`;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements,\n      allSlides: true\n    });\n  };\n  effectInit({\n    effect: 'fade',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}", "import effectInit from '../../shared/effect-init.js';\nimport { createElement } from '../../shared/utils.js';\nexport default function EffectCube({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    cubeEffect: {\n      slideShadows: true,\n      shadow: true,\n      shadowOffset: 20,\n      shadowScale: 0.94\n    }\n  });\n  const createSlideShadows = (slideEl, progress, isHorizontal) => {\n    let shadowBefore = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n    let shadowAfter = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n    if (!shadowBefore) {\n      shadowBefore = createElement('div', `swiper-slide-shadow-${isHorizontal ? 'left' : 'top'}`);\n      slideEl.append(shadowBefore);\n    }\n    if (!shadowAfter) {\n      shadowAfter = createElement('div', `swiper-slide-shadow-${isHorizontal ? 'right' : 'bottom'}`);\n      slideEl.append(shadowAfter);\n    }\n    if (shadowBefore) shadowBefore.style.opacity = Math.max(-progress, 0);\n    if (shadowAfter) shadowAfter.style.opacity = Math.max(progress, 0);\n  };\n  const recreateShadows = () => {\n    // create new ones\n    const isHorizontal = swiper.isHorizontal();\n    swiper.slides.forEach(slideEl => {\n      const progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      createSlideShadows(slideEl, progress, isHorizontal);\n    });\n  };\n  const setTranslate = () => {\n    const {\n      el,\n      wrapperEl,\n      slides,\n      width: swiperWidth,\n      height: swiperHeight,\n      rtlTranslate: rtl,\n      size: swiperSize,\n      browser\n    } = swiper;\n    const params = swiper.params.cubeEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n    let wrapperRotate = 0;\n    let cubeShadowEl;\n    if (params.shadow) {\n      if (isHorizontal) {\n        cubeShadowEl = swiper.slidesEl.querySelector('.swiper-cube-shadow');\n        if (!cubeShadowEl) {\n          cubeShadowEl = createElement('div', 'swiper-cube-shadow');\n          swiper.slidesEl.append(cubeShadowEl);\n        }\n        cubeShadowEl.style.height = `${swiperWidth}px`;\n      } else {\n        cubeShadowEl = el.querySelector('.swiper-cube-shadow');\n        if (!cubeShadowEl) {\n          cubeShadowEl = createElement('div', 'swiper-cube-shadow');\n          el.append(cubeShadowEl);\n        }\n      }\n    }\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      let slideIndex = i;\n      if (isVirtual) {\n        slideIndex = parseInt(slideEl.getAttribute('data-swiper-slide-index'), 10);\n      }\n      let slideAngle = slideIndex * 90;\n      let round = Math.floor(slideAngle / 360);\n      if (rtl) {\n        slideAngle = -slideAngle;\n        round = Math.floor(-slideAngle / 360);\n      }\n      const progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      let tx = 0;\n      let ty = 0;\n      let tz = 0;\n      if (slideIndex % 4 === 0) {\n        tx = -round * 4 * swiperSize;\n        tz = 0;\n      } else if ((slideIndex - 1) % 4 === 0) {\n        tx = 0;\n        tz = -round * 4 * swiperSize;\n      } else if ((slideIndex - 2) % 4 === 0) {\n        tx = swiperSize + round * 4 * swiperSize;\n        tz = swiperSize;\n      } else if ((slideIndex - 3) % 4 === 0) {\n        tx = -swiperSize;\n        tz = 3 * swiperSize + swiperSize * 4 * round;\n      }\n      if (rtl) {\n        tx = -tx;\n      }\n      if (!isHorizontal) {\n        ty = tx;\n        tx = 0;\n      }\n      const transform = `rotateX(${isHorizontal ? 0 : -slideAngle}deg) rotateY(${isHorizontal ? slideAngle : 0}deg) translate3d(${tx}px, ${ty}px, ${tz}px)`;\n      if (progress <= 1 && progress > -1) {\n        wrapperRotate = slideIndex * 90 + progress * 90;\n        if (rtl) wrapperRotate = -slideIndex * 90 - progress * 90;\n      }\n      slideEl.style.transform = transform;\n      if (params.slideShadows) {\n        createSlideShadows(slideEl, progress, isHorizontal);\n      }\n    }\n    wrapperEl.style.transformOrigin = `50% 50% -${swiperSize / 2}px`;\n    wrapperEl.style['-webkit-transform-origin'] = `50% 50% -${swiperSize / 2}px`;\n    if (params.shadow) {\n      if (isHorizontal) {\n        cubeShadowEl.style.transform = `translate3d(0px, ${swiperWidth / 2 + params.shadowOffset}px, ${-swiperWidth / 2}px) rotateX(90deg) rotateZ(0deg) scale(${params.shadowScale})`;\n      } else {\n        const shadowAngle = Math.abs(wrapperRotate) - Math.floor(Math.abs(wrapperRotate) / 90) * 90;\n        const multiplier = 1.5 - (Math.sin(shadowAngle * 2 * Math.PI / 360) / 2 + Math.cos(shadowAngle * 2 * Math.PI / 360) / 2);\n        const scale1 = params.shadowScale;\n        const scale2 = params.shadowScale / multiplier;\n        const offset = params.shadowOffset;\n        cubeShadowEl.style.transform = `scale3d(${scale1}, 1, ${scale2}) translate3d(0px, ${swiperHeight / 2 + offset}px, ${-swiperHeight / 2 / scale2}px) rotateX(-90deg)`;\n      }\n    }\n    const zFactor = (browser.isSafari || browser.isWebView) && browser.needPerspectiveFix ? -swiperSize / 2 : 0;\n    wrapperEl.style.transform = `translate3d(0px,0,${zFactor}px) rotateX(${swiper.isHorizontal() ? 0 : wrapperRotate}deg) rotateY(${swiper.isHorizontal() ? -wrapperRotate : 0}deg)`;\n    wrapperEl.style.setProperty('--swiper-cube-translate-z', `${zFactor}px`);\n  };\n  const setTransition = duration => {\n    const {\n      el,\n      slides\n    } = swiper;\n    slides.forEach(slideEl => {\n      slideEl.style.transitionDuration = `${duration}ms`;\n      slideEl.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(subEl => {\n        subEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    if (swiper.params.cubeEffect.shadow && !swiper.isHorizontal()) {\n      const shadowEl = el.querySelector('.swiper-cube-shadow');\n      if (shadowEl) shadowEl.style.transitionDuration = `${duration}ms`;\n    }\n  };\n  effectInit({\n    effect: 'cube',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    recreateShadows,\n    getEffectParams: () => swiper.params.cubeEffect,\n    perspective: () => true,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      resistanceRatio: 0,\n      spaceBetween: 0,\n      centeredSlides: false,\n      virtualTranslate: true\n    })\n  });\n}", "import { createElement, getSlideTransformEl } from './utils.js';\nexport default function createShadow(params, slideEl, side) {\n  const shadowClass = `swiper-slide-shadow${side ? `-${side}` : ''}`;\n  const shadowContainer = getSlideTransformEl(slideEl);\n  let shadowEl = shadowContainer.querySelector(`.${shadowClass}`);\n  if (!shadowEl) {\n    shadowEl = createElement('div', `swiper-slide-shadow${side ? `-${side}` : ''}`);\n    shadowContainer.append(shadowEl);\n  }\n  return shadowEl;\n}", "import createShadow from '../../shared/create-shadow.js';\nimport effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport effectVirtualTransitionEnd from '../../shared/effect-virtual-transition-end.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\nexport default function EffectFlip({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    flipEffect: {\n      slideShadows: true,\n      limitRotation: true\n    }\n  });\n  const createSlideShadows = (slideEl, progress, params) => {\n    let shadowBefore = swiper.isHorizontal() ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n    let shadowAfter = swiper.isHorizontal() ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n    if (!shadowBefore) {\n      shadowBefore = createShadow(params, slideEl, swiper.isHorizontal() ? 'left' : 'top');\n    }\n    if (!shadowAfter) {\n      shadowAfter = createShadow(params, slideEl, swiper.isHorizontal() ? 'right' : 'bottom');\n    }\n    if (shadowBefore) shadowBefore.style.opacity = Math.max(-progress, 0);\n    if (shadowAfter) shadowAfter.style.opacity = Math.max(progress, 0);\n  };\n  const recreateShadows = () => {\n    // Set shadows\n    const params = swiper.params.flipEffect;\n    swiper.slides.forEach(slideEl => {\n      let progress = slideEl.progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      }\n      createSlideShadows(slideEl, progress, params);\n    });\n  };\n  const setTranslate = () => {\n    const {\n      slides,\n      rtlTranslate: rtl\n    } = swiper;\n    const params = swiper.params.flipEffect;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      let progress = slideEl.progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      }\n      const offset = slideEl.swiperSlideOffset;\n      const rotate = -180 * progress;\n      let rotateY = rotate;\n      let rotateX = 0;\n      let tx = swiper.params.cssMode ? -offset - swiper.translate : -offset;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n        rotateX = -rotateY;\n        rotateY = 0;\n      } else if (rtl) {\n        rotateY = -rotateY;\n      }\n      slideEl.style.zIndex = -Math.abs(Math.round(progress)) + slides.length;\n      if (params.slideShadows) {\n        createSlideShadows(slideEl, progress, params);\n      }\n      const transform = `translate3d(${tx}px, ${ty}px, 0px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements\n    });\n  };\n  effectInit({\n    effect: 'flip',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    recreateShadows,\n    getEffectParams: () => swiper.params.flipEffect,\n    perspective: () => true,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}", "import createShadow from '../../shared/create-shadow.js';\nimport effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\nexport default function EffectCoverflow({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    coverflowEffect: {\n      rotate: 50,\n      stretch: 0,\n      depth: 100,\n      scale: 1,\n      modifier: 1,\n      slideShadows: true\n    }\n  });\n  const setTranslate = () => {\n    const {\n      width: swiperWidth,\n      height: swiperHeight,\n      slides,\n      slidesSizesGrid\n    } = swiper;\n    const params = swiper.params.coverflowEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const transform = swiper.translate;\n    const center = isHorizontal ? -transform + swiperWidth / 2 : -transform + swiperHeight / 2;\n    const rotate = isHorizontal ? params.rotate : -params.rotate;\n    const translate = params.depth;\n    // Each slide offset from center\n    for (let i = 0, length = slides.length; i < length; i += 1) {\n      const slideEl = slides[i];\n      const slideSize = slidesSizesGrid[i];\n      const slideOffset = slideEl.swiperSlideOffset;\n      const centerOffset = (center - slideOffset - slideSize / 2) / slideSize;\n      const offsetMultiplier = typeof params.modifier === 'function' ? params.modifier(centerOffset) : centerOffset * params.modifier;\n      let rotateY = isHorizontal ? rotate * offsetMultiplier : 0;\n      let rotateX = isHorizontal ? 0 : rotate * offsetMultiplier;\n      // var rotateZ = 0\n      let translateZ = -translate * Math.abs(offsetMultiplier);\n      let stretch = params.stretch;\n      // Allow percentage to make a relative stretch for responsive sliders\n      if (typeof stretch === 'string' && stretch.indexOf('%') !== -1) {\n        stretch = parseFloat(params.stretch) / 100 * slideSize;\n      }\n      let translateY = isHorizontal ? 0 : stretch * offsetMultiplier;\n      let translateX = isHorizontal ? stretch * offsetMultiplier : 0;\n      let scale = 1 - (1 - params.scale) * Math.abs(offsetMultiplier);\n\n      // Fix for ultra small values\n      if (Math.abs(translateX) < 0.001) translateX = 0;\n      if (Math.abs(translateY) < 0.001) translateY = 0;\n      if (Math.abs(translateZ) < 0.001) translateZ = 0;\n      if (Math.abs(rotateY) < 0.001) rotateY = 0;\n      if (Math.abs(rotateX) < 0.001) rotateX = 0;\n      if (Math.abs(scale) < 0.001) scale = 0;\n      const slideTransform = `translate3d(${translateX}px,${translateY}px,${translateZ}px)  rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(${scale})`;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = slideTransform;\n      slideEl.style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowBeforeEl = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n        let shadowAfterEl = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n        if (!shadowBeforeEl) {\n          shadowBeforeEl = createShadow(params, slideEl, isHorizontal ? 'left' : 'top');\n        }\n        if (!shadowAfterEl) {\n          shadowAfterEl = createShadow(params, slideEl, isHorizontal ? 'right' : 'bottom');\n        }\n        if (shadowBeforeEl) shadowBeforeEl.style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0;\n        if (shadowAfterEl) shadowAfterEl.style.opacity = -offsetMultiplier > 0 ? -offsetMultiplier : 0;\n      }\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n  };\n  effectInit({\n    effect: 'coverflow',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      watchSlidesProgress: true\n    })\n  });\n}", "import createShadow from '../../shared/create-shadow.js';\nimport effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport effectVirtualTransitionEnd from '../../shared/effect-virtual-transition-end.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\nexport default function EffectCreative({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    creativeEffect: {\n      limitProgress: 1,\n      shadowPerProgress: false,\n      progressMultiplier: 1,\n      perspective: true,\n      prev: {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        opacity: 1,\n        scale: 1\n      },\n      next: {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        opacity: 1,\n        scale: 1\n      }\n    }\n  });\n  const getTranslateValue = value => {\n    if (typeof value === 'string') return value;\n    return `${value}px`;\n  };\n  const setTranslate = () => {\n    const {\n      slides,\n      wrapperEl,\n      slidesSizesGrid\n    } = swiper;\n    const params = swiper.params.creativeEffect;\n    const {\n      progressMultiplier: multiplier\n    } = params;\n    const isCenteredSlides = swiper.params.centeredSlides;\n    if (isCenteredSlides) {\n      const margin = slidesSizesGrid[0] / 2 - swiper.params.slidesOffsetBefore || 0;\n      wrapperEl.style.transform = `translateX(calc(50% - ${margin}px))`;\n    }\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      const slideProgress = slideEl.progress;\n      const progress = Math.min(Math.max(slideEl.progress, -params.limitProgress), params.limitProgress);\n      let originalProgress = progress;\n      if (!isCenteredSlides) {\n        originalProgress = Math.min(Math.max(slideEl.originalProgress, -params.limitProgress), params.limitProgress);\n      }\n      const offset = slideEl.swiperSlideOffset;\n      const t = [swiper.params.cssMode ? -offset - swiper.translate : -offset, 0, 0];\n      const r = [0, 0, 0];\n      let custom = false;\n      if (!swiper.isHorizontal()) {\n        t[1] = t[0];\n        t[0] = 0;\n      }\n      let data = {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        scale: 1,\n        opacity: 1\n      };\n      if (progress < 0) {\n        data = params.next;\n        custom = true;\n      } else if (progress > 0) {\n        data = params.prev;\n        custom = true;\n      }\n      // set translate\n      t.forEach((value, index) => {\n        t[index] = `calc(${value}px + (${getTranslateValue(data.translate[index])} * ${Math.abs(progress * multiplier)}))`;\n      });\n      // set rotates\n      r.forEach((value, index) => {\n        r[index] = data.rotate[index] * Math.abs(progress * multiplier);\n      });\n      slideEl.style.zIndex = -Math.abs(Math.round(slideProgress)) + slides.length;\n      const translateString = t.join(', ');\n      const rotateString = `rotateX(${r[0]}deg) rotateY(${r[1]}deg) rotateZ(${r[2]}deg)`;\n      const scaleString = originalProgress < 0 ? `scale(${1 + (1 - data.scale) * originalProgress * multiplier})` : `scale(${1 - (1 - data.scale) * originalProgress * multiplier})`;\n      const opacityString = originalProgress < 0 ? 1 + (1 - data.opacity) * originalProgress * multiplier : 1 - (1 - data.opacity) * originalProgress * multiplier;\n      const transform = `translate3d(${translateString}) ${rotateString} ${scaleString}`;\n\n      // Set shadows\n      if (custom && data.shadow || !custom) {\n        let shadowEl = slideEl.querySelector('.swiper-slide-shadow');\n        if (!shadowEl && data.shadow) {\n          shadowEl = createShadow(params, slideEl);\n        }\n        if (shadowEl) {\n          const shadowOpacity = params.shadowPerProgress ? progress * (1 / params.limitProgress) : progress;\n          shadowEl.style.opacity = Math.min(Math.max(Math.abs(shadowOpacity), 0), 1);\n        }\n      }\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n      targetEl.style.opacity = opacityString;\n      if (data.origin) {\n        targetEl.style.transformOrigin = data.origin;\n      }\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements,\n      allSlides: true\n    });\n  };\n  effectInit({\n    effect: 'creative',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => swiper.params.creativeEffect.perspective,\n    overwriteParams: () => ({\n      watchSlidesProgress: true,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}", "import createShadow from '../../shared/create-shadow.js';\nimport effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport effectVirtualTransitionEnd from '../../shared/effect-virtual-transition-end.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\nexport default function EffectCards({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    cardsEffect: {\n      slideShadows: true,\n      rotate: true,\n      perSlideRotate: 2,\n      perSlideOffset: 8\n    }\n  });\n  const setTranslate = () => {\n    const {\n      slides,\n      activeIndex\n    } = swiper;\n    const params = swiper.params.cardsEffect;\n    const {\n      startTranslate,\n      isTouched\n    } = swiper.touchEventsData;\n    const currentTranslate = swiper.translate;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      const slideProgress = slideEl.progress;\n      const progress = Math.min(Math.max(slideProgress, -4), 4);\n      let offset = slideEl.swiperSlideOffset;\n      if (swiper.params.centeredSlides && !swiper.params.cssMode) {\n        swiper.wrapperEl.style.transform = `translateX(${swiper.minTranslate()}px)`;\n      }\n      if (swiper.params.centeredSlides && swiper.params.cssMode) {\n        offset -= slides[0].swiperSlideOffset;\n      }\n      let tX = swiper.params.cssMode ? -offset - swiper.translate : -offset;\n      let tY = 0;\n      const tZ = -100 * Math.abs(progress);\n      let scale = 1;\n      let rotate = -params.perSlideRotate * progress;\n      let tXAdd = params.perSlideOffset - Math.abs(progress) * 0.75;\n      const slideIndex = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.from + i : i;\n      const isSwipeToNext = (slideIndex === activeIndex || slideIndex === activeIndex - 1) && progress > 0 && progress < 1 && (isTouched || swiper.params.cssMode) && currentTranslate < startTranslate;\n      const isSwipeToPrev = (slideIndex === activeIndex || slideIndex === activeIndex + 1) && progress < 0 && progress > -1 && (isTouched || swiper.params.cssMode) && currentTranslate > startTranslate;\n      if (isSwipeToNext || isSwipeToPrev) {\n        const subProgress = (1 - Math.abs((Math.abs(progress) - 0.5) / 0.5)) ** 0.5;\n        rotate += -28 * progress * subProgress;\n        scale += -0.5 * subProgress;\n        tXAdd += 96 * subProgress;\n        tY = `${-25 * subProgress * Math.abs(progress)}%`;\n      }\n      if (progress < 0) {\n        // next\n        tX = `calc(${tX}px + (${tXAdd * Math.abs(progress)}%))`;\n      } else if (progress > 0) {\n        // prev\n        tX = `calc(${tX}px + (-${tXAdd * Math.abs(progress)}%))`;\n      } else {\n        tX = `${tX}px`;\n      }\n      if (!swiper.isHorizontal()) {\n        const prevY = tY;\n        tY = tX;\n        tX = prevY;\n      }\n      const scaleString = progress < 0 ? `${1 + (1 - scale) * progress}` : `${1 - (1 - scale) * progress}`;\n      const transform = `\n        translate3d(${tX}, ${tY}, ${tZ}px)\n        rotateZ(${params.rotate ? rotate : 0}deg)\n        scale(${scaleString})\n      `;\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowEl = slideEl.querySelector('.swiper-slide-shadow');\n        if (!shadowEl) {\n          shadowEl = createShadow(params, slideEl);\n        }\n        if (shadowEl) shadowEl.style.opacity = Math.min(Math.max((Math.abs(progress) - 0.5) / 0.5, 0), 1);\n      }\n      slideEl.style.zIndex = -Math.abs(Math.round(slideProgress)) + slides.length;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements\n    });\n  };\n  effectInit({\n    effect: 'cards',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      watchSlidesProgress: true,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}"], "mappings": ";AAYA,SAAS,SAAS,KAAK;AACnB,SAAQ,QAAQ,QACZ,OAAO,QAAQ,YACf,iBAAiB,OACjB,IAAI,gBAAgB;AAC5B;AACA,SAAS,OAAO,SAAS,CAAC,GAAG,MAAM,CAAC,GAAG;AACnC,SAAO,KAAK,GAAG,EAAE,QAAQ,CAAC,QAAQ;AAC9B,QAAI,OAAO,OAAO,GAAG,MAAM;AACvB,aAAO,GAAG,IAAI,IAAI,GAAG;AAAA,aAChB,SAAS,IAAI,GAAG,CAAC,KACtB,SAAS,OAAO,GAAG,CAAC,KACpB,OAAO,KAAK,IAAI,GAAG,CAAC,EAAE,SAAS,GAAG;AAClC,aAAO,OAAO,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAChC;AAAA,EACJ,CAAC;AACL;AAEA,IAAM,cAAc;AAAA,EAChB,MAAM,CAAC;AAAA,EACP,mBAAmB;AAAA,EAAE;AAAA,EACrB,sBAAsB;AAAA,EAAE;AAAA,EACxB,eAAe;AAAA,IACX,OAAO;AAAA,IAAE;AAAA,IACT,UAAU;AAAA,EACd;AAAA,EACA,gBAAgB;AACZ,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB;AACf,WAAO,CAAC;AAAA,EACZ;AAAA,EACA,iBAAiB;AACb,WAAO;AAAA,EACX;AAAA,EACA,cAAc;AACV,WAAO;AAAA,MACH,YAAY;AAAA,MAAE;AAAA,IAClB;AAAA,EACJ;AAAA,EACA,gBAAgB;AACZ,WAAO;AAAA,MACH,UAAU,CAAC;AAAA,MACX,YAAY,CAAC;AAAA,MACb,OAAO,CAAC;AAAA,MACR,eAAe;AAAA,MAAE;AAAA,MACjB,uBAAuB;AACnB,eAAO,CAAC;AAAA,MACZ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,kBAAkB;AACd,WAAO,CAAC;AAAA,EACZ;AAAA,EACA,aAAa;AACT,WAAO;AAAA,EACX;AAAA,EACA,UAAU;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,EACZ;AACJ;AACA,SAAS,cAAc;AACnB,QAAM,MAAM,OAAO,aAAa,cAAc,WAAW,CAAC;AAC1D,SAAO,KAAK,WAAW;AACvB,SAAO;AACX;AAEA,IAAM,YAAY;AAAA,EACd,UAAU;AAAA,EACV,WAAW;AAAA,IACP,WAAW;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,EACZ;AAAA,EACA,SAAS;AAAA,IACL,eAAe;AAAA,IAAE;AAAA,IACjB,YAAY;AAAA,IAAE;AAAA,IACd,KAAK;AAAA,IAAE;AAAA,IACP,OAAO;AAAA,IAAE;AAAA,EACb;AAAA,EACA,aAAa,SAAS,cAAc;AAChC,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB;AAAA,EAAE;AAAA,EACrB,sBAAsB;AAAA,EAAE;AAAA,EACxB,mBAAmB;AACf,WAAO;AAAA,MACH,mBAAmB;AACf,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EAAE;AAAA,EACV,OAAO;AAAA,EAAE;AAAA,EACT,QAAQ,CAAC;AAAA,EACT,aAAa;AAAA,EAAE;AAAA,EACf,eAAe;AAAA,EAAE;AAAA,EACjB,aAAa;AACT,WAAO,CAAC;AAAA,EACZ;AAAA,EACA,sBAAsB,UAAU;AAC5B,QAAI,OAAO,eAAe,aAAa;AACnC,eAAS;AACT,aAAO;AAAA,IACX;AACA,WAAO,WAAW,UAAU,CAAC;AAAA,EACjC;AAAA,EACA,qBAAqB,IAAI;AACrB,QAAI,OAAO,eAAe,aAAa;AACnC;AAAA,IACJ;AACA,iBAAa,EAAE;AAAA,EACnB;AACJ;AACA,SAAS,YAAY;AACjB,QAAM,MAAM,OAAO,WAAW,cAAc,SAAS,CAAC;AACtD,SAAO,KAAK,SAAS;AACrB,SAAO;AACX;;;AChJA,SAAS,YAAY,KAAK;AACxB,QAAM,SAAS;AACf,SAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AACjC,QAAI;AACF,aAAO,GAAG,IAAI;AAAA,IAChB,SAAS,GAAP;AAAA,IAEF;AACA,QAAI;AACF,aAAO,OAAO,GAAG;AAAA,IACnB,SAAS,GAAP;AAAA,IAEF;AAAA,EACF,CAAC;AACH;AACA,SAAS,SAAS,UAAU,QAAQ,GAAG;AACrC,SAAO,WAAW,UAAU,KAAK;AACnC;AACA,SAAS,MAAM;AACb,SAAO,KAAK,IAAI;AAClB;AACA,SAASA,kBAAiB,IAAI;AAC5B,QAAMC,UAAS,UAAU;AACzB,MAAI;AACJ,MAAIA,QAAO,kBAAkB;AAC3B,YAAQA,QAAO,iBAAiB,IAAI,IAAI;AAAA,EAC1C;AACA,MAAI,CAAC,SAAS,GAAG,cAAc;AAC7B,YAAQ,GAAG;AAAA,EACb;AACA,MAAI,CAAC,OAAO;AACV,YAAQ,GAAG;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,aAAa,IAAI,OAAO,KAAK;AACpC,QAAMA,UAAS,UAAU;AACzB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,WAAWD,kBAAiB,IAAI,IAAI;AAC1C,MAAIC,QAAO,iBAAiB;AAC1B,mBAAe,SAAS,aAAa,SAAS;AAC9C,QAAI,aAAa,MAAM,GAAG,EAAE,SAAS,GAAG;AACtC,qBAAe,aAAa,MAAM,IAAI,EAAE,IAAI,OAAK,EAAE,QAAQ,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI;AAAA,IACjF;AAGA,sBAAkB,IAAIA,QAAO,gBAAgB,iBAAiB,SAAS,KAAK,YAAY;AAAA,EAC1F,OAAO;AACL,sBAAkB,SAAS,gBAAgB,SAAS,cAAc,SAAS,eAAe,SAAS,eAAe,SAAS,aAAa,SAAS,iBAAiB,WAAW,EAAE,QAAQ,cAAc,oBAAoB;AACzN,aAAS,gBAAgB,SAAS,EAAE,MAAM,GAAG;AAAA,EAC/C;AACA,MAAI,SAAS,KAAK;AAEhB,QAAIA,QAAO;AAAiB,qBAAe,gBAAgB;AAAA,aAElD,OAAO,WAAW;AAAI,qBAAe,WAAW,OAAO,EAAE,CAAC;AAAA;AAE9D,qBAAe,WAAW,OAAO,CAAC,CAAC;AAAA,EAC1C;AACA,MAAI,SAAS,KAAK;AAEhB,QAAIA,QAAO;AAAiB,qBAAe,gBAAgB;AAAA,aAElD,OAAO,WAAW;AAAI,qBAAe,WAAW,OAAO,EAAE,CAAC;AAAA;AAE9D,qBAAe,WAAW,OAAO,CAAC,CAAC;AAAA,EAC1C;AACA,SAAO,gBAAgB;AACzB;AACA,SAASC,UAAS,GAAG;AACnB,SAAO,OAAO,MAAM,YAAY,MAAM,QAAQ,EAAE,eAAe,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE,MAAM;AACpH;AACA,SAAS,OAAO,MAAM;AAEpB,MAAI,OAAO,WAAW,eAAe,OAAO,OAAO,gBAAgB,aAAa;AAC9E,WAAO,gBAAgB;AAAA,EACzB;AACA,SAAO,SAAS,KAAK,aAAa,KAAK,KAAK,aAAa;AAC3D;AACA,SAASC,WAAU,MAAM;AACvB,QAAM,KAAK,OAAO,KAAK,CAAC,CAAC;AACzB,QAAM,WAAW,CAAC,aAAa,eAAe,WAAW;AACzD,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,UAAM,aAAa,KAAK,CAAC;AACzB,QAAI,eAAe,UAAa,eAAe,QAAQ,CAAC,OAAO,UAAU,GAAG;AAC1E,YAAM,YAAY,OAAO,KAAK,OAAO,UAAU,CAAC,EAAE,OAAO,SAAO,SAAS,QAAQ,GAAG,IAAI,CAAC;AACzF,eAAS,YAAY,GAAG,MAAM,UAAU,QAAQ,YAAY,KAAK,aAAa,GAAG;AAC/E,cAAM,UAAU,UAAU,SAAS;AACnC,cAAM,OAAO,OAAO,yBAAyB,YAAY,OAAO;AAChE,YAAI,SAAS,UAAa,KAAK,YAAY;AACzC,cAAID,UAAS,GAAG,OAAO,CAAC,KAAKA,UAAS,WAAW,OAAO,CAAC,GAAG;AAC1D,gBAAI,WAAW,OAAO,EAAE,YAAY;AAClC,iBAAG,OAAO,IAAI,WAAW,OAAO;AAAA,YAClC,OAAO;AACL,cAAAC,QAAO,GAAG,OAAO,GAAG,WAAW,OAAO,CAAC;AAAA,YACzC;AAAA,UACF,WAAW,CAACD,UAAS,GAAG,OAAO,CAAC,KAAKA,UAAS,WAAW,OAAO,CAAC,GAAG;AAClE,eAAG,OAAO,IAAI,CAAC;AACf,gBAAI,WAAW,OAAO,EAAE,YAAY;AAClC,iBAAG,OAAO,IAAI,WAAW,OAAO;AAAA,YAClC,OAAO;AACL,cAAAC,QAAO,GAAG,OAAO,GAAG,WAAW,OAAO,CAAC;AAAA,YACzC;AAAA,UACF,OAAO;AACL,eAAG,OAAO,IAAI,WAAW,OAAO;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,IAAI,SAAS,UAAU;AAC7C,KAAG,MAAM,YAAY,SAAS,QAAQ;AACxC;AACA,SAAS,qBAAqB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAMF,UAAS,UAAU;AACzB,QAAM,gBAAgB,CAAC,OAAO;AAC9B,MAAI,YAAY;AAChB,MAAI;AACJ,QAAM,WAAW,OAAO,OAAO;AAC/B,SAAO,UAAU,MAAM,iBAAiB;AACxC,EAAAA,QAAO,qBAAqB,OAAO,cAAc;AACjD,QAAM,MAAM,iBAAiB,gBAAgB,SAAS;AACtD,QAAM,eAAe,CAAC,SAAS,WAAW;AACxC,WAAO,QAAQ,UAAU,WAAW,UAAU,QAAQ,UAAU,WAAW;AAAA,EAC7E;AACA,QAAM,UAAU,MAAM;AACpB,YAAO,oBAAI,KAAK,GAAE,QAAQ;AAC1B,QAAI,cAAc,MAAM;AACtB,kBAAY;AAAA,IACd;AACA,UAAM,WAAW,KAAK,IAAI,KAAK,KAAK,OAAO,aAAa,UAAU,CAAC,GAAG,CAAC;AACvE,UAAM,eAAe,MAAM,KAAK,IAAI,WAAW,KAAK,EAAE,IAAI;AAC1D,QAAI,kBAAkB,gBAAgB,gBAAgB,iBAAiB;AACvE,QAAI,aAAa,iBAAiB,cAAc,GAAG;AACjD,wBAAkB;AAAA,IACpB;AACA,WAAO,UAAU,SAAS;AAAA,MACxB,CAAC,IAAI,GAAG;AAAA,IACV,CAAC;AACD,QAAI,aAAa,iBAAiB,cAAc,GAAG;AACjD,aAAO,UAAU,MAAM,WAAW;AAClC,aAAO,UAAU,MAAM,iBAAiB;AACxC,iBAAW,MAAM;AACf,eAAO,UAAU,MAAM,WAAW;AAClC,eAAO,UAAU,SAAS;AAAA,UACxB,CAAC,IAAI,GAAG;AAAA,QACV,CAAC;AAAA,MACH,CAAC;AACD,MAAAA,QAAO,qBAAqB,OAAO,cAAc;AACjD;AAAA,IACF;AACA,WAAO,iBAAiBA,QAAO,sBAAsB,OAAO;AAAA,EAC9D;AACA,UAAQ;AACV;AACA,SAAS,oBAAoB,SAAS;AACpC,SAAO,QAAQ,cAAc,yBAAyB,KAAK,QAAQ,YAAY,QAAQ,SAAS,cAAc,yBAAyB,KAAK;AAC9I;AAQA,SAAS,gBAAgB,SAAS,WAAW,IAAI;AAC/C,SAAO,CAAC,GAAG,QAAQ,QAAQ,EAAE,OAAO,QAAM,GAAG,QAAQ,QAAQ,CAAC;AAChE;AACA,SAAS,cAAc,KAAK,UAAU,CAAC,GAAG;AACxC,QAAM,KAAK,SAAS,cAAc,GAAG;AACrC,KAAG,UAAU,IAAI,GAAI,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO,CAAE;AAClE,SAAO;AACT;AACA,SAAS,cAAc,IAAI;AACzB,QAAMG,UAAS,UAAU;AACzB,QAAMC,YAAW,YAAY;AAC7B,QAAM,MAAM,GAAG,sBAAsB;AACrC,QAAM,OAAOA,UAAS;AACtB,QAAM,YAAY,GAAG,aAAa,KAAK,aAAa;AACpD,QAAM,aAAa,GAAG,cAAc,KAAK,cAAc;AACvD,QAAM,YAAY,OAAOD,UAASA,QAAO,UAAU,GAAG;AACtD,QAAM,aAAa,OAAOA,UAASA,QAAO,UAAU,GAAG;AACvD,SAAO;AAAA,IACL,KAAK,IAAI,MAAM,YAAY;AAAA,IAC3B,MAAM,IAAI,OAAO,aAAa;AAAA,EAChC;AACF;AACA,SAAS,eAAe,IAAI,UAAU;AACpC,QAAM,UAAU,CAAC;AACjB,SAAO,GAAG,wBAAwB;AAChC,UAAM,OAAO,GAAG;AAChB,QAAI,UAAU;AACZ,UAAI,KAAK,QAAQ,QAAQ;AAAG,gBAAQ,KAAK,IAAI;AAAA,IAC/C;AAAO,cAAQ,KAAK,IAAI;AACxB,SAAK;AAAA,EACP;AACA,SAAO;AACT;AACA,SAAS,eAAe,IAAI,UAAU;AACpC,QAAM,UAAU,CAAC;AACjB,SAAO,GAAG,oBAAoB;AAC5B,UAAM,OAAO,GAAG;AAChB,QAAI,UAAU;AACZ,UAAI,KAAK,QAAQ,QAAQ;AAAG,gBAAQ,KAAK,IAAI;AAAA,IAC/C;AAAO,cAAQ,KAAK,IAAI;AACxB,SAAK;AAAA,EACP;AACA,SAAO;AACT;AACA,SAAS,aAAa,IAAI,MAAM;AAC9B,QAAMA,UAAS,UAAU;AACzB,SAAOA,QAAO,iBAAiB,IAAI,IAAI,EAAE,iBAAiB,IAAI;AAChE;AACA,SAAS,aAAa,IAAI;AACxB,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI,OAAO;AACT,QAAI;AAEJ,YAAQ,QAAQ,MAAM,qBAAqB,MAAM;AAC/C,UAAI,MAAM,aAAa;AAAG,aAAK;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,eAAe,IAAI,UAAU;AACpC,QAAM,UAAU,CAAC;AACjB,MAAI,SAAS,GAAG;AAChB,SAAO,QAAQ;AACb,QAAI,UAAU;AACZ,UAAI,OAAO,QAAQ,QAAQ;AAAG,gBAAQ,KAAK,MAAM;AAAA,IACnD,OAAO;AACL,cAAQ,KAAK,MAAM;AAAA,IACrB;AACA,aAAS,OAAO;AAAA,EAClB;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,IAAI,UAAU;AAC1C,WAAS,aAAa,GAAG;AACvB,QAAI,EAAE,WAAW;AAAI;AACrB,aAAS,KAAK,IAAI,CAAC;AACnB,OAAG,oBAAoB,iBAAiB,YAAY;AAAA,EACtD;AACA,MAAI,UAAU;AACZ,OAAG,iBAAiB,iBAAiB,YAAY;AAAA,EACnD;AACF;AACA,SAAS,iBAAiB,IAAI,MAAM,gBAAgB;AAClD,QAAMA,UAAS,UAAU;AACzB,MAAI,gBAAgB;AAClB,WAAO,GAAG,SAAS,UAAU,gBAAgB,cAAc,IAAI,WAAWA,QAAO,iBAAiB,IAAI,IAAI,EAAE,iBAAiB,SAAS,UAAU,iBAAiB,YAAY,CAAC,IAAI,WAAWA,QAAO,iBAAiB,IAAI,IAAI,EAAE,iBAAiB,SAAS,UAAU,gBAAgB,eAAe,CAAC;AAAA,EACrS;AACA,SAAO,GAAG;AACZ;;;ACvQA,IAAI;AACJ,SAAS,cAAc;AACrB,QAAME,UAAS,UAAU;AACzB,QAAMC,YAAW,YAAY;AAC7B,SAAO;AAAA,IACL,cAAcA,UAAS,mBAAmB,oBAAoBA,UAAS,gBAAgB;AAAA,IACvF,OAAO,CAAC,EAAE,kBAAkBD,WAAUA,QAAO,iBAAiBC,qBAAoBD,QAAO;AAAA,EAC3F;AACF;AACA,SAAS,aAAa;AACpB,MAAI,CAAC,SAAS;AACZ,cAAU,YAAY;AAAA,EACxB;AACA,SAAO;AACT;;;ACbA,IAAI;AACJ,SAAS,WAAW;AAAA,EAClB;AACF,IAAI,CAAC,GAAG;AACN,QAAME,WAAU,WAAW;AAC3B,QAAMC,UAAS,UAAU;AACzB,QAAM,WAAWA,QAAO,UAAU;AAClC,QAAM,KAAK,aAAaA,QAAO,UAAU;AACzC,QAAM,SAAS;AAAA,IACb,KAAK;AAAA,IACL,SAAS;AAAA,EACX;AACA,QAAM,cAAcA,QAAO,OAAO;AAClC,QAAM,eAAeA,QAAO,OAAO;AACnC,QAAM,UAAU,GAAG,MAAM,6BAA6B;AACtD,MAAI,OAAO,GAAG,MAAM,sBAAsB;AAC1C,QAAM,OAAO,GAAG,MAAM,yBAAyB;AAC/C,QAAM,SAAS,CAAC,QAAQ,GAAG,MAAM,4BAA4B;AAC7D,QAAM,UAAU,aAAa;AAC7B,MAAI,QAAQ,aAAa;AAGzB,QAAM,cAAc,CAAC,aAAa,aAAa,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,UAAU;AACrK,MAAI,CAAC,QAAQ,SAASD,SAAQ,SAAS,YAAY,QAAQ,GAAG,eAAe,cAAc,KAAK,GAAG;AACjG,WAAO,GAAG,MAAM,qBAAqB;AACrC,QAAI,CAAC;AAAM,aAAO,CAAC,GAAG,GAAG,QAAQ;AACjC,YAAQ;AAAA,EACV;AAGA,MAAI,WAAW,CAAC,SAAS;AACvB,WAAO,KAAK;AACZ,WAAO,UAAU;AAAA,EACnB;AACA,MAAI,QAAQ,UAAU,MAAM;AAC1B,WAAO,KAAK;AACZ,WAAO,MAAM;AAAA,EACf;AAGA,SAAO;AACT;AACA,SAAS,UAAU,YAAY,CAAC,GAAG;AACjC,MAAI,CAAC,cAAc;AACjB,mBAAe,WAAW,SAAS;AAAA,EACrC;AACA,SAAO;AACT;;;AChDA,IAAI;AACJ,SAAS,cAAc;AACrB,QAAME,UAAS,UAAU;AACzB,MAAI,qBAAqB;AACzB,WAAS,WAAW;AAClB,UAAM,KAAKA,QAAO,UAAU,UAAU,YAAY;AAClD,WAAO,GAAG,QAAQ,QAAQ,KAAK,KAAK,GAAG,QAAQ,QAAQ,IAAI,KAAK,GAAG,QAAQ,SAAS,IAAI;AAAA,EAC1F;AACA,MAAI,SAAS,GAAG;AACd,UAAM,KAAK,OAAOA,QAAO,UAAU,SAAS;AAC5C,QAAI,GAAG,SAAS,UAAU,GAAG;AAC3B,YAAM,CAAC,OAAO,KAAK,IAAI,GAAG,MAAM,UAAU,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,SAAO,OAAO,GAAG,CAAC;AAC9F,2BAAqB,QAAQ,MAAM,UAAU,MAAM,QAAQ;AAAA,IAC7D;AAAA,EACF;AACA,SAAO;AAAA,IACL,UAAU,sBAAsB,SAAS;AAAA,IACzC;AAAA,IACA,WAAW,+CAA+C,KAAKA,QAAO,UAAU,SAAS;AAAA,EAC3F;AACF;AACA,SAAS,aAAa;AACpB,MAAI,CAAC,SAAS;AACZ,cAAU,YAAY;AAAA,EACxB;AACA,SAAO;AACT;;;AC1Be,SAAR,OAAwB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAMC,UAAS,UAAU;AACzB,MAAI,WAAW;AACf,MAAI,iBAAiB;AACrB,QAAM,gBAAgB,MAAM;AAC1B,QAAI,CAAC,UAAU,OAAO,aAAa,CAAC,OAAO;AAAa;AACxD,SAAK,cAAc;AACnB,SAAK,QAAQ;AAAA,EACf;AACA,QAAM,iBAAiB,MAAM;AAC3B,QAAI,CAAC,UAAU,OAAO,aAAa,CAAC,OAAO;AAAa;AACxD,eAAW,IAAI,eAAe,aAAW;AACvC,uBAAiBA,QAAO,sBAAsB,MAAM;AAClD,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,WAAW;AACf,YAAI,YAAY;AAChB,gBAAQ,QAAQ,CAAC;AAAA,UACf;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM;AACJ,cAAI,UAAU,WAAW,OAAO;AAAI;AACpC,qBAAW,cAAc,YAAY,SAAS,eAAe,CAAC,KAAK,gBAAgB;AACnF,sBAAY,cAAc,YAAY,UAAU,eAAe,CAAC,KAAK,gBAAgB;AAAA,QACvF,CAAC;AACD,YAAI,aAAa,SAAS,cAAc,QAAQ;AAC9C,wBAAc;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,aAAS,QAAQ,OAAO,EAAE;AAAA,EAC5B;AACA,QAAM,iBAAiB,MAAM;AAC3B,QAAI,gBAAgB;AAClB,MAAAA,QAAO,qBAAqB,cAAc;AAAA,IAC5C;AACA,QAAI,YAAY,SAAS,aAAa,OAAO,IAAI;AAC/C,eAAS,UAAU,OAAO,EAAE;AAC5B,iBAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,2BAA2B,MAAM;AACrC,QAAI,CAAC,UAAU,OAAO,aAAa,CAAC,OAAO;AAAa;AACxD,SAAK,mBAAmB;AAAA,EAC1B;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,kBAAkB,OAAOA,QAAO,mBAAmB,aAAa;AAChF,qBAAe;AACf;AAAA,IACF;AACA,IAAAA,QAAO,iBAAiB,UAAU,aAAa;AAC/C,IAAAA,QAAO,iBAAiB,qBAAqB,wBAAwB;AAAA,EACvE,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,mBAAe;AACf,IAAAA,QAAO,oBAAoB,UAAU,aAAa;AAClD,IAAAA,QAAO,oBAAoB,qBAAqB,wBAAwB;AAAA,EAC1E,CAAC;AACH;;;AChEe,SAAR,SAA0B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,YAAY,CAAC;AACnB,QAAMC,UAAS,UAAU;AACzB,QAAM,SAAS,CAAC,QAAQ,UAAU,CAAC,MAAM;AACvC,UAAM,eAAeA,QAAO,oBAAoBA,QAAO;AACvD,UAAM,WAAW,IAAI,aAAa,eAAa;AAI7C,UAAI,OAAO;AAAqB;AAChC,UAAI,UAAU,WAAW,GAAG;AAC1B,aAAK,kBAAkB,UAAU,CAAC,CAAC;AACnC;AAAA,MACF;AACA,YAAM,iBAAiB,SAASC,kBAAiB;AAC/C,aAAK,kBAAkB,UAAU,CAAC,CAAC;AAAA,MACrC;AACA,UAAID,QAAO,uBAAuB;AAChC,QAAAA,QAAO,sBAAsB,cAAc;AAAA,MAC7C,OAAO;AACL,QAAAA,QAAO,WAAW,gBAAgB,CAAC;AAAA,MACrC;AAAA,IACF,CAAC;AACD,aAAS,QAAQ,QAAQ;AAAA,MACvB,YAAY,OAAO,QAAQ,eAAe,cAAc,OAAO,QAAQ;AAAA,MACvE,WAAW,OAAO,QAAQ,cAAc,cAAc,OAAO,QAAQ;AAAA,MACrE,eAAe,OAAO,QAAQ,kBAAkB,cAAc,OAAO,QAAQ;AAAA,IAC/E,CAAC;AACD,cAAU,KAAK,QAAQ;AAAA,EACzB;AACA,QAAM,OAAO,MAAM;AACjB,QAAI,CAAC,OAAO,OAAO;AAAU;AAC7B,QAAI,OAAO,OAAO,gBAAgB;AAChC,YAAM,mBAAmB,eAAe,OAAO,EAAE;AACjD,eAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK,GAAG;AACnD,eAAO,iBAAiB,CAAC,CAAC;AAAA,MAC5B;AAAA,IACF;AAEA,WAAO,OAAO,IAAI;AAAA,MAChB,WAAW,OAAO,OAAO;AAAA,IAC3B,CAAC;AAGD,WAAO,OAAO,WAAW;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACA,QAAM,UAAU,MAAM;AACpB,cAAU,QAAQ,cAAY;AAC5B,eAAS,WAAW;AAAA,IACtB,CAAC;AACD,cAAU,OAAO,GAAG,UAAU,MAAM;AAAA,EACtC;AACA,eAAa;AAAA,IACX,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,sBAAsB;AAAA,EACxB,CAAC;AACD,KAAG,QAAQ,IAAI;AACf,KAAG,WAAW,OAAO;AACvB;;;AClEA,IAAO,yBAAQ;AAAA,EACb,GAAGE,SAAQ,SAAS,UAAU;AAC5B,UAAM,OAAO;AACb,QAAI,CAAC,KAAK,mBAAmB,KAAK;AAAW,aAAO;AACpD,QAAI,OAAO,YAAY;AAAY,aAAO;AAC1C,UAAM,SAAS,WAAW,YAAY;AACtC,IAAAA,QAAO,MAAM,GAAG,EAAE,QAAQ,CAAAC,WAAS;AACjC,UAAI,CAAC,KAAK,gBAAgBA,MAAK;AAAG,aAAK,gBAAgBA,MAAK,IAAI,CAAC;AACjE,WAAK,gBAAgBA,MAAK,EAAE,MAAM,EAAE,OAAO;AAAA,IAC7C,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,KAAKD,SAAQ,SAAS,UAAU;AAC9B,UAAM,OAAO;AACb,QAAI,CAAC,KAAK,mBAAmB,KAAK;AAAW,aAAO;AACpD,QAAI,OAAO,YAAY;AAAY,aAAO;AAC1C,aAAS,eAAe,MAAM;AAC5B,WAAK,IAAIA,SAAQ,WAAW;AAC5B,UAAI,YAAY,gBAAgB;AAC9B,eAAO,YAAY;AAAA,MACrB;AACA,cAAQ,MAAM,MAAM,IAAI;AAAA,IAC1B;AACA,gBAAY,iBAAiB;AAC7B,WAAO,KAAK,GAAGA,SAAQ,aAAa,QAAQ;AAAA,EAC9C;AAAA,EACA,MAAM,SAAS,UAAU;AACvB,UAAM,OAAO;AACb,QAAI,CAAC,KAAK,mBAAmB,KAAK;AAAW,aAAO;AACpD,QAAI,OAAO,YAAY;AAAY,aAAO;AAC1C,UAAM,SAAS,WAAW,YAAY;AACtC,QAAI,KAAK,mBAAmB,QAAQ,OAAO,IAAI,GAAG;AAChD,WAAK,mBAAmB,MAAM,EAAE,OAAO;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAAS;AACd,UAAM,OAAO;AACb,QAAI,CAAC,KAAK,mBAAmB,KAAK;AAAW,aAAO;AACpD,QAAI,CAAC,KAAK;AAAoB,aAAO;AACrC,UAAM,QAAQ,KAAK,mBAAmB,QAAQ,OAAO;AACrD,QAAI,SAAS,GAAG;AACd,WAAK,mBAAmB,OAAO,OAAO,CAAC;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAIA,SAAQ,SAAS;AACnB,UAAM,OAAO;AACb,QAAI,CAAC,KAAK,mBAAmB,KAAK;AAAW,aAAO;AACpD,QAAI,CAAC,KAAK;AAAiB,aAAO;AAClC,IAAAA,QAAO,MAAM,GAAG,EAAE,QAAQ,CAAAC,WAAS;AACjC,UAAI,OAAO,YAAY,aAAa;AAClC,aAAK,gBAAgBA,MAAK,IAAI,CAAC;AAAA,MACjC,WAAW,KAAK,gBAAgBA,MAAK,GAAG;AACtC,aAAK,gBAAgBA,MAAK,EAAE,QAAQ,CAAC,cAAc,UAAU;AAC3D,cAAI,iBAAiB,WAAW,aAAa,kBAAkB,aAAa,mBAAmB,SAAS;AACtG,iBAAK,gBAAgBA,MAAK,EAAE,OAAO,OAAO,CAAC;AAAA,UAC7C;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,MAAM;AACZ,UAAM,OAAO;AACb,QAAI,CAAC,KAAK,mBAAmB,KAAK;AAAW,aAAO;AACpD,QAAI,CAAC,KAAK;AAAiB,aAAO;AAClC,QAAID;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,OAAO,KAAK,CAAC,MAAM,YAAY,MAAM,QAAQ,KAAK,CAAC,CAAC,GAAG;AACzD,MAAAA,UAAS,KAAK,CAAC;AACf,aAAO,KAAK,MAAM,GAAG,KAAK,MAAM;AAChC,gBAAU;AAAA,IACZ,OAAO;AACL,MAAAA,UAAS,KAAK,CAAC,EAAE;AACjB,aAAO,KAAK,CAAC,EAAE;AACf,gBAAU,KAAK,CAAC,EAAE,WAAW;AAAA,IAC/B;AACA,SAAK,QAAQ,OAAO;AACpB,UAAM,cAAc,MAAM,QAAQA,OAAM,IAAIA,UAASA,QAAO,MAAM,GAAG;AACrE,gBAAY,QAAQ,CAAAC,WAAS;AAC3B,UAAI,KAAK,sBAAsB,KAAK,mBAAmB,QAAQ;AAC7D,aAAK,mBAAmB,QAAQ,kBAAgB;AAC9C,uBAAa,MAAM,SAAS,CAACA,QAAO,GAAG,IAAI,CAAC;AAAA,QAC9C,CAAC;AAAA,MACH;AACA,UAAI,KAAK,mBAAmB,KAAK,gBAAgBA,MAAK,GAAG;AACvD,aAAK,gBAAgBA,MAAK,EAAE,QAAQ,kBAAgB;AAClD,uBAAa,MAAM,SAAS,IAAI;AAAA,QAClC,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACF;;;AChGe,SAAR,aAA8B;AACnC,QAAM,SAAS;AACf,MAAI;AACJ,MAAI;AACJ,QAAM,KAAK,OAAO;AAClB,MAAI,OAAO,OAAO,OAAO,UAAU,eAAe,OAAO,OAAO,UAAU,MAAM;AAC9E,YAAQ,OAAO,OAAO;AAAA,EACxB,OAAO;AACL,YAAQ,GAAG;AAAA,EACb;AACA,MAAI,OAAO,OAAO,OAAO,WAAW,eAAe,OAAO,OAAO,WAAW,MAAM;AAChF,aAAS,OAAO,OAAO;AAAA,EACzB,OAAO;AACL,aAAS,GAAG;AAAA,EACd;AACA,MAAI,UAAU,KAAK,OAAO,aAAa,KAAK,WAAW,KAAK,OAAO,WAAW,GAAG;AAC/E;AAAA,EACF;AAGA,UAAQ,QAAQ,SAAS,aAAa,IAAI,cAAc,KAAK,GAAG,EAAE,IAAI,SAAS,aAAa,IAAI,eAAe,KAAK,GAAG,EAAE;AACzH,WAAS,SAAS,SAAS,aAAa,IAAI,aAAa,KAAK,GAAG,EAAE,IAAI,SAAS,aAAa,IAAI,gBAAgB,KAAK,GAAG,EAAE;AAC3H,MAAI,OAAO,MAAM,KAAK;AAAG,YAAQ;AACjC,MAAI,OAAO,MAAM,MAAM;AAAG,aAAS;AACnC,SAAO,OAAO,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,IACA,MAAM,OAAO,aAAa,IAAI,QAAQ;AAAA,EACxC,CAAC;AACH;;;AC7Be,SAAR,eAAgC;AACrC,QAAM,SAAS;AACf,WAAS,kBAAkB,UAAU;AACnC,QAAI,OAAO,aAAa,GAAG;AACzB,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,MACL,SAAS;AAAA,MACT,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,IACjB,EAAE,QAAQ;AAAA,EACZ;AACA,WAAS,0BAA0B,MAAM,OAAO;AAC9C,WAAO,WAAW,KAAK,iBAAiB,kBAAkB,KAAK,CAAC,KAAK,CAAC;AAAA,EACxE;AACA,QAAM,SAAS,OAAO;AACtB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,cAAc;AAAA,IACd;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,OAAO,WAAW,OAAO,QAAQ;AACnD,QAAM,uBAAuB,YAAY,OAAO,QAAQ,OAAO,SAAS,OAAO,OAAO;AACtF,QAAM,SAAS,gBAAgB,UAAU,IAAI,OAAO,OAAO,0BAA0B;AACrF,QAAM,eAAe,YAAY,OAAO,QAAQ,OAAO,SAAS,OAAO;AACvE,MAAI,WAAW,CAAC;AAChB,QAAM,aAAa,CAAC;AACpB,QAAM,kBAAkB,CAAC;AACzB,MAAI,eAAe,OAAO;AAC1B,MAAI,OAAO,iBAAiB,YAAY;AACtC,mBAAe,OAAO,mBAAmB,KAAK,MAAM;AAAA,EACtD;AACA,MAAI,cAAc,OAAO;AACzB,MAAI,OAAO,gBAAgB,YAAY;AACrC,kBAAc,OAAO,kBAAkB,KAAK,MAAM;AAAA,EACpD;AACA,QAAM,yBAAyB,OAAO,SAAS;AAC/C,QAAM,2BAA2B,OAAO,WAAW;AACnD,MAAI,eAAe,OAAO;AAC1B,MAAI,gBAAgB,CAAC;AACrB,MAAI,gBAAgB;AACpB,MAAI,QAAQ;AACZ,MAAI,OAAO,eAAe,aAAa;AACrC;AAAA,EACF;AACA,MAAI,OAAO,iBAAiB,YAAY,aAAa,QAAQ,GAAG,KAAK,GAAG;AACtE,mBAAe,WAAW,aAAa,QAAQ,KAAK,EAAE,CAAC,IAAI,MAAM;AAAA,EACnE;AACA,SAAO,cAAc,CAAC;AAGtB,SAAO,QAAQ,aAAW;AACxB,QAAI,KAAK;AACP,cAAQ,MAAM,aAAa;AAAA,IAC7B,OAAO;AACL,cAAQ,MAAM,cAAc;AAAA,IAC9B;AACA,YAAQ,MAAM,eAAe;AAC7B,YAAQ,MAAM,YAAY;AAAA,EAC5B,CAAC;AAGD,MAAI,OAAO,kBAAkB,OAAO,SAAS;AAC3C,mBAAe,WAAW,mCAAmC,EAAE;AAC/D,mBAAe,WAAW,kCAAkC,EAAE;AAAA,EAChE;AACA,QAAM,cAAc,OAAO,QAAQ,OAAO,KAAK,OAAO,KAAK,OAAO;AAClE,MAAI,aAAa;AACf,WAAO,KAAK,WAAW,YAAY;AAAA,EACrC;AAGA,MAAI;AACJ,QAAM,uBAAuB,OAAO,kBAAkB,UAAU,OAAO,eAAe,OAAO,KAAK,OAAO,WAAW,EAAE,OAAO,SAAO;AAClI,WAAO,OAAO,OAAO,YAAY,GAAG,EAAE,kBAAkB;AAAA,EAC1D,CAAC,EAAE,SAAS;AACZ,WAAS,IAAI,GAAG,IAAI,cAAc,KAAK,GAAG;AACxC,gBAAY;AACZ,QAAI;AACJ,QAAI,OAAO,CAAC;AAAG,cAAQ,OAAO,CAAC;AAC/B,QAAI,aAAa;AACf,aAAO,KAAK,YAAY,GAAG,OAAO,cAAc,iBAAiB;AAAA,IACnE;AACA,QAAI,OAAO,CAAC,KAAK,aAAa,OAAO,SAAS,MAAM;AAAQ;AAE5D,QAAI,OAAO,kBAAkB,QAAQ;AACnC,UAAI,sBAAsB;AACxB,eAAO,CAAC,EAAE,MAAM,kBAAkB,OAAO,CAAC,IAAI;AAAA,MAChD;AACA,YAAM,cAAc,iBAAiB,KAAK;AAC1C,YAAM,mBAAmB,MAAM,MAAM;AACrC,YAAM,yBAAyB,MAAM,MAAM;AAC3C,UAAI,kBAAkB;AACpB,cAAM,MAAM,YAAY;AAAA,MAC1B;AACA,UAAI,wBAAwB;AAC1B,cAAM,MAAM,kBAAkB;AAAA,MAChC;AACA,UAAI,OAAO,cAAc;AACvB,oBAAY,OAAO,aAAa,IAAI,iBAAiB,OAAO,SAAS,IAAI,IAAI,iBAAiB,OAAO,UAAU,IAAI;AAAA,MACrH,OAAO;AAEL,cAAM,QAAQ,0BAA0B,aAAa,OAAO;AAC5D,cAAM,cAAc,0BAA0B,aAAa,cAAc;AACzE,cAAM,eAAe,0BAA0B,aAAa,eAAe;AAC3E,cAAM,aAAa,0BAA0B,aAAa,aAAa;AACvE,cAAM,cAAc,0BAA0B,aAAa,cAAc;AACzE,cAAM,YAAY,YAAY,iBAAiB,YAAY;AAC3D,YAAI,aAAa,cAAc,cAAc;AAC3C,sBAAY,QAAQ,aAAa;AAAA,QACnC,OAAO;AACL,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,sBAAY,QAAQ,cAAc,eAAe,aAAa,eAAe,cAAc;AAAA,QAC7F;AAAA,MACF;AACA,UAAI,kBAAkB;AACpB,cAAM,MAAM,YAAY;AAAA,MAC1B;AACA,UAAI,wBAAwB;AAC1B,cAAM,MAAM,kBAAkB;AAAA,MAChC;AACA,UAAI,OAAO;AAAc,oBAAY,KAAK,MAAM,SAAS;AAAA,IAC3D,OAAO;AACL,mBAAa,cAAc,OAAO,gBAAgB,KAAK,gBAAgB,OAAO;AAC9E,UAAI,OAAO;AAAc,oBAAY,KAAK,MAAM,SAAS;AACzD,UAAI,OAAO,CAAC,GAAG;AACb,eAAO,CAAC,EAAE,MAAM,kBAAkB,OAAO,CAAC,IAAI,GAAG;AAAA,MACnD;AAAA,IACF;AACA,QAAI,OAAO,CAAC,GAAG;AACb,aAAO,CAAC,EAAE,kBAAkB;AAAA,IAC9B;AACA,oBAAgB,KAAK,SAAS;AAC9B,QAAI,OAAO,gBAAgB;AACzB,sBAAgB,gBAAgB,YAAY,IAAI,gBAAgB,IAAI;AACpE,UAAI,kBAAkB,KAAK,MAAM;AAAG,wBAAgB,gBAAgB,aAAa,IAAI;AACrF,UAAI,MAAM;AAAG,wBAAgB,gBAAgB,aAAa,IAAI;AAC9D,UAAI,KAAK,IAAI,aAAa,IAAI,IAAI;AAAM,wBAAgB;AACxD,UAAI,OAAO;AAAc,wBAAgB,KAAK,MAAM,aAAa;AACjE,UAAI,QAAQ,OAAO,mBAAmB;AAAG,iBAAS,KAAK,aAAa;AACpE,iBAAW,KAAK,aAAa;AAAA,IAC/B,OAAO;AACL,UAAI,OAAO;AAAc,wBAAgB,KAAK,MAAM,aAAa;AACjE,WAAK,QAAQ,KAAK,IAAI,OAAO,OAAO,oBAAoB,KAAK,KAAK,OAAO,OAAO,mBAAmB;AAAG,iBAAS,KAAK,aAAa;AACjI,iBAAW,KAAK,aAAa;AAC7B,sBAAgB,gBAAgB,YAAY;AAAA,IAC9C;AACA,WAAO,eAAe,YAAY;AAClC,oBAAgB;AAChB,aAAS;AAAA,EACX;AACA,SAAO,cAAc,KAAK,IAAI,OAAO,aAAa,UAAU,IAAI;AAChE,MAAI,OAAO,aAAa,OAAO,WAAW,WAAW,OAAO,WAAW,cAAc;AACnF,cAAU,MAAM,QAAQ,GAAG,OAAO,cAAc,OAAO;AAAA,EACzD;AACA,MAAI,OAAO,gBAAgB;AACzB,cAAU,MAAM,kBAAkB,OAAO,CAAC,IAAI,GAAG,OAAO,cAAc,OAAO;AAAA,EAC/E;AACA,MAAI,aAAa;AACf,WAAO,KAAK,kBAAkB,WAAW,UAAU,iBAAiB;AAAA,EACtE;AAGA,MAAI,CAAC,OAAO,gBAAgB;AAC1B,UAAM,gBAAgB,CAAC;AACvB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,UAAI,iBAAiB,SAAS,CAAC;AAC/B,UAAI,OAAO;AAAc,yBAAiB,KAAK,MAAM,cAAc;AACnE,UAAI,SAAS,CAAC,KAAK,OAAO,cAAc,YAAY;AAClD,sBAAc,KAAK,cAAc;AAAA,MACnC;AAAA,IACF;AACA,eAAW;AACX,QAAI,KAAK,MAAM,OAAO,cAAc,UAAU,IAAI,KAAK,MAAM,SAAS,SAAS,SAAS,CAAC,CAAC,IAAI,GAAG;AAC/F,eAAS,KAAK,OAAO,cAAc,UAAU;AAAA,IAC/C;AAAA,EACF;AACA,MAAI,aAAa,OAAO,MAAM;AAC5B,UAAM,OAAO,gBAAgB,CAAC,IAAI;AAClC,QAAI,OAAO,iBAAiB,GAAG;AAC7B,YAAM,SAAS,KAAK,MAAM,OAAO,QAAQ,eAAe,OAAO,QAAQ,eAAe,OAAO,cAAc;AAC3G,YAAM,YAAY,OAAO,OAAO;AAChC,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,iBAAS,KAAK,SAAS,SAAS,SAAS,CAAC,IAAI,SAAS;AAAA,MACzD;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,eAAe,OAAO,QAAQ,aAAa,KAAK,GAAG;AACpF,UAAI,OAAO,mBAAmB,GAAG;AAC/B,iBAAS,KAAK,SAAS,SAAS,SAAS,CAAC,IAAI,IAAI;AAAA,MACpD;AACA,iBAAW,KAAK,WAAW,WAAW,SAAS,CAAC,IAAI,IAAI;AACxD,aAAO,eAAe;AAAA,IACxB;AAAA,EACF;AACA,MAAI,SAAS,WAAW;AAAG,eAAW,CAAC,CAAC;AACxC,MAAI,OAAO,iBAAiB,GAAG;AAC7B,UAAM,MAAM,OAAO,aAAa,KAAK,MAAM,eAAe,kBAAkB,aAAa;AACzF,WAAO,OAAO,CAAC,GAAG,eAAe;AAC/B,UAAI,CAAC,OAAO,WAAW,OAAO;AAAM,eAAO;AAC3C,UAAI,eAAe,OAAO,SAAS,GAAG;AACpC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC,EAAE,QAAQ,aAAW;AACpB,cAAQ,MAAM,GAAG,IAAI,GAAG;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,MAAI,OAAO,kBAAkB,OAAO,sBAAsB;AACxD,QAAI,gBAAgB;AACpB,oBAAgB,QAAQ,oBAAkB;AACxC,uBAAiB,kBAAkB,OAAO,eAAe,OAAO,eAAe;AAAA,IACjF,CAAC;AACD,qBAAiB,OAAO;AACxB,UAAM,UAAU,gBAAgB;AAChC,eAAW,SAAS,IAAI,UAAQ;AAC9B,UAAI,OAAO;AAAG,eAAO,CAAC;AACtB,UAAI,OAAO;AAAS,eAAO,UAAU;AACrC,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI,OAAO,0BAA0B;AACnC,QAAI,gBAAgB;AACpB,oBAAgB,QAAQ,oBAAkB;AACxC,uBAAiB,kBAAkB,OAAO,eAAe,OAAO,eAAe;AAAA,IACjF,CAAC;AACD,qBAAiB,OAAO;AACxB,QAAI,gBAAgB,YAAY;AAC9B,YAAM,mBAAmB,aAAa,iBAAiB;AACvD,eAAS,QAAQ,CAAC,MAAM,cAAc;AACpC,iBAAS,SAAS,IAAI,OAAO;AAAA,MAC/B,CAAC;AACD,iBAAW,QAAQ,CAAC,MAAM,cAAc;AACtC,mBAAW,SAAS,IAAI,OAAO;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO,OAAO,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,OAAO,kBAAkB,OAAO,WAAW,CAAC,OAAO,sBAAsB;AAC3E,mBAAe,WAAW,mCAAmC,GAAG,CAAC,SAAS,CAAC,KAAK;AAChF,mBAAe,WAAW,kCAAkC,GAAG,OAAO,OAAO,IAAI,gBAAgB,gBAAgB,SAAS,CAAC,IAAI,KAAK;AACpI,UAAM,gBAAgB,CAAC,OAAO,SAAS,CAAC;AACxC,UAAM,kBAAkB,CAAC,OAAO,WAAW,CAAC;AAC5C,WAAO,WAAW,OAAO,SAAS,IAAI,OAAK,IAAI,aAAa;AAC5D,WAAO,aAAa,OAAO,WAAW,IAAI,OAAK,IAAI,eAAe;AAAA,EACpE;AACA,MAAI,iBAAiB,sBAAsB;AACzC,WAAO,KAAK,oBAAoB;AAAA,EAClC;AACA,MAAI,SAAS,WAAW,wBAAwB;AAC9C,QAAI,OAAO,OAAO;AAAe,aAAO,cAAc;AACtD,WAAO,KAAK,sBAAsB;AAAA,EACpC;AACA,MAAI,WAAW,WAAW,0BAA0B;AAClD,WAAO,KAAK,wBAAwB;AAAA,EACtC;AACA,MAAI,OAAO,qBAAqB;AAC9B,WAAO,mBAAmB;AAAA,EAC5B;AACA,MAAI,CAAC,aAAa,CAAC,OAAO,YAAY,OAAO,WAAW,WAAW,OAAO,WAAW,SAAS;AAC5F,UAAM,sBAAsB,GAAG,OAAO;AACtC,UAAM,6BAA6B,OAAO,GAAG,UAAU,SAAS,mBAAmB;AACnF,QAAI,gBAAgB,OAAO,yBAAyB;AAClD,UAAI,CAAC;AAA4B,eAAO,GAAG,UAAU,IAAI,mBAAmB;AAAA,IAC9E,WAAW,4BAA4B;AACrC,aAAO,GAAG,UAAU,OAAO,mBAAmB;AAAA,IAChD;AAAA,EACF;AACF;;;AC5Re,SAAR,iBAAkC,OAAO;AAC9C,QAAM,SAAS;AACf,QAAM,eAAe,CAAC;AACtB,QAAM,YAAY,OAAO,WAAW,OAAO,OAAO,QAAQ;AAC1D,MAAI,YAAY;AAChB,MAAI;AACJ,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,cAAc,KAAK;AAAA,EAC5B,WAAW,UAAU,MAAM;AACzB,WAAO,cAAc,OAAO,OAAO,KAAK;AAAA,EAC1C;AACA,QAAM,kBAAkB,WAAS;AAC/B,QAAI,WAAW;AACb,aAAO,OAAO,oBAAoB,KAAK;AAAA,IACzC;AACA,WAAO,OAAO,OAAO,KAAK;AAAA,EAC5B;AAEA,MAAI,OAAO,OAAO,kBAAkB,UAAU,OAAO,OAAO,gBAAgB,GAAG;AAC7E,QAAI,OAAO,OAAO,gBAAgB;AAChC,OAAC,OAAO,iBAAiB,CAAC,GAAG,QAAQ,WAAS;AAC5C,qBAAa,KAAK,KAAK;AAAA,MACzB,CAAC;AAAA,IACH,OAAO;AACL,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK,OAAO,OAAO,aAAa,GAAG,KAAK,GAAG;AAC9D,cAAM,QAAQ,OAAO,cAAc;AACnC,YAAI,QAAQ,OAAO,OAAO,UAAU,CAAC;AAAW;AAChD,qBAAa,KAAK,gBAAgB,KAAK,CAAC;AAAA,MAC1C;AAAA,IACF;AAAA,EACF,OAAO;AACL,iBAAa,KAAK,gBAAgB,OAAO,WAAW,CAAC;AAAA,EACvD;AAGA,OAAK,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK,GAAG;AAC3C,QAAI,OAAO,aAAa,CAAC,MAAM,aAAa;AAC1C,YAAM,SAAS,aAAa,CAAC,EAAE;AAC/B,kBAAY,SAAS,YAAY,SAAS;AAAA,IAC5C;AAAA,EACF;AAGA,MAAI,aAAa,cAAc;AAAG,WAAO,UAAU,MAAM,SAAS,GAAG;AACvE;;;AC5Ce,SAAR,qBAAsC;AAC3C,QAAM,SAAS;AACf,QAAM,SAAS,OAAO;AAEtB,QAAM,cAAc,OAAO,YAAY,OAAO,aAAa,IAAI,OAAO,UAAU,aAAa,OAAO,UAAU,YAAY;AAC1H,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,WAAO,CAAC,EAAE,qBAAqB,OAAO,aAAa,IAAI,OAAO,CAAC,EAAE,aAAa,OAAO,CAAC,EAAE,aAAa;AAAA,EACvG;AACF;;;ACRe,SAAR,qBAAsC,YAAY,QAAQ,KAAK,aAAa,GAAG;AACpF,QAAM,SAAS;AACf,QAAM,SAAS,OAAO;AACtB,QAAM;AAAA,IACJ;AAAA,IACA,cAAc;AAAA,IACd;AAAA,EACF,IAAI;AACJ,MAAI,OAAO,WAAW;AAAG;AACzB,MAAI,OAAO,OAAO,CAAC,EAAE,sBAAsB;AAAa,WAAO,mBAAmB;AAClF,MAAI,eAAe,CAAC;AACpB,MAAI;AAAK,mBAAe;AAGxB,SAAO,QAAQ,aAAW;AACxB,YAAQ,UAAU,OAAO,OAAO,iBAAiB;AAAA,EACnD,CAAC;AACD,SAAO,uBAAuB,CAAC;AAC/B,SAAO,gBAAgB,CAAC;AACxB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,UAAM,QAAQ,OAAO,CAAC;AACtB,QAAI,cAAc,MAAM;AACxB,QAAI,OAAO,WAAW,OAAO,gBAAgB;AAC3C,qBAAe,OAAO,CAAC,EAAE;AAAA,IAC3B;AACA,UAAM,iBAAiB,gBAAgB,OAAO,iBAAiB,OAAO,aAAa,IAAI,KAAK,gBAAgB,MAAM,kBAAkB,OAAO;AAC3I,UAAM,yBAAyB,eAAe,SAAS,CAAC,KAAK,OAAO,iBAAiB,OAAO,aAAa,IAAI,KAAK,gBAAgB,MAAM,kBAAkB,OAAO;AACjK,UAAM,cAAc,EAAE,eAAe;AACrC,UAAM,aAAa,cAAc,OAAO,gBAAgB,CAAC;AACzD,UAAM,YAAY,eAAe,KAAK,cAAc,OAAO,OAAO,KAAK,aAAa,KAAK,cAAc,OAAO,QAAQ,eAAe,KAAK,cAAc,OAAO;AAC/J,QAAI,WAAW;AACb,aAAO,cAAc,KAAK,KAAK;AAC/B,aAAO,qBAAqB,KAAK,CAAC;AAClC,aAAO,CAAC,EAAE,UAAU,IAAI,OAAO,iBAAiB;AAAA,IAClD;AACA,UAAM,WAAW,MAAM,CAAC,gBAAgB;AACxC,UAAM,mBAAmB,MAAM,CAAC,wBAAwB;AAAA,EAC1D;AACF;;;ACtCe,SAAR,eAAgC,WAAW;AAChD,QAAM,SAAS;AACf,MAAI,OAAO,cAAc,aAAa;AACpC,UAAM,aAAa,OAAO,eAAe,KAAK;AAE9C,gBAAY,UAAU,OAAO,aAAa,OAAO,YAAY,cAAc;AAAA,EAC7E;AACA,QAAM,SAAS,OAAO;AACtB,QAAM,iBAAiB,OAAO,aAAa,IAAI,OAAO,aAAa;AACnE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,eAAe;AACrB,QAAM,SAAS;AACf,MAAI,mBAAmB,GAAG;AACxB,eAAW;AACX,kBAAc;AACd,YAAQ;AAAA,EACV,OAAO;AACL,gBAAY,YAAY,OAAO,aAAa,KAAK;AACjD,UAAM,qBAAqB,KAAK,IAAI,YAAY,OAAO,aAAa,CAAC,IAAI;AACzE,UAAM,eAAe,KAAK,IAAI,YAAY,OAAO,aAAa,CAAC,IAAI;AACnE,kBAAc,sBAAsB,YAAY;AAChD,YAAQ,gBAAgB,YAAY;AACpC,QAAI;AAAoB,iBAAW;AACnC,QAAI;AAAc,iBAAW;AAAA,EAC/B;AACA,MAAI,OAAO,MAAM;AACf,UAAM,kBAAkB,OAAO,oBAAoB,CAAC;AACpD,UAAM,iBAAiB,OAAO,oBAAoB,OAAO,OAAO,SAAS,CAAC;AAC1E,UAAM,sBAAsB,OAAO,WAAW,eAAe;AAC7D,UAAM,qBAAqB,OAAO,WAAW,cAAc;AAC3D,UAAM,eAAe,OAAO,WAAW,OAAO,WAAW,SAAS,CAAC;AACnE,UAAM,eAAe,KAAK,IAAI,SAAS;AACvC,QAAI,gBAAgB,qBAAqB;AACvC,sBAAgB,eAAe,uBAAuB;AAAA,IACxD,OAAO;AACL,sBAAgB,eAAe,eAAe,sBAAsB;AAAA,IACtE;AACA,QAAI,eAAe;AAAG,sBAAgB;AAAA,EACxC;AACA,SAAO,OAAO,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,OAAO,uBAAuB,OAAO,kBAAkB,OAAO;AAAY,WAAO,qBAAqB,SAAS;AACnH,MAAI,eAAe,CAAC,cAAc;AAChC,WAAO,KAAK,uBAAuB;AAAA,EACrC;AACA,MAAI,SAAS,CAAC,QAAQ;AACpB,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AACA,MAAI,gBAAgB,CAAC,eAAe,UAAU,CAAC,OAAO;AACpD,WAAO,KAAK,UAAU;AAAA,EACxB;AACA,SAAO,KAAK,YAAY,QAAQ;AAClC;;;AC5De,SAAR,sBAAuC;AAC5C,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,OAAO,WAAW,OAAO,QAAQ;AACnD,QAAM,mBAAmB,cAAY;AACnC,WAAO,gBAAgB,UAAU,IAAI,OAAO,aAAa,yBAAyB,UAAU,EAAE,CAAC;AAAA,EACjG;AACA,SAAO,QAAQ,aAAW;AACxB,YAAQ,UAAU,OAAO,OAAO,kBAAkB,OAAO,gBAAgB,OAAO,cAAc;AAAA,EAChG,CAAC;AACD,MAAI;AACJ,MAAI,WAAW;AACb,QAAI,OAAO,MAAM;AACf,UAAI,aAAa,cAAc,OAAO,QAAQ;AAC9C,UAAI,aAAa;AAAG,qBAAa,OAAO,QAAQ,OAAO,SAAS;AAChE,UAAI,cAAc,OAAO,QAAQ,OAAO;AAAQ,sBAAc,OAAO,QAAQ,OAAO;AACpF,oBAAc,iBAAiB,6BAA6B,cAAc;AAAA,IAC5E,OAAO;AACL,oBAAc,iBAAiB,6BAA6B,eAAe;AAAA,IAC7E;AAAA,EACF,OAAO;AACL,kBAAc,OAAO,WAAW;AAAA,EAClC;AACA,MAAI,aAAa;AAEf,gBAAY,UAAU,IAAI,OAAO,gBAAgB;AAGjD,QAAI,YAAY,eAAe,aAAa,IAAI,OAAO,0BAA0B,EAAE,CAAC;AACpF,QAAI,OAAO,QAAQ,CAAC,WAAW;AAC7B,kBAAY,OAAO,CAAC;AAAA,IACtB;AACA,QAAI,WAAW;AACb,gBAAU,UAAU,IAAI,OAAO,cAAc;AAAA,IAC/C;AAEA,QAAI,YAAY,eAAe,aAAa,IAAI,OAAO,0BAA0B,EAAE,CAAC;AACpF,QAAI,OAAO,QAAQ,CAAC,cAAc,GAAG;AACnC,kBAAY,OAAO,OAAO,SAAS,CAAC;AAAA,IACtC;AACA,QAAI,WAAW;AACb,gBAAU,UAAU,IAAI,OAAO,cAAc;AAAA,IAC/C;AAAA,EACF;AACA,SAAO,kBAAkB;AAC3B;;;ACnDO,IAAM,uBAAuB,CAAC,QAAQ,YAAY;AACvD,MAAI,CAAC,UAAU,OAAO,aAAa,CAAC,OAAO;AAAQ;AACnD,QAAM,gBAAgB,MAAM,OAAO,YAAY,iBAAiB,IAAI,OAAO,OAAO;AAClF,QAAM,UAAU,QAAQ,QAAQ,cAAc,CAAC;AAC/C,MAAI,SAAS;AACX,UAAM,SAAS,QAAQ,cAAc,IAAI,OAAO,OAAO,oBAAoB;AAC3E,QAAI;AAAQ,aAAO,OAAO;AAAA,EAC5B;AACF;AACA,IAAM,SAAS,CAAC,QAAQ,UAAU;AAChC,MAAI,CAAC,OAAO,OAAO,KAAK;AAAG;AAC3B,QAAM,UAAU,OAAO,OAAO,KAAK,EAAE,cAAc,kBAAkB;AACrE,MAAI;AAAS,YAAQ,gBAAgB,SAAS;AAChD;AACO,IAAM,UAAU,YAAU;AAC/B,MAAI,CAAC,UAAU,OAAO,aAAa,CAAC,OAAO;AAAQ;AACnD,MAAI,SAAS,OAAO,OAAO;AAC3B,QAAM,MAAM,OAAO,OAAO;AAC1B,MAAI,CAAC,OAAO,CAAC,UAAU,SAAS;AAAG;AACnC,WAAS,KAAK,IAAI,QAAQ,GAAG;AAC7B,QAAM,gBAAgB,OAAO,OAAO,kBAAkB,SAAS,OAAO,qBAAqB,IAAI,KAAK,KAAK,OAAO,OAAO,aAAa;AACpI,QAAM,cAAc,OAAO;AAC3B,QAAM,uBAAuB,cAAc,gBAAgB;AAC3D,MAAI,OAAO,OAAO,QAAQ;AACxB,aAAS,IAAI,cAAc,QAAQ,KAAK,uBAAuB,QAAQ,KAAK,GAAG;AAC7E,YAAM,aAAa,IAAI,MAAM,OAAO;AACpC,UAAI,cAAc,eAAe,YAAY;AAAsB,eAAO,QAAQ,SAAS;AAAA,IAC7F;AAAA,EACF,OAAO;AACL,aAAS,IAAI,KAAK,IAAI,uBAAuB,QAAQ,CAAC,GAAG,KAAK,KAAK,IAAI,uBAAuB,QAAQ,MAAM,CAAC,GAAG,KAAK,GAAG;AACtH,UAAI,MAAM,eAAe,IAAI;AAAsB,eAAO,QAAQ,CAAC;AAAA,IACrE;AAAA,EACF;AACF;;;AChCO,SAAS,0BAA0B,QAAQ;AAChD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,OAAO,eAAe,OAAO,YAAY,CAAC,OAAO;AACnE,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AAC7C,QAAI,OAAO,WAAW,IAAI,CAAC,MAAM,aAAa;AAC5C,UAAI,aAAa,WAAW,CAAC,KAAK,YAAY,WAAW,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,IAAI,WAAW,CAAC,KAAK,GAAG;AACzG,sBAAc;AAAA,MAChB,WAAW,aAAa,WAAW,CAAC,KAAK,YAAY,WAAW,IAAI,CAAC,GAAG;AACtE,sBAAc,IAAI;AAAA,MACpB;AAAA,IACF,WAAW,aAAa,WAAW,CAAC,GAAG;AACrC,oBAAc;AAAA,IAChB;AAAA,EACF;AAEA,MAAI,OAAO,qBAAqB;AAC9B,QAAI,cAAc,KAAK,OAAO,gBAAgB;AAAa,oBAAc;AAAA,EAC3E;AACA,SAAO;AACT;AACe,SAAR,kBAAmC,gBAAgB;AACxD,QAAM,SAAS;AACf,QAAM,YAAY,OAAO,eAAe,OAAO,YAAY,CAAC,OAAO;AACnE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,EACb,IAAI;AACJ,MAAI,cAAc;AAClB,MAAI;AACJ,QAAM,sBAAsB,YAAU;AACpC,QAAIC,aAAY,SAAS,OAAO,QAAQ;AACxC,QAAIA,aAAY,GAAG;AACjB,MAAAA,aAAY,OAAO,QAAQ,OAAO,SAASA;AAAA,IAC7C;AACA,QAAIA,cAAa,OAAO,QAAQ,OAAO,QAAQ;AAC7C,MAAAA,cAAa,OAAO,QAAQ,OAAO;AAAA,IACrC;AACA,WAAOA;AAAA,EACT;AACA,MAAI,OAAO,gBAAgB,aAAa;AACtC,kBAAc,0BAA0B,MAAM;AAAA,EAChD;AACA,MAAI,SAAS,QAAQ,SAAS,KAAK,GAAG;AACpC,gBAAY,SAAS,QAAQ,SAAS;AAAA,EACxC,OAAO;AACL,UAAM,OAAO,KAAK,IAAI,OAAO,oBAAoB,WAAW;AAC5D,gBAAY,OAAO,KAAK,OAAO,cAAc,QAAQ,OAAO,cAAc;AAAA,EAC5E;AACA,MAAI,aAAa,SAAS;AAAQ,gBAAY,SAAS,SAAS;AAChE,MAAI,gBAAgB,eAAe;AACjC,QAAI,cAAc,mBAAmB;AACnC,aAAO,YAAY;AACnB,aAAO,KAAK,iBAAiB;AAAA,IAC/B;AACA,QAAI,OAAO,OAAO,QAAQ,OAAO,WAAW,OAAO,OAAO,QAAQ,SAAS;AACzE,aAAO,YAAY,oBAAoB,WAAW;AAAA,IACpD;AACA;AAAA,EACF;AAEA,MAAI;AACJ,MAAI,OAAO,WAAW,OAAO,QAAQ,WAAW,OAAO,MAAM;AAC3D,gBAAY,oBAAoB,WAAW;AAAA,EAC7C,WAAW,OAAO,OAAO,WAAW,GAAG;AACrC,gBAAY,SAAS,OAAO,OAAO,WAAW,EAAE,aAAa,yBAAyB,KAAK,aAAa,EAAE;AAAA,EAC5G,OAAO;AACL,gBAAY;AAAA,EACd;AACA,SAAO,OAAO,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,OAAO,aAAa;AACtB,YAAQ,MAAM;AAAA,EAChB;AACA,SAAO,KAAK,mBAAmB;AAC/B,SAAO,KAAK,iBAAiB;AAC7B,MAAI,sBAAsB,WAAW;AACnC,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AACA,MAAI,OAAO,eAAe,OAAO,OAAO,oBAAoB;AAC1D,WAAO,KAAK,aAAa;AAAA,EAC3B;AACF;;;AC7Fe,SAAR,mBAAoC,GAAG;AAC5C,QAAM,SAAS;AACf,QAAM,SAAS,OAAO;AACtB,QAAM,QAAQ,EAAE,QAAQ,IAAI,OAAO,0BAA0B;AAC7D,MAAI,aAAa;AACjB,MAAI;AACJ,MAAI,OAAO;AACT,aAAS,IAAI,GAAG,IAAI,OAAO,OAAO,QAAQ,KAAK,GAAG;AAChD,UAAI,OAAO,OAAO,CAAC,MAAM,OAAO;AAC9B,qBAAa;AACb,qBAAa;AACb;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS,YAAY;AACvB,WAAO,eAAe;AACtB,QAAI,OAAO,WAAW,OAAO,OAAO,QAAQ,SAAS;AACnD,aAAO,eAAe,SAAS,MAAM,aAAa,yBAAyB,GAAG,EAAE;AAAA,IAClF,OAAO;AACL,aAAO,eAAe;AAAA,IACxB;AAAA,EACF,OAAO;AACL,WAAO,eAAe;AACtB,WAAO,eAAe;AACtB;AAAA,EACF;AACA,MAAI,OAAO,uBAAuB,OAAO,iBAAiB,UAAa,OAAO,iBAAiB,OAAO,aAAa;AACjH,WAAO,oBAAoB;AAAA,EAC7B;AACF;;;ACrBA,IAAO,iBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AClBe,SAAR,mBAAoC,OAAO,KAAK,aAAa,IAAI,MAAM,KAAK;AACjF,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,OAAO,kBAAkB;AAC3B,WAAO,MAAM,CAAC,YAAY;AAAA,EAC5B;AACA,MAAI,OAAO,SAAS;AAClB,WAAO;AAAA,EACT;AACA,MAAI,mBAAmB,aAAa,WAAW,IAAI;AACnD,MAAI;AAAK,uBAAmB,CAAC;AAC7B,SAAO,oBAAoB;AAC7B;;;AClBe,SAAR,aAA8B,WAAW,cAAc;AAC5D,QAAM,SAAS;AACf,QAAM;AAAA,IACJ,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,IAAI;AACR,MAAI,IAAI;AACR,QAAM,IAAI;AACV,MAAI,OAAO,aAAa,GAAG;AACzB,QAAI,MAAM,CAAC,YAAY;AAAA,EACzB,OAAO;AACL,QAAI;AAAA,EACN;AACA,MAAI,OAAO,cAAc;AACvB,QAAI,KAAK,MAAM,CAAC;AAChB,QAAI,KAAK,MAAM,CAAC;AAAA,EAClB;AACA,MAAI,OAAO,SAAS;AAClB,cAAU,OAAO,aAAa,IAAI,eAAe,WAAW,IAAI,OAAO,aAAa,IAAI,CAAC,IAAI,CAAC;AAAA,EAChG,WAAW,CAAC,OAAO,kBAAkB;AACnC,cAAU,MAAM,YAAY,eAAe,QAAQ,QAAQ;AAAA,EAC7D;AACA,SAAO,oBAAoB,OAAO;AAClC,SAAO,YAAY,OAAO,aAAa,IAAI,IAAI;AAG/C,MAAI;AACJ,QAAM,iBAAiB,OAAO,aAAa,IAAI,OAAO,aAAa;AACnE,MAAI,mBAAmB,GAAG;AACxB,kBAAc;AAAA,EAChB,OAAO;AACL,mBAAe,YAAY,OAAO,aAAa,KAAK;AAAA,EACtD;AACA,MAAI,gBAAgB,UAAU;AAC5B,WAAO,eAAe,SAAS;AAAA,EACjC;AACA,SAAO,KAAK,gBAAgB,OAAO,WAAW,YAAY;AAC5D;;;ACxCe,SAAR,eAAgC;AACrC,SAAO,CAAC,KAAK,SAAS,CAAC;AACzB;;;ACFe,SAAR,eAAgC;AACrC,SAAO,CAAC,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;AAChD;;;ACDe,SAAR,YAA6B,YAAY,GAAG,QAAQ,KAAK,OAAO,OAAO,eAAe,MAAM,kBAAkB,MAAM,UAAU;AACnI,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,OAAO,aAAa,OAAO,gCAAgC;AAC7D,WAAO;AAAA,EACT;AACA,QAAMC,gBAAe,OAAO,aAAa;AACzC,QAAMC,gBAAe,OAAO,aAAa;AACzC,MAAI;AACJ,MAAI,mBAAmB,YAAYD;AAAc,mBAAeA;AAAA,WAAsB,mBAAmB,YAAYC;AAAc,mBAAeA;AAAA;AAAkB,mBAAe;AAGnL,SAAO,eAAe,YAAY;AAClC,MAAI,OAAO,SAAS;AAClB,UAAM,MAAM,OAAO,aAAa;AAChC,QAAI,UAAU,GAAG;AACf,gBAAU,MAAM,eAAe,WAAW,IAAI,CAAC;AAAA,IACjD,OAAO;AACL,UAAI,CAAC,OAAO,QAAQ,cAAc;AAChC,6BAAqB;AAAA,UACnB;AAAA,UACA,gBAAgB,CAAC;AAAA,UACjB,MAAM,MAAM,SAAS;AAAA,QACvB,CAAC;AACD,eAAO;AAAA,MACT;AACA,gBAAU,SAAS;AAAA,QACjB,CAAC,MAAM,SAAS,KAAK,GAAG,CAAC;AAAA,QACzB,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACA,MAAI,UAAU,GAAG;AACf,WAAO,cAAc,CAAC;AACtB,WAAO,aAAa,YAAY;AAChC,QAAI,cAAc;AAChB,aAAO,KAAK,yBAAyB,OAAO,QAAQ;AACpD,aAAO,KAAK,eAAe;AAAA,IAC7B;AAAA,EACF,OAAO;AACL,WAAO,cAAc,KAAK;AAC1B,WAAO,aAAa,YAAY;AAChC,QAAI,cAAc;AAChB,aAAO,KAAK,yBAAyB,OAAO,QAAQ;AACpD,aAAO,KAAK,iBAAiB;AAAA,IAC/B;AACA,QAAI,CAAC,OAAO,WAAW;AACrB,aAAO,YAAY;AACnB,UAAI,CAAC,OAAO,mCAAmC;AAC7C,eAAO,oCAAoC,SAASC,eAAc,GAAG;AACnE,cAAI,CAAC,UAAU,OAAO;AAAW;AACjC,cAAI,EAAE,WAAW;AAAM;AACvB,iBAAO,UAAU,oBAAoB,iBAAiB,OAAO,iCAAiC;AAC9F,iBAAO,oCAAoC;AAC3C,iBAAO,OAAO;AACd,cAAI,cAAc;AAChB,mBAAO,KAAK,eAAe;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AACA,aAAO,UAAU,iBAAiB,iBAAiB,OAAO,iCAAiC;AAAA,IAC7F;AAAA,EACF;AACA,SAAO;AACT;;;AChEA,IAAO,oBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACXe,SAAR,cAA+B,UAAU,cAAc;AAC5D,QAAM,SAAS;AACf,MAAI,CAAC,OAAO,OAAO,SAAS;AAC1B,WAAO,UAAU,MAAM,qBAAqB,GAAG;AAAA,EACjD;AACA,SAAO,KAAK,iBAAiB,UAAU,YAAY;AACrD;;;ACNe,SAAR,eAAgC;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,MAAM;AACV,MAAI,CAAC,KAAK;AACR,QAAI,cAAc;AAAe,YAAM;AAAA,aAAgB,cAAc;AAAe,YAAM;AAAA;AAAY,YAAM;AAAA,EAC9G;AACA,SAAO,KAAK,aAAa,MAAM;AAC/B,MAAI,gBAAgB,gBAAgB,eAAe;AACjD,QAAI,QAAQ,SAAS;AACnB,aAAO,KAAK,uBAAuB,MAAM;AACzC;AAAA,IACF;AACA,WAAO,KAAK,wBAAwB,MAAM;AAC1C,QAAI,QAAQ,QAAQ;AAClB,aAAO,KAAK,sBAAsB,MAAM;AAAA,IAC1C,OAAO;AACL,aAAO,KAAK,sBAAsB,MAAM;AAAA,IAC1C;AAAA,EACF;AACF;;;AC1Be,SAAR,gBAAiC,eAAe,MAAM,WAAW;AACtE,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,MAAI,OAAO;AAAS;AACpB,MAAI,OAAO,YAAY;AACrB,WAAO,iBAAiB;AAAA,EAC1B;AACA,iBAAe;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR,CAAC;AACH;;;ACfe,SAAR,cAA+B,eAAe,MAAM,WAAW;AACpE,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO,YAAY;AACnB,MAAI,OAAO;AAAS;AACpB,SAAO,cAAc,CAAC;AACtB,iBAAe;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR,CAAC;AACH;;;ACZA,IAAO,qBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AACF;;;ACNe,SAAR,QAAyB,QAAQ,GAAG,QAAQ,KAAK,OAAO,OAAO,eAAe,MAAM,UAAU,SAAS;AAC5G,MAAI,OAAO,UAAU,UAAU;AAC7B,YAAQ,SAAS,OAAO,EAAE;AAAA,EAC5B;AACA,QAAM,SAAS;AACf,MAAI,aAAa;AACjB,MAAI,aAAa;AAAG,iBAAa;AACjC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,OAAO,aAAa,OAAO,kCAAkC,CAAC,WAAW,CAAC,YAAY,CAAC,SAAS;AAClG,WAAO;AAAA,EACT;AACA,QAAM,OAAO,KAAK,IAAI,OAAO,OAAO,oBAAoB,UAAU;AAClE,MAAI,YAAY,OAAO,KAAK,OAAO,aAAa,QAAQ,OAAO,OAAO,cAAc;AACpF,MAAI,aAAa,SAAS;AAAQ,gBAAY,SAAS,SAAS;AAChE,QAAM,YAAY,CAAC,SAAS,SAAS;AAErC,MAAI,OAAO,qBAAqB;AAC9B,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AAC7C,YAAM,sBAAsB,CAAC,KAAK,MAAM,YAAY,GAAG;AACvD,YAAM,iBAAiB,KAAK,MAAM,WAAW,CAAC,IAAI,GAAG;AACrD,YAAM,qBAAqB,KAAK,MAAM,WAAW,IAAI,CAAC,IAAI,GAAG;AAC7D,UAAI,OAAO,WAAW,IAAI,CAAC,MAAM,aAAa;AAC5C,YAAI,uBAAuB,kBAAkB,sBAAsB,sBAAsB,qBAAqB,kBAAkB,GAAG;AACjI,uBAAa;AAAA,QACf,WAAW,uBAAuB,kBAAkB,sBAAsB,oBAAoB;AAC5F,uBAAa,IAAI;AAAA,QACnB;AAAA,MACF,WAAW,uBAAuB,gBAAgB;AAChD,qBAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAEA,MAAI,OAAO,eAAe,eAAe,aAAa;AACpD,QAAI,CAAC,OAAO,kBAAkB,YAAY,OAAO,aAAa,YAAY,OAAO,aAAa,GAAG;AAC/F,aAAO;AAAA,IACT;AACA,QAAI,CAAC,OAAO,kBAAkB,YAAY,OAAO,aAAa,YAAY,OAAO,aAAa,GAAG;AAC/F,WAAK,eAAe,OAAO,YAAY;AACrC,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,MAAI,gBAAgB,iBAAiB,MAAM,cAAc;AACvD,WAAO,KAAK,wBAAwB;AAAA,EACtC;AAGA,SAAO,eAAe,SAAS;AAC/B,MAAI;AACJ,MAAI,aAAa;AAAa,gBAAY;AAAA,WAAgB,aAAa;AAAa,gBAAY;AAAA;AAAY,gBAAY;AAGxH,MAAI,OAAO,CAAC,cAAc,OAAO,aAAa,CAAC,OAAO,cAAc,OAAO,WAAW;AACpF,WAAO,kBAAkB,UAAU;AAEnC,QAAI,OAAO,YAAY;AACrB,aAAO,iBAAiB;AAAA,IAC1B;AACA,WAAO,oBAAoB;AAC3B,QAAI,OAAO,WAAW,SAAS;AAC7B,aAAO,aAAa,SAAS;AAAA,IAC/B;AACA,QAAI,cAAc,SAAS;AACzB,aAAO,gBAAgB,cAAc,SAAS;AAC9C,aAAO,cAAc,cAAc,SAAS;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS;AAClB,UAAM,MAAM,OAAO,aAAa;AAChC,UAAM,IAAI,MAAM,YAAY,CAAC;AAC7B,QAAI,UAAU,GAAG;AACf,YAAM,YAAY,OAAO,WAAW,OAAO,OAAO,QAAQ;AAC1D,UAAI,WAAW;AACb,eAAO,UAAU,MAAM,iBAAiB;AACxC,eAAO,oBAAoB;AAAA,MAC7B;AACA,UAAI,aAAa,CAAC,OAAO,6BAA6B,OAAO,OAAO,eAAe,GAAG;AACpF,eAAO,4BAA4B;AACnC,8BAAsB,MAAM;AAC1B,oBAAU,MAAM,eAAe,WAAW,IAAI;AAAA,QAChD,CAAC;AAAA,MACH,OAAO;AACL,kBAAU,MAAM,eAAe,WAAW,IAAI;AAAA,MAChD;AACA,UAAI,WAAW;AACb,8BAAsB,MAAM;AAC1B,iBAAO,UAAU,MAAM,iBAAiB;AACxC,iBAAO,oBAAoB;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,UAAI,CAAC,OAAO,QAAQ,cAAc;AAChC,6BAAqB;AAAA,UACnB;AAAA,UACA,gBAAgB;AAAA,UAChB,MAAM,MAAM,SAAS;AAAA,QACvB,CAAC;AACD,eAAO;AAAA,MACT;AACA,gBAAU,SAAS;AAAA,QACjB,CAAC,MAAM,SAAS,KAAK,GAAG;AAAA,QACxB,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACA,SAAO,cAAc,KAAK;AAC1B,SAAO,aAAa,SAAS;AAC7B,SAAO,kBAAkB,UAAU;AACnC,SAAO,oBAAoB;AAC3B,SAAO,KAAK,yBAAyB,OAAO,QAAQ;AACpD,SAAO,gBAAgB,cAAc,SAAS;AAC9C,MAAI,UAAU,GAAG;AACf,WAAO,cAAc,cAAc,SAAS;AAAA,EAC9C,WAAW,CAAC,OAAO,WAAW;AAC5B,WAAO,YAAY;AACnB,QAAI,CAAC,OAAO,+BAA+B;AACzC,aAAO,gCAAgC,SAASC,eAAc,GAAG;AAC/D,YAAI,CAAC,UAAU,OAAO;AAAW;AACjC,YAAI,EAAE,WAAW;AAAM;AACvB,eAAO,UAAU,oBAAoB,iBAAiB,OAAO,6BAA6B;AAC1F,eAAO,gCAAgC;AACvC,eAAO,OAAO;AACd,eAAO,cAAc,cAAc,SAAS;AAAA,MAC9C;AAAA,IACF;AACA,WAAO,UAAU,iBAAiB,iBAAiB,OAAO,6BAA6B;AAAA,EACzF;AACA,SAAO;AACT;;;AC7Ie,SAAR,YAA6B,QAAQ,GAAG,QAAQ,KAAK,OAAO,OAAO,eAAe,MAAM,UAAU;AACvG,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,gBAAgB,SAAS,OAAO,EAAE;AACxC,YAAQ;AAAA,EACV;AACA,QAAM,SAAS;AACf,MAAI,WAAW;AACf,MAAI,OAAO,OAAO,MAAM;AACtB,QAAI,OAAO,WAAW,OAAO,OAAO,QAAQ,SAAS;AAEnD,iBAAW,WAAW,OAAO,QAAQ;AAAA,IACvC,OAAO;AACL,iBAAW,OAAO,oBAAoB,QAAQ;AAAA,IAChD;AAAA,EACF;AACA,SAAO,OAAO,QAAQ,UAAU,OAAO,cAAc,QAAQ;AAC/D;;;ACfe,SAAR,UAA2B,QAAQ,KAAK,OAAO,OAAO,eAAe,MAAM,UAAU;AAC1F,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC;AAAS,WAAO;AACrB,MAAI,WAAW,OAAO;AACtB,MAAI,OAAO,kBAAkB,UAAU,OAAO,mBAAmB,KAAK,OAAO,oBAAoB;AAC/F,eAAW,KAAK,IAAI,OAAO,qBAAqB,WAAW,IAAI,GAAG,CAAC;AAAA,EACrE;AACA,QAAM,YAAY,OAAO,cAAc,OAAO,qBAAqB,IAAI;AACvE,QAAM,YAAY,OAAO,WAAW,OAAO,QAAQ;AACnD,MAAI,OAAO,MAAM;AACf,QAAI,aAAa,CAAC,aAAa,OAAO;AAAqB,aAAO;AAClE,WAAO,QAAQ;AAAA,MACb,WAAW;AAAA,IACb,CAAC;AAED,WAAO,cAAc,OAAO,UAAU;AAAA,EACxC;AACA,MAAI,OAAO,UAAU,OAAO,OAAO;AACjC,WAAO,OAAO,QAAQ,GAAG,OAAO,cAAc,QAAQ;AAAA,EACxD;AACA,SAAO,OAAO,QAAQ,OAAO,cAAc,WAAW,OAAO,cAAc,QAAQ;AACrF;;;AC1Be,SAAR,UAA2B,QAAQ,KAAK,OAAO,OAAO,eAAe,MAAM,UAAU;AAC1F,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC;AAAS,WAAO;AACrB,QAAM,YAAY,OAAO,WAAW,OAAO,QAAQ;AACnD,MAAI,OAAO,MAAM;AACf,QAAI,aAAa,CAAC,aAAa,OAAO;AAAqB,aAAO;AAClE,WAAO,QAAQ;AAAA,MACb,WAAW;AAAA,IACb,CAAC;AAED,WAAO,cAAc,OAAO,UAAU;AAAA,EACxC;AACA,QAAM,YAAY,eAAe,OAAO,YAAY,CAAC,OAAO;AAC5D,WAAS,UAAU,KAAK;AACtB,QAAI,MAAM;AAAG,aAAO,CAAC,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC;AAC7C,WAAO,KAAK,MAAM,GAAG;AAAA,EACvB;AACA,QAAM,sBAAsB,UAAU,SAAS;AAC/C,QAAM,qBAAqB,SAAS,IAAI,SAAO,UAAU,GAAG,CAAC;AAC7D,MAAI,WAAW,SAAS,mBAAmB,QAAQ,mBAAmB,IAAI,CAAC;AAC3E,MAAI,OAAO,aAAa,eAAe,OAAO,SAAS;AACrD,QAAI;AACJ,aAAS,QAAQ,CAAC,MAAM,cAAc;AACpC,UAAI,uBAAuB,MAAM;AAE/B,wBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AACD,QAAI,OAAO,kBAAkB,aAAa;AACxC,iBAAW,SAAS,gBAAgB,IAAI,gBAAgB,IAAI,aAAa;AAAA,IAC3E;AAAA,EACF;AACA,MAAI,YAAY;AAChB,MAAI,OAAO,aAAa,aAAa;AACnC,gBAAY,WAAW,QAAQ,QAAQ;AACvC,QAAI,YAAY;AAAG,kBAAY,OAAO,cAAc;AACpD,QAAI,OAAO,kBAAkB,UAAU,OAAO,mBAAmB,KAAK,OAAO,oBAAoB;AAC/F,kBAAY,YAAY,OAAO,qBAAqB,YAAY,IAAI,IAAI;AACxE,kBAAY,KAAK,IAAI,WAAW,CAAC;AAAA,IACnC;AAAA,EACF;AACA,MAAI,OAAO,UAAU,OAAO,aAAa;AACvC,UAAM,YAAY,OAAO,OAAO,WAAW,OAAO,OAAO,QAAQ,WAAW,OAAO,UAAU,OAAO,QAAQ,OAAO,SAAS,IAAI,OAAO,OAAO,SAAS;AACvJ,WAAO,OAAO,QAAQ,WAAW,OAAO,cAAc,QAAQ;AAAA,EAChE;AACA,SAAO,OAAO,QAAQ,WAAW,OAAO,cAAc,QAAQ;AAChE;;;ACtDe,SAAR,WAA4B,QAAQ,KAAK,OAAO,OAAO,eAAe,MAAM,UAAU;AAC3F,QAAM,SAAS;AACf,SAAO,OAAO,QAAQ,OAAO,aAAa,OAAO,cAAc,QAAQ;AACzE;;;ACHe,SAAR,eAAgC,QAAQ,KAAK,OAAO,OAAO,eAAe,MAAM,UAAU,YAAY,KAAK;AAChH,QAAM,SAAS;AACf,MAAI,QAAQ,OAAO;AACnB,QAAM,OAAO,KAAK,IAAI,OAAO,OAAO,oBAAoB,KAAK;AAC7D,QAAM,YAAY,OAAO,KAAK,OAAO,QAAQ,QAAQ,OAAO,OAAO,cAAc;AACjF,QAAM,YAAY,OAAO,eAAe,OAAO,YAAY,CAAC,OAAO;AACnE,MAAI,aAAa,OAAO,SAAS,SAAS,GAAG;AAG3C,UAAM,cAAc,OAAO,SAAS,SAAS;AAC7C,UAAM,WAAW,OAAO,SAAS,YAAY,CAAC;AAC9C,QAAI,YAAY,eAAe,WAAW,eAAe,WAAW;AAClE,eAAS,OAAO,OAAO;AAAA,IACzB;AAAA,EACF,OAAO;AAGL,UAAM,WAAW,OAAO,SAAS,YAAY,CAAC;AAC9C,UAAM,cAAc,OAAO,SAAS,SAAS;AAC7C,QAAI,YAAY,aAAa,cAAc,YAAY,WAAW;AAChE,eAAS,OAAO,OAAO;AAAA,IACzB;AAAA,EACF;AACA,UAAQ,KAAK,IAAI,OAAO,CAAC;AACzB,UAAQ,KAAK,IAAI,OAAO,OAAO,WAAW,SAAS,CAAC;AACpD,SAAO,OAAO,QAAQ,OAAO,OAAO,cAAc,QAAQ;AAC5D;;;AC1Be,SAAR,sBAAuC;AAC5C,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,gBAAgB,OAAO,kBAAkB,SAAS,OAAO,qBAAqB,IAAI,OAAO;AAC/F,MAAI,eAAe,OAAO;AAC1B,MAAI;AACJ,QAAM,gBAAgB,OAAO,YAAY,iBAAiB,IAAI,OAAO;AACrE,MAAI,OAAO,MAAM;AACf,QAAI,OAAO;AAAW;AACtB,gBAAY,SAAS,OAAO,aAAa,aAAa,yBAAyB,GAAG,EAAE;AACpF,QAAI,OAAO,gBAAgB;AACzB,UAAI,eAAe,OAAO,eAAe,gBAAgB,KAAK,eAAe,OAAO,OAAO,SAAS,OAAO,eAAe,gBAAgB,GAAG;AAC3I,eAAO,QAAQ;AACf,uBAAe,OAAO,cAAc,gBAAgB,UAAU,GAAG,0CAA0C,aAAa,EAAE,CAAC,CAAC;AAC5H,iBAAS,MAAM;AACb,iBAAO,QAAQ,YAAY;AAAA,QAC7B,CAAC;AAAA,MACH,OAAO;AACL,eAAO,QAAQ,YAAY;AAAA,MAC7B;AAAA,IACF,WAAW,eAAe,OAAO,OAAO,SAAS,eAAe;AAC9D,aAAO,QAAQ;AACf,qBAAe,OAAO,cAAc,gBAAgB,UAAU,GAAG,0CAA0C,aAAa,EAAE,CAAC,CAAC;AAC5H,eAAS,MAAM;AACb,eAAO,QAAQ,YAAY;AAAA,MAC7B,CAAC;AAAA,IACH,OAAO;AACL,aAAO,QAAQ,YAAY;AAAA,IAC7B;AAAA,EACF,OAAO;AACL,WAAO,QAAQ,YAAY;AAAA,EAC7B;AACF;;;AC7BA,IAAO,gBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACde,SAAR,WAA4B,gBAAgB;AACjD,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,OAAO,QAAQ,OAAO,WAAW,OAAO,OAAO,QAAQ;AAAS;AACrE,QAAM,SAAS,gBAAgB,UAAU,IAAI,OAAO,0BAA0B;AAC9E,SAAO,QAAQ,CAAC,IAAI,UAAU;AAC5B,OAAG,aAAa,2BAA2B,KAAK;AAAA,EAClD,CAAC;AACD,SAAO,QAAQ;AAAA,IACb;AAAA,IACA,WAAW,OAAO,iBAAiB,SAAY;AAAA,EACjD,CAAC;AACH;;;AChBe,SAAR,QAAyB;AAAA,EAC9B;AAAA,EACA,SAAAC,WAAU;AAAA,EACV;AAAA,EACA,cAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,SAAS;AACf,MAAI,CAAC,OAAO,OAAO;AAAM;AACzB,SAAO,KAAK,eAAe;AAC3B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO,iBAAiB;AACxB,SAAO,iBAAiB;AACxB,MAAI,OAAO,WAAW,OAAO,QAAQ,SAAS;AAC5C,QAAID,UAAS;AACX,UAAI,CAAC,OAAO,kBAAkB,OAAO,cAAc,GAAG;AACpD,eAAO,QAAQ,OAAO,QAAQ,OAAO,QAAQ,GAAG,OAAO,IAAI;AAAA,MAC7D,WAAW,OAAO,kBAAkB,OAAO,YAAY,OAAO,eAAe;AAC3E,eAAO,QAAQ,OAAO,QAAQ,OAAO,SAAS,OAAO,WAAW,GAAG,OAAO,IAAI;AAAA,MAChF,WAAW,OAAO,cAAc,OAAO,SAAS,SAAS,GAAG;AAC1D,eAAO,QAAQ,OAAO,QAAQ,cAAc,GAAG,OAAO,IAAI;AAAA,MAC5D;AAAA,IACF;AACA,WAAO,iBAAiB;AACxB,WAAO,iBAAiB;AACxB,WAAO,KAAK,SAAS;AACrB;AAAA,EACF;AACA,QAAM,gBAAgB,OAAO,kBAAkB,SAAS,OAAO,qBAAqB,IAAI,KAAK,KAAK,WAAW,OAAO,eAAe,EAAE,CAAC;AACtI,MAAI,eAAe,OAAO,gBAAgB;AAC1C,MAAI,eAAe,OAAO,mBAAmB,GAAG;AAC9C,oBAAgB,OAAO,iBAAiB,eAAe,OAAO;AAAA,EAChE;AACA,SAAO,eAAe;AACtB,QAAM,uBAAuB,CAAC;AAC9B,QAAM,sBAAsB,CAAC;AAC7B,MAAI,cAAc,OAAO;AACzB,MAAI,OAAO,qBAAqB,aAAa;AAC3C,uBAAmB,OAAO,cAAc,OAAO,OAAO,OAAO,QAAM,GAAG,UAAU,SAAS,OAAO,gBAAgB,CAAC,EAAE,CAAC,CAAC;AAAA,EACvH,OAAO;AACL,kBAAc;AAAA,EAChB;AACA,QAAM,SAAS,cAAc,UAAU,CAAC;AACxC,QAAM,SAAS,cAAc,UAAU,CAAC;AACxC,MAAI,kBAAkB;AACtB,MAAI,iBAAiB;AAErB,MAAI,mBAAmB,cAAc;AACnC,sBAAkB,KAAK,IAAI,eAAe,kBAAkB,OAAO,cAAc;AACjF,aAAS,IAAI,GAAG,IAAI,eAAe,kBAAkB,KAAK,GAAG;AAC3D,YAAM,QAAQ,IAAI,KAAK,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO;AACzD,2BAAqB,KAAK,OAAO,SAAS,QAAQ,CAAC;AAAA,IACrD;AAAA,EACF,WAAW,mBAAyC,OAAO,OAAO,SAAS,eAAe,GAAG;AAC3F,qBAAiB,KAAK,IAAI,oBAAoB,OAAO,OAAO,SAAS,eAAe,IAAI,OAAO,cAAc;AAC7G,aAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK,GAAG;AAC1C,YAAM,QAAQ,IAAI,KAAK,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO;AACzD,0BAAoB,KAAK,KAAK;AAAA,IAChC;AAAA,EACF;AACA,MAAI,QAAQ;AACV,yBAAqB,QAAQ,WAAS;AACpC,eAAS,QAAQ,OAAO,OAAO,KAAK,CAAC;AAAA,IACvC,CAAC;AAAA,EACH;AACA,MAAI,QAAQ;AACV,wBAAoB,QAAQ,WAAS;AACnC,eAAS,OAAO,OAAO,OAAO,KAAK,CAAC;AAAA,IACtC,CAAC;AAAA,EACH;AACA,SAAO,aAAa;AACpB,MAAI,OAAO,qBAAqB;AAC9B,WAAO,mBAAmB;AAAA,EAC5B;AACA,MAAIA,UAAS;AACX,QAAI,qBAAqB,SAAS,KAAK,QAAQ;AAC7C,UAAI,OAAO,mBAAmB,aAAa;AACzC,cAAM,wBAAwB,OAAO,WAAW,WAAW;AAC3D,cAAM,oBAAoB,OAAO,WAAW,cAAc,eAAe;AACzE,cAAM,OAAO,oBAAoB;AACjC,YAAI,cAAc;AAChB,iBAAO,aAAa,OAAO,YAAY,IAAI;AAAA,QAC7C,OAAO;AACL,iBAAO,QAAQ,cAAc,iBAAiB,GAAG,OAAO,IAAI;AAC5D,cAAIC,eAAc;AAChB,mBAAO,QAAQ,OAAO,aAAa,IAAI,WAAW,QAAQ,KAAK;AAAA,UACjE;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAIA,eAAc;AAChB,iBAAO,YAAY,gBAAgB,GAAG,OAAO,IAAI;AAAA,QACnD;AAAA,MACF;AAAA,IACF,WAAW,oBAAoB,SAAS,KAAK,QAAQ;AACnD,UAAI,OAAO,mBAAmB,aAAa;AACzC,cAAM,wBAAwB,OAAO,WAAW,WAAW;AAC3D,cAAM,oBAAoB,OAAO,WAAW,cAAc,cAAc;AACxE,cAAM,OAAO,oBAAoB;AACjC,YAAI,cAAc;AAChB,iBAAO,aAAa,OAAO,YAAY,IAAI;AAAA,QAC7C,OAAO;AACL,iBAAO,QAAQ,cAAc,gBAAgB,GAAG,OAAO,IAAI;AAC3D,cAAIA,eAAc;AAChB,mBAAO,QAAQ,OAAO,aAAa,IAAI,WAAW,QAAQ,KAAK;AAAA,UACjE;AAAA,QACF;AAAA,MACF,OAAO;AACL,eAAO,YAAY,gBAAgB,GAAG,OAAO,IAAI;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AACA,SAAO,iBAAiB;AACxB,SAAO,iBAAiB;AACxB,MAAI,OAAO,cAAc,OAAO,WAAW,WAAW,CAAC,cAAc;AACnE,UAAM,aAAa;AAAA,MACjB;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA,cAAAA;AAAA,MACA;AAAA,MACA,cAAc;AAAA,IAChB;AACA,QAAI,MAAM,QAAQ,OAAO,WAAW,OAAO,GAAG;AAC5C,aAAO,WAAW,QAAQ,QAAQ,OAAK;AACrC,YAAI,CAAC,EAAE,aAAa,EAAE,OAAO;AAAM,YAAE,QAAQ,UAAU;AAAA,MACzD,CAAC;AAAA,IACH,WAAW,OAAO,WAAW,mBAAmB,OAAO,eAAe,OAAO,WAAW,QAAQ,OAAO,MAAM;AAC3G,aAAO,WAAW,QAAQ,QAAQ,UAAU;AAAA,IAC9C;AAAA,EACF;AACA,SAAO,KAAK,SAAS;AACvB;;;AC3Ie,SAAR,cAA+B;AACpC,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,OAAO,QAAQ,OAAO,WAAW,OAAO,OAAO,QAAQ;AAAS;AACrE,SAAO,aAAa;AACpB,QAAM,iBAAiB,CAAC;AACxB,SAAO,OAAO,QAAQ,aAAW;AAC/B,UAAM,QAAQ,OAAO,QAAQ,qBAAqB,cAAc,QAAQ,aAAa,yBAAyB,IAAI,IAAI,QAAQ;AAC9H,mBAAe,KAAK,IAAI;AAAA,EAC1B,CAAC;AACD,SAAO,OAAO,QAAQ,aAAW;AAC/B,YAAQ,gBAAgB,yBAAyB;AAAA,EACnD,CAAC;AACD,iBAAe,QAAQ,aAAW;AAChC,aAAS,OAAO,OAAO;AAAA,EACzB,CAAC;AACD,SAAO,aAAa;AACpB,SAAO,QAAQ,OAAO,WAAW,CAAC;AACpC;;;AClBA,IAAO,eAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AACF;;;ACPe,SAAR,cAA+B,QAAQ;AAC5C,QAAM,SAAS;AACf,MAAI,CAAC,OAAO,OAAO,iBAAiB,OAAO,OAAO,iBAAiB,OAAO,YAAY,OAAO,OAAO;AAAS;AAC7G,QAAM,KAAK,OAAO,OAAO,sBAAsB,cAAc,OAAO,KAAK,OAAO;AAChF,MAAI,OAAO,WAAW;AACpB,WAAO,sBAAsB;AAAA,EAC/B;AACA,KAAG,MAAM,SAAS;AAClB,KAAG,MAAM,SAAS,SAAS,aAAa;AACxC,MAAI,OAAO,WAAW;AACpB,0BAAsB,MAAM;AAC1B,aAAO,sBAAsB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;;;ACde,SAAR,kBAAmC;AACxC,QAAM,SAAS;AACf,MAAI,OAAO,OAAO,iBAAiB,OAAO,YAAY,OAAO,OAAO,SAAS;AAC3E;AAAA,EACF;AACA,MAAI,OAAO,WAAW;AACpB,WAAO,sBAAsB;AAAA,EAC/B;AACA,SAAO,OAAO,OAAO,sBAAsB,cAAc,OAAO,WAAW,EAAE,MAAM,SAAS;AAC5F,MAAI,OAAO,WAAW;AACpB,0BAAsB,MAAM;AAC1B,aAAO,sBAAsB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;;;ACZA,IAAO,sBAAQ;AAAA,EACb;AAAA,EACA;AACF;;;ACDA,SAAS,eAAe,UAAU,OAAO,MAAM;AAC7C,WAAS,cAAc,IAAI;AACzB,QAAI,CAAC,MAAM,OAAO,YAAY,KAAK,OAAO,UAAU;AAAG,aAAO;AAC9D,QAAI,GAAG;AAAc,WAAK,GAAG;AAC7B,UAAM,QAAQ,GAAG,QAAQ,QAAQ;AACjC,QAAI,CAAC,SAAS,CAAC,GAAG,aAAa;AAC7B,aAAO;AAAA,IACT;AACA,WAAO,SAAS,cAAc,GAAG,YAAY,EAAE,IAAI;AAAA,EACrD;AACA,SAAO,cAAc,IAAI;AAC3B;AACe,SAAR,aAA8BC,QAAO;AAC1C,QAAM,SAAS;AACf,QAAMC,YAAW,YAAY;AAC7B,QAAMC,UAAS,UAAU;AACzB,QAAM,OAAO,OAAO;AACpB,OAAK,QAAQ,KAAKF,MAAK;AACvB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC;AAAS;AACd,MAAI,CAAC,OAAO,iBAAiBA,OAAM,gBAAgB;AAAS;AAC5D,MAAI,OAAO,aAAa,OAAO,gCAAgC;AAC7D;AAAA,EACF;AACA,MAAI,CAAC,OAAO,aAAa,OAAO,WAAW,OAAO,MAAM;AACtD,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,IAAIA;AACR,MAAI,EAAE;AAAe,QAAI,EAAE;AAC3B,MAAI,WAAW,EAAE;AACjB,MAAI,OAAO,sBAAsB,WAAW;AAC1C,QAAI,CAAC,OAAO,UAAU,SAAS,QAAQ;AAAG;AAAA,EAC5C;AACA,MAAI,WAAW,KAAK,EAAE,UAAU;AAAG;AACnC,MAAI,YAAY,KAAK,EAAE,SAAS;AAAG;AACnC,MAAI,KAAK,aAAa,KAAK;AAAS;AAGpC,QAAM,uBAAuB,CAAC,CAAC,OAAO,kBAAkB,OAAO,mBAAmB;AAElF,QAAM,YAAYA,OAAM,eAAeA,OAAM,aAAa,IAAIA,OAAM;AACpE,MAAI,wBAAwB,EAAE,UAAU,EAAE,OAAO,cAAc,WAAW;AACxE,eAAW,UAAU,CAAC;AAAA,EACxB;AACA,QAAM,oBAAoB,OAAO,oBAAoB,OAAO,oBAAoB,IAAI,OAAO;AAC3F,QAAM,iBAAiB,CAAC,EAAE,EAAE,UAAU,EAAE,OAAO;AAG/C,MAAI,OAAO,cAAc,iBAAiB,eAAe,mBAAmB,QAAQ,IAAI,SAAS,QAAQ,iBAAiB,IAAI;AAC5H,WAAO,aAAa;AACpB;AAAA,EACF;AACA,MAAI,OAAO,cAAc;AACvB,QAAI,CAAC,SAAS,QAAQ,OAAO,YAAY;AAAG;AAAA,EAC9C;AACA,UAAQ,WAAW,EAAE;AACrB,UAAQ,WAAW,EAAE;AACrB,QAAM,SAAS,QAAQ;AACvB,QAAM,SAAS,QAAQ;AAIvB,QAAM,qBAAqB,OAAO,sBAAsB,OAAO;AAC/D,QAAM,qBAAqB,OAAO,sBAAsB,OAAO;AAC/D,MAAI,uBAAuB,UAAU,sBAAsB,UAAUE,QAAO,aAAa,qBAAqB;AAC5G,QAAI,uBAAuB,WAAW;AACpC,MAAAF,OAAM,eAAe;AAAA,IACvB,OAAO;AACL;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO,MAAM;AAAA,IAClB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,qBAAqB;AAAA,IACrB,aAAa;AAAA,IACb,aAAa;AAAA,EACf,CAAC;AACD,UAAQ,SAAS;AACjB,UAAQ,SAAS;AACjB,OAAK,iBAAiB,IAAI;AAC1B,SAAO,aAAa;AACpB,SAAO,WAAW;AAClB,SAAO,iBAAiB;AACxB,MAAI,OAAO,YAAY;AAAG,SAAK,qBAAqB;AACpD,MAAI,iBAAiB;AACrB,MAAI,SAAS,QAAQ,KAAK,iBAAiB,GAAG;AAC5C,qBAAiB;AACjB,QAAI,SAAS,aAAa,UAAU;AAClC,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AACA,MAAIC,UAAS,iBAAiBA,UAAS,cAAc,QAAQ,KAAK,iBAAiB,KAAKA,UAAS,kBAAkB,UAAU;AAC3H,IAAAA,UAAS,cAAc,KAAK;AAAA,EAC9B;AACA,QAAM,uBAAuB,kBAAkB,OAAO,kBAAkB,OAAO;AAC/E,OAAK,OAAO,iCAAiC,yBAAyB,CAAC,SAAS,mBAAmB;AACjG,MAAE,eAAe;AAAA,EACnB;AACA,MAAI,OAAO,OAAO,YAAY,OAAO,OAAO,SAAS,WAAW,OAAO,YAAY,OAAO,aAAa,CAAC,OAAO,SAAS;AACtH,WAAO,SAAS,aAAa;AAAA,EAC/B;AACA,SAAO,KAAK,cAAc,CAAC;AAC7B;;;AC7Ge,SAAR,YAA6BE,QAAO;AACzC,QAAMC,YAAW,YAAY;AAC7B,QAAM,SAAS;AACf,QAAM,OAAO,OAAO;AACpB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,EACF,IAAI;AACJ,MAAI,CAAC;AAAS;AACd,MAAI,CAAC,OAAO,iBAAiBD,OAAM,gBAAgB;AAAS;AAC5D,MAAI,IAAIA;AACR,MAAI,EAAE;AAAe,QAAI,EAAE;AAC3B,MAAI,CAAC,KAAK,WAAW;AACnB,QAAI,KAAK,eAAe,KAAK,aAAa;AACxC,aAAO,KAAK,qBAAqB,CAAC;AAAA,IACpC;AACA;AAAA,EACF;AACA,QAAM,eAAe,KAAK,QAAQ,UAAU,cAAY,SAAS,cAAc,EAAE,SAAS;AAC1F,MAAI,gBAAgB;AAAG,SAAK,QAAQ,YAAY,IAAI;AACpD,QAAM,cAAc,KAAK,QAAQ,SAAS,IAAI,KAAK,QAAQ,CAAC,IAAI;AAChE,QAAM,QAAQ,YAAY;AAC1B,QAAM,QAAQ,YAAY;AAC1B,MAAI,EAAE,yBAAyB;AAC7B,YAAQ,SAAS;AACjB,YAAQ,SAAS;AACjB;AAAA,EACF;AACA,MAAI,CAAC,OAAO,gBAAgB;AAC1B,QAAI,CAAC,EAAE,OAAO,QAAQ,KAAK,iBAAiB,GAAG;AAC7C,aAAO,aAAa;AAAA,IACtB;AACA,QAAI,KAAK,WAAW;AAClB,aAAO,OAAO,SAAS;AAAA,QACrB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO,OAAO,QAAQ;AAAA,QACtB,OAAO,OAAO,QAAQ;AAAA,QACtB,UAAU;AAAA,QACV,UAAU;AAAA,MACZ,CAAC;AACD,WAAK,iBAAiB,IAAI;AAAA,IAC5B;AACA;AAAA,EACF;AACA,MAAI,OAAO,uBAAuB,CAAC,OAAO,MAAM;AAC9C,QAAI,OAAO,WAAW,GAAG;AAEvB,UAAI,QAAQ,QAAQ,UAAU,OAAO,aAAa,OAAO,aAAa,KAAK,QAAQ,QAAQ,UAAU,OAAO,aAAa,OAAO,aAAa,GAAG;AAC9I,aAAK,YAAY;AACjB,aAAK,UAAU;AACf;AAAA,MACF;AAAA,IACF,WAAW,QAAQ,QAAQ,UAAU,OAAO,aAAa,OAAO,aAAa,KAAK,QAAQ,QAAQ,UAAU,OAAO,aAAa,OAAO,aAAa,GAAG;AACrJ;AAAA,IACF;AAAA,EACF;AACA,MAAIC,UAAS,eAAe;AAC1B,QAAI,EAAE,WAAWA,UAAS,iBAAiB,EAAE,OAAO,QAAQ,KAAK,iBAAiB,GAAG;AACnF,WAAK,UAAU;AACf,aAAO,aAAa;AACpB;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK,qBAAqB;AAC5B,WAAO,KAAK,aAAa,CAAC;AAAA,EAC5B;AACA,MAAI,EAAE,iBAAiB,EAAE,cAAc,SAAS;AAAG;AACnD,UAAQ,WAAW;AACnB,UAAQ,WAAW;AACnB,QAAM,QAAQ,QAAQ,WAAW,QAAQ;AACzC,QAAM,QAAQ,QAAQ,WAAW,QAAQ;AACzC,MAAI,OAAO,OAAO,aAAa,KAAK,KAAK,SAAS,IAAI,SAAS,CAAC,IAAI,OAAO,OAAO;AAAW;AAC7F,MAAI,OAAO,KAAK,gBAAgB,aAAa;AAC3C,QAAI;AACJ,QAAI,OAAO,aAAa,KAAK,QAAQ,aAAa,QAAQ,UAAU,OAAO,WAAW,KAAK,QAAQ,aAAa,QAAQ,QAAQ;AAC9H,WAAK,cAAc;AAAA,IACrB,OAAO;AAEL,UAAI,QAAQ,QAAQ,QAAQ,SAAS,IAAI;AACvC,qBAAa,KAAK,MAAM,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,IAAI,MAAM,KAAK;AACvE,aAAK,cAAc,OAAO,aAAa,IAAI,aAAa,OAAO,aAAa,KAAK,aAAa,OAAO;AAAA,MACvG;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK,aAAa;AACpB,WAAO,KAAK,qBAAqB,CAAC;AAAA,EACpC;AACA,MAAI,OAAO,KAAK,gBAAgB,aAAa;AAC3C,QAAI,QAAQ,aAAa,QAAQ,UAAU,QAAQ,aAAa,QAAQ,QAAQ;AAC9E,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AACA,MAAI,KAAK,eAAe,OAAO,QAAQ,OAAO,OAAO,QAAQ,OAAO,OAAO,KAAK,WAAW,KAAK,QAAQ,SAAS,GAAG;AAClH,SAAK,YAAY;AACjB;AAAA,EACF;AACA,MAAI,CAAC,KAAK,aAAa;AACrB;AAAA,EACF;AACA,SAAO,aAAa;AACpB,MAAI,CAAC,OAAO,WAAW,EAAE,YAAY;AACnC,MAAE,eAAe;AAAA,EACnB;AACA,MAAI,OAAO,4BAA4B,CAAC,OAAO,QAAQ;AACrD,MAAE,gBAAgB;AAAA,EACpB;AACA,MAAI,OAAO,OAAO,aAAa,IAAI,QAAQ;AAC3C,MAAI,cAAc,OAAO,aAAa,IAAI,QAAQ,WAAW,QAAQ,YAAY,QAAQ,WAAW,QAAQ;AAC5G,MAAI,OAAO,gBAAgB;AACzB,WAAO,KAAK,IAAI,IAAI,KAAK,MAAM,IAAI;AACnC,kBAAc,KAAK,IAAI,WAAW,KAAK,MAAM,IAAI;AAAA,EACnD;AACA,UAAQ,OAAO;AACf,UAAQ,OAAO;AACf,MAAI,KAAK;AACP,WAAO,CAAC;AACR,kBAAc,CAAC;AAAA,EACjB;AACA,QAAM,uBAAuB,OAAO;AACpC,SAAO,iBAAiB,OAAO,IAAI,SAAS;AAC5C,SAAO,mBAAmB,cAAc,IAAI,SAAS;AACrD,QAAM,SAAS,OAAO,OAAO,QAAQ,CAAC,OAAO;AAC7C,MAAI,CAAC,KAAK,SAAS;AACjB,QAAI,QAAQ;AACV,aAAO,QAAQ;AAAA,QACb,WAAW,OAAO;AAAA,MACpB,CAAC;AAAA,IACH;AACA,SAAK,iBAAiB,OAAO,aAAa;AAC1C,WAAO,cAAc,CAAC;AACtB,QAAI,OAAO,WAAW;AACpB,YAAM,MAAM,IAAI,OAAO,YAAY,iBAAiB;AAAA,QAClD,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AACD,aAAO,UAAU,cAAc,GAAG;AAAA,IACpC;AACA,SAAK,sBAAsB;AAE3B,QAAI,OAAO,eAAe,OAAO,mBAAmB,QAAQ,OAAO,mBAAmB,OAAO;AAC3F,aAAO,cAAc,IAAI;AAAA,IAC3B;AACA,WAAO,KAAK,mBAAmB,CAAC;AAAA,EAClC;AACA,MAAI;AACJ,MAAI,KAAK,WAAW,yBAAyB,OAAO,oBAAoB,UAAU,KAAK,IAAI,IAAI,KAAK,GAAG;AAErG,WAAO,QAAQ;AAAA,MACb,WAAW,OAAO;AAAA,MAClB,cAAc;AAAA,IAChB,CAAC;AACD,gBAAY;AAAA,EACd;AACA,SAAO,KAAK,cAAc,CAAC;AAC3B,OAAK,UAAU;AACf,OAAK,mBAAmB,OAAO,KAAK;AACpC,MAAI,sBAAsB;AAC1B,MAAI,kBAAkB,OAAO;AAC7B,MAAI,OAAO,qBAAqB;AAC9B,sBAAkB;AAAA,EACpB;AACA,MAAI,OAAO,GAAG;AACZ,QAAI,UAAU,CAAC,aAAa,KAAK,oBAAoB,OAAO,iBAAiB,OAAO,aAAa,IAAI,OAAO,OAAO,IAAI,OAAO,aAAa,IAAI;AAC7I,aAAO,QAAQ;AAAA,QACb,WAAW;AAAA,QACX,cAAc;AAAA,QACd,kBAAkB;AAAA,MACpB,CAAC;AAAA,IACH;AACA,QAAI,KAAK,mBAAmB,OAAO,aAAa,GAAG;AACjD,4BAAsB;AACtB,UAAI,OAAO,YAAY;AACrB,aAAK,mBAAmB,OAAO,aAAa,IAAI,KAAK,CAAC,OAAO,aAAa,IAAI,KAAK,iBAAiB,SAAS;AAAA,MAC/G;AAAA,IACF;AAAA,EACF,WAAW,OAAO,GAAG;AACnB,QAAI,UAAU,CAAC,aAAa,KAAK,oBAAoB,OAAO,iBAAiB,OAAO,aAAa,IAAI,OAAO,OAAO,IAAI,OAAO,aAAa,IAAI;AAC7I,aAAO,QAAQ;AAAA,QACb,WAAW;AAAA,QACX,cAAc;AAAA,QACd,kBAAkB,OAAO,OAAO,UAAU,OAAO,kBAAkB,SAAS,OAAO,qBAAqB,IAAI,KAAK,KAAK,WAAW,OAAO,eAAe,EAAE,CAAC;AAAA,MAC5J,CAAC;AAAA,IACH;AACA,QAAI,KAAK,mBAAmB,OAAO,aAAa,GAAG;AACjD,4BAAsB;AACtB,UAAI,OAAO,YAAY;AACrB,aAAK,mBAAmB,OAAO,aAAa,IAAI,KAAK,OAAO,aAAa,IAAI,KAAK,iBAAiB,SAAS;AAAA,MAC9G;AAAA,IACF;AAAA,EACF;AACA,MAAI,qBAAqB;AACvB,MAAE,0BAA0B;AAAA,EAC9B;AAGA,MAAI,CAAC,OAAO,kBAAkB,OAAO,mBAAmB,UAAU,KAAK,mBAAmB,KAAK,gBAAgB;AAC7G,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AACA,MAAI,CAAC,OAAO,kBAAkB,OAAO,mBAAmB,UAAU,KAAK,mBAAmB,KAAK,gBAAgB;AAC7G,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AACA,MAAI,CAAC,OAAO,kBAAkB,CAAC,OAAO,gBAAgB;AACpD,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AAGA,MAAI,OAAO,YAAY,GAAG;AACxB,QAAI,KAAK,IAAI,IAAI,IAAI,OAAO,aAAa,KAAK,oBAAoB;AAChE,UAAI,CAAC,KAAK,oBAAoB;AAC5B,aAAK,qBAAqB;AAC1B,gBAAQ,SAAS,QAAQ;AACzB,gBAAQ,SAAS,QAAQ;AACzB,aAAK,mBAAmB,KAAK;AAC7B,gBAAQ,OAAO,OAAO,aAAa,IAAI,QAAQ,WAAW,QAAQ,SAAS,QAAQ,WAAW,QAAQ;AACtG;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,mBAAmB,KAAK;AAC7B;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,OAAO,gBAAgB,OAAO;AAAS;AAG5C,MAAI,OAAO,YAAY,OAAO,SAAS,WAAW,OAAO,YAAY,OAAO,qBAAqB;AAC/F,WAAO,kBAAkB;AACzB,WAAO,oBAAoB;AAAA,EAC7B;AACA,MAAI,OAAO,OAAO,YAAY,OAAO,SAAS,WAAW,OAAO,UAAU;AACxE,WAAO,SAAS,YAAY;AAAA,EAC9B;AAEA,SAAO,eAAe,KAAK,gBAAgB;AAE3C,SAAO,aAAa,KAAK,gBAAgB;AAC3C;;;AC/Oe,SAAR,WAA4BC,QAAO;AACxC,QAAM,SAAS;AACf,QAAM,OAAO,OAAO;AACpB,QAAM,eAAe,KAAK,QAAQ,UAAU,cAAY,SAAS,cAAcA,OAAM,SAAS;AAC9F,MAAI,gBAAgB,GAAG;AACrB,SAAK,QAAQ,OAAO,cAAc,CAAC;AAAA,EACrC;AACA,MAAI,CAAC,iBAAiB,cAAc,cAAc,EAAE,SAASA,OAAM,IAAI,GAAG;AACxE,UAAM,UAAUA,OAAM,SAAS,oBAAoB,OAAO,QAAQ,YAAY,OAAO,QAAQ;AAC7F,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC;AAAS;AACd,MAAI,CAAC,OAAO,iBAAiBA,OAAM,gBAAgB;AAAS;AAC5D,MAAI,IAAIA;AACR,MAAI,EAAE;AAAe,QAAI,EAAE;AAC3B,MAAI,KAAK,qBAAqB;AAC5B,WAAO,KAAK,YAAY,CAAC;AAAA,EAC3B;AACA,OAAK,sBAAsB;AAC3B,MAAI,CAAC,KAAK,WAAW;AACnB,QAAI,KAAK,WAAW,OAAO,YAAY;AACrC,aAAO,cAAc,KAAK;AAAA,IAC5B;AACA,SAAK,UAAU;AACf,SAAK,cAAc;AACnB;AAAA,EACF;AAEA,MAAI,OAAO,cAAc,KAAK,WAAW,KAAK,cAAc,OAAO,mBAAmB,QAAQ,OAAO,mBAAmB,OAAO;AAC7H,WAAO,cAAc,KAAK;AAAA,EAC5B;AAGA,QAAM,eAAe,IAAI;AACzB,QAAM,WAAW,eAAe,KAAK;AAGrC,MAAI,OAAO,YAAY;AACrB,UAAM,WAAW,EAAE,QAAQ,EAAE,gBAAgB,EAAE,aAAa;AAC5D,WAAO,mBAAmB,YAAY,SAAS,CAAC,KAAK,EAAE,MAAM;AAC7D,WAAO,KAAK,aAAa,CAAC;AAC1B,QAAI,WAAW,OAAO,eAAe,KAAK,gBAAgB,KAAK;AAC7D,aAAO,KAAK,yBAAyB,CAAC;AAAA,IACxC;AAAA,EACF;AACA,OAAK,gBAAgB,IAAI;AACzB,WAAS,MAAM;AACb,QAAI,CAAC,OAAO;AAAW,aAAO,aAAa;AAAA,EAC7C,CAAC;AACD,MAAI,CAAC,KAAK,aAAa,CAAC,KAAK,WAAW,CAAC,OAAO,kBAAkB,QAAQ,SAAS,KAAK,KAAK,qBAAqB,KAAK,gBAAgB;AACrI,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB;AAAA,EACF;AACA,OAAK,YAAY;AACjB,OAAK,UAAU;AACf,OAAK,cAAc;AACnB,MAAI;AACJ,MAAI,OAAO,cAAc;AACvB,iBAAa,MAAM,OAAO,YAAY,CAAC,OAAO;AAAA,EAChD,OAAO;AACL,iBAAa,CAAC,KAAK;AAAA,EACrB;AACA,MAAI,OAAO,SAAS;AAClB;AAAA,EACF;AACA,MAAI,OAAO,OAAO,YAAY,OAAO,SAAS,SAAS;AACrD,WAAO,SAAS,WAAW;AAAA,MACzB;AAAA,IACF,CAAC;AACD;AAAA,EACF;AAGA,MAAI,YAAY;AAChB,MAAI,YAAY,OAAO,gBAAgB,CAAC;AACxC,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,IAAI,OAAO,qBAAqB,IAAI,OAAO,gBAAgB;AACrG,UAAMC,aAAY,IAAI,OAAO,qBAAqB,IAAI,IAAI,OAAO;AACjE,QAAI,OAAO,WAAW,IAAIA,UAAS,MAAM,aAAa;AACpD,UAAI,cAAc,WAAW,CAAC,KAAK,aAAa,WAAW,IAAIA,UAAS,GAAG;AACzE,oBAAY;AACZ,oBAAY,WAAW,IAAIA,UAAS,IAAI,WAAW,CAAC;AAAA,MACtD;AAAA,IACF,WAAW,cAAc,WAAW,CAAC,GAAG;AACtC,kBAAY;AACZ,kBAAY,WAAW,WAAW,SAAS,CAAC,IAAI,WAAW,WAAW,SAAS,CAAC;AAAA,IAClF;AAAA,EACF;AACA,MAAI,mBAAmB;AACvB,MAAI,kBAAkB;AACtB,MAAI,OAAO,QAAQ;AACjB,QAAI,OAAO,aAAa;AACtB,wBAAkB,OAAO,OAAO,WAAW,OAAO,OAAO,QAAQ,WAAW,OAAO,UAAU,OAAO,QAAQ,OAAO,SAAS,IAAI,OAAO,OAAO,SAAS;AAAA,IACzJ,WAAW,OAAO,OAAO;AACvB,yBAAmB;AAAA,IACrB;AAAA,EACF;AAEA,QAAM,SAAS,aAAa,WAAW,SAAS,KAAK;AACrD,QAAM,YAAY,YAAY,OAAO,qBAAqB,IAAI,IAAI,OAAO;AACzE,MAAI,WAAW,OAAO,cAAc;AAElC,QAAI,CAAC,OAAO,YAAY;AACtB,aAAO,QAAQ,OAAO,WAAW;AACjC;AAAA,IACF;AACA,QAAI,OAAO,mBAAmB,QAAQ;AACpC,UAAI,SAAS,OAAO;AAAiB,eAAO,QAAQ,OAAO,UAAU,OAAO,QAAQ,mBAAmB,YAAY,SAAS;AAAA;AAAO,eAAO,QAAQ,SAAS;AAAA,IAC7J;AACA,QAAI,OAAO,mBAAmB,QAAQ;AACpC,UAAI,QAAQ,IAAI,OAAO,iBAAiB;AACtC,eAAO,QAAQ,YAAY,SAAS;AAAA,MACtC,WAAW,oBAAoB,QAAQ,QAAQ,KAAK,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB;AAC5F,eAAO,QAAQ,eAAe;AAAA,MAChC,OAAO;AACL,eAAO,QAAQ,SAAS;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,OAAO;AAEL,QAAI,CAAC,OAAO,aAAa;AACvB,aAAO,QAAQ,OAAO,WAAW;AACjC;AAAA,IACF;AACA,UAAM,oBAAoB,OAAO,eAAe,EAAE,WAAW,OAAO,WAAW,UAAU,EAAE,WAAW,OAAO,WAAW;AACxH,QAAI,CAAC,mBAAmB;AACtB,UAAI,OAAO,mBAAmB,QAAQ;AACpC,eAAO,QAAQ,qBAAqB,OAAO,mBAAmB,YAAY,SAAS;AAAA,MACrF;AACA,UAAI,OAAO,mBAAmB,QAAQ;AACpC,eAAO,QAAQ,oBAAoB,OAAO,kBAAkB,SAAS;AAAA,MACvE;AAAA,IACF,WAAW,EAAE,WAAW,OAAO,WAAW,QAAQ;AAChD,aAAO,QAAQ,YAAY,SAAS;AAAA,IACtC,OAAO;AACL,aAAO,QAAQ,SAAS;AAAA,IAC1B;AAAA,EACF;AACF;;;ACrJA,IAAI;AACW,SAAR,WAA4B;AACjC,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,MAAM,GAAG,gBAAgB;AAAG;AAGhC,MAAI,OAAO,aAAa;AACtB,WAAO,cAAc;AAAA,EACvB;AAGA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,OAAO,WAAW,OAAO,OAAO,QAAQ;AAG1D,SAAO,iBAAiB;AACxB,SAAO,iBAAiB;AACxB,SAAO,WAAW;AAClB,SAAO,aAAa;AACpB,SAAO,oBAAoB;AAC3B,QAAM,gBAAgB,aAAa,OAAO;AAC1C,OAAK,OAAO,kBAAkB,UAAU,OAAO,gBAAgB,MAAM,OAAO,SAAS,CAAC,OAAO,eAAe,CAAC,OAAO,OAAO,kBAAkB,CAAC,eAAe;AAC3J,WAAO,QAAQ,OAAO,OAAO,SAAS,GAAG,GAAG,OAAO,IAAI;AAAA,EACzD,OAAO;AACL,QAAI,OAAO,OAAO,QAAQ,CAAC,WAAW;AACpC,aAAO,YAAY,OAAO,WAAW,GAAG,OAAO,IAAI;AAAA,IACrD,OAAO;AACL,aAAO,QAAQ,OAAO,aAAa,GAAG,OAAO,IAAI;AAAA,IACnD;AAAA,EACF;AACA,MAAI,OAAO,YAAY,OAAO,SAAS,WAAW,OAAO,SAAS,QAAQ;AACxE,iBAAa,OAAO;AACpB,cAAU,WAAW,MAAM;AACzB,UAAI,OAAO,YAAY,OAAO,SAAS,WAAW,OAAO,SAAS,QAAQ;AACxE,eAAO,SAAS,OAAO;AAAA,MACzB;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AAEA,SAAO,iBAAiB;AACxB,SAAO,iBAAiB;AACxB,MAAI,OAAO,OAAO,iBAAiB,aAAa,OAAO,UAAU;AAC/D,WAAO,cAAc;AAAA,EACvB;AACF;;;ACpDe,SAAR,QAAyB,GAAG;AACjC,QAAM,SAAS;AACf,MAAI,CAAC,OAAO;AAAS;AACrB,MAAI,CAAC,OAAO,YAAY;AACtB,QAAI,OAAO,OAAO;AAAe,QAAE,eAAe;AAClD,QAAI,OAAO,OAAO,4BAA4B,OAAO,WAAW;AAC9D,QAAE,gBAAgB;AAClB,QAAE,yBAAyB;AAAA,IAC7B;AAAA,EACF;AACF;;;ACVe,SAAR,WAA4B;AACjC,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC;AAAS;AACd,SAAO,oBAAoB,OAAO;AAClC,MAAI,OAAO,aAAa,GAAG;AACzB,WAAO,YAAY,CAAC,UAAU;AAAA,EAChC,OAAO;AACL,WAAO,YAAY,CAAC,UAAU;AAAA,EAChC;AAEA,MAAI,OAAO,cAAc;AAAG,WAAO,YAAY;AAC/C,SAAO,kBAAkB;AACzB,SAAO,oBAAoB;AAC3B,MAAI;AACJ,QAAM,iBAAiB,OAAO,aAAa,IAAI,OAAO,aAAa;AACnE,MAAI,mBAAmB,GAAG;AACxB,kBAAc;AAAA,EAChB,OAAO;AACL,mBAAe,OAAO,YAAY,OAAO,aAAa,KAAK;AAAA,EAC7D;AACA,MAAI,gBAAgB,OAAO,UAAU;AACnC,WAAO,eAAe,eAAe,CAAC,OAAO,YAAY,OAAO,SAAS;AAAA,EAC3E;AACA,SAAO,KAAK,gBAAgB,OAAO,WAAW,KAAK;AACrD;;;AC5Be,SAAR,OAAwB,GAAG;AAChC,QAAM,SAAS;AACf,uBAAqB,QAAQ,EAAE,MAAM;AACrC,SAAO,OAAO;AAChB;;;ACGA,IAAI,qBAAqB;AACzB,SAAS,qBAAqB;AAAC;AAC/B,IAAM,SAAS,CAAC,QAAQ,WAAW;AACjC,QAAMC,YAAW,YAAY;AAC7B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,CAAC,CAAC,OAAO;AACzB,QAAM,YAAY,WAAW,OAAO,qBAAqB;AACzD,QAAM,eAAe;AAGrB,KAAG,SAAS,EAAE,eAAe,OAAO,cAAc;AAAA,IAChD,SAAS;AAAA,EACX,CAAC;AACD,EAAAA,UAAS,SAAS,EAAE,eAAe,OAAO,aAAa;AAAA,IACrD,SAAS;AAAA,IACT;AAAA,EACF,CAAC;AACD,EAAAA,UAAS,SAAS,EAAE,aAAa,OAAO,YAAY;AAAA,IAClD,SAAS;AAAA,EACX,CAAC;AACD,EAAAA,UAAS,SAAS,EAAE,iBAAiB,OAAO,YAAY;AAAA,IACtD,SAAS;AAAA,EACX,CAAC;AACD,EAAAA,UAAS,SAAS,EAAE,cAAc,OAAO,YAAY;AAAA,IACnD,SAAS;AAAA,EACX,CAAC;AACD,EAAAA,UAAS,SAAS,EAAE,gBAAgB,OAAO,YAAY;AAAA,IACrD,SAAS;AAAA,EACX,CAAC;AAGD,MAAI,OAAO,iBAAiB,OAAO,0BAA0B;AAC3D,OAAG,SAAS,EAAE,SAAS,OAAO,SAAS,IAAI;AAAA,EAC7C;AACA,MAAI,OAAO,SAAS;AAClB,cAAU,SAAS,EAAE,UAAU,OAAO,QAAQ;AAAA,EAChD;AAGA,MAAI,OAAO,sBAAsB;AAC/B,WAAO,YAAY,EAAE,OAAO,OAAO,OAAO,UAAU,4CAA4C,yBAAyB,UAAU,IAAI;AAAA,EACzI,OAAO;AACL,WAAO,YAAY,EAAE,kBAAkB,UAAU,IAAI;AAAA,EACvD;AAGA,KAAG,SAAS,EAAE,QAAQ,OAAO,QAAQ;AAAA,IACnC,SAAS;AAAA,EACX,CAAC;AACH;AACA,SAAS,eAAe;AACtB,QAAM,SAAS;AACf,QAAMA,YAAW,YAAY;AAC7B,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO,eAAe,aAAa,KAAK,MAAM;AAC9C,SAAO,cAAc,YAAY,KAAK,MAAM;AAC5C,SAAO,aAAa,WAAW,KAAK,MAAM;AAC1C,MAAI,OAAO,SAAS;AAClB,WAAO,WAAW,SAAS,KAAK,MAAM;AAAA,EACxC;AACA,SAAO,UAAU,QAAQ,KAAK,MAAM;AACpC,SAAO,SAAS,OAAO,KAAK,MAAM;AAClC,MAAI,CAAC,oBAAoB;AACvB,IAAAA,UAAS,iBAAiB,cAAc,kBAAkB;AAC1D,yBAAqB;AAAA,EACvB;AACA,SAAO,QAAQ,IAAI;AACrB;AACA,SAAS,eAAe;AACtB,QAAM,SAAS;AACf,SAAO,QAAQ,KAAK;AACtB;AACA,IAAO,iBAAQ;AAAA,EACb;AAAA,EACA;AACF;;;ACzFA,IAAM,gBAAgB,CAAC,QAAQ,WAAW;AACxC,SAAO,OAAO,QAAQ,OAAO,QAAQ,OAAO,KAAK,OAAO;AAC1D;AACe,SAAR,gBAAiC;AACtC,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,cAAc,OAAO;AAC3B,MAAI,CAAC,eAAe,eAAe,OAAO,KAAK,WAAW,EAAE,WAAW;AAAG;AAG1E,QAAM,aAAa,OAAO,cAAc,aAAa,OAAO,OAAO,iBAAiB,OAAO,EAAE;AAC7F,MAAI,CAAC,cAAc,OAAO,sBAAsB;AAAY;AAC5D,QAAM,uBAAuB,cAAc,cAAc,YAAY,UAAU,IAAI;AACnF,QAAM,mBAAmB,wBAAwB,OAAO;AACxD,QAAM,cAAc,cAAc,QAAQ,MAAM;AAChD,QAAM,aAAa,cAAc,QAAQ,gBAAgB;AACzD,QAAM,aAAa,OAAO;AAC1B,MAAI,eAAe,CAAC,YAAY;AAC9B,OAAG,UAAU,OAAO,GAAG,OAAO,8BAA8B,GAAG,OAAO,mCAAmC;AACzG,WAAO,qBAAqB;AAAA,EAC9B,WAAW,CAAC,eAAe,YAAY;AACrC,OAAG,UAAU,IAAI,GAAG,OAAO,4BAA4B;AACvD,QAAI,iBAAiB,KAAK,QAAQ,iBAAiB,KAAK,SAAS,YAAY,CAAC,iBAAiB,KAAK,QAAQ,OAAO,KAAK,SAAS,UAAU;AACzI,SAAG,UAAU,IAAI,GAAG,OAAO,mCAAmC;AAAA,IAChE;AACA,WAAO,qBAAqB;AAAA,EAC9B;AAGA,GAAC,cAAc,cAAc,WAAW,EAAE,QAAQ,UAAQ;AACxD,UAAM,mBAAmB,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE;AACtD,UAAM,kBAAkB,iBAAiB,IAAI,KAAK,iBAAiB,IAAI,EAAE;AACzE,QAAI,oBAAoB,CAAC,iBAAiB;AACxC,aAAO,IAAI,EAAE,QAAQ;AAAA,IACvB;AACA,QAAI,CAAC,oBAAoB,iBAAiB;AACxC,aAAO,IAAI,EAAE,OAAO;AAAA,IACtB;AAAA,EACF,CAAC;AACD,QAAM,mBAAmB,iBAAiB,aAAa,iBAAiB,cAAc,OAAO;AAC7F,QAAM,cAAc,OAAO,SAAS,iBAAiB,kBAAkB,OAAO,iBAAiB;AAC/F,MAAI,oBAAoB,aAAa;AACnC,WAAO,gBAAgB;AAAA,EACzB;AACA,EAAAC,QAAO,OAAO,QAAQ,gBAAgB;AACtC,QAAM,YAAY,OAAO,OAAO;AAChC,SAAO,OAAO,QAAQ;AAAA,IACpB,gBAAgB,OAAO,OAAO;AAAA,IAC9B,gBAAgB,OAAO,OAAO;AAAA,IAC9B,gBAAgB,OAAO,OAAO;AAAA,EAChC,CAAC;AACD,MAAI,cAAc,CAAC,WAAW;AAC5B,WAAO,QAAQ;AAAA,EACjB,WAAW,CAAC,cAAc,WAAW;AACnC,WAAO,OAAO;AAAA,EAChB;AACA,SAAO,oBAAoB;AAC3B,SAAO,KAAK,qBAAqB,gBAAgB;AACjD,MAAI,eAAe,aAAa;AAC9B,WAAO,YAAY;AACnB,WAAO,WAAW,SAAS;AAC3B,WAAO,aAAa;AAAA,EACtB;AACA,SAAO,KAAK,cAAc,gBAAgB;AAC5C;;;ACrEe,SAAR,cAA+B,aAAa,OAAO,UAAU,aAAa;AAC/E,MAAI,CAAC,eAAe,SAAS,eAAe,CAAC;AAAa,WAAO;AACjE,MAAI,aAAa;AACjB,QAAMC,UAAS,UAAU;AACzB,QAAM,gBAAgB,SAAS,WAAWA,QAAO,cAAc,YAAY;AAC3E,QAAM,SAAS,OAAO,KAAK,WAAW,EAAE,IAAI,WAAS;AACnD,QAAI,OAAO,UAAU,YAAY,MAAM,QAAQ,GAAG,MAAM,GAAG;AACzD,YAAM,WAAW,WAAW,MAAM,OAAO,CAAC,CAAC;AAC3C,YAAM,QAAQ,gBAAgB;AAC9B,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,OAAO;AAAA,MACP;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,KAAK,CAAC,GAAG,MAAM,SAAS,EAAE,OAAO,EAAE,IAAI,SAAS,EAAE,OAAO,EAAE,CAAC;AACnE,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO,CAAC;AACZ,QAAI,SAAS,UAAU;AACrB,UAAIA,QAAO,WAAW,eAAe,UAAU,EAAE,SAAS;AACxD,qBAAa;AAAA,MACf;AAAA,IACF,WAAW,SAAS,YAAY,aAAa;AAC3C,mBAAa;AAAA,IACf;AAAA,EACF;AACA,SAAO,cAAc;AACvB;;;ACjCA,IAAO,sBAAQ;AAAA,EACb;AAAA,EACA;AACF;;;ACLA,SAAS,eAAe,SAAS,QAAQ;AACvC,QAAM,gBAAgB,CAAC;AACvB,UAAQ,QAAQ,UAAQ;AACtB,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,KAAK,IAAI,EAAE,QAAQ,gBAAc;AACtC,YAAI,KAAK,UAAU,GAAG;AACpB,wBAAc,KAAK,SAAS,UAAU;AAAA,QACxC;AAAA,MACF,CAAC;AAAA,IACH,WAAW,OAAO,SAAS,UAAU;AACnC,oBAAc,KAAK,SAAS,IAAI;AAAA,IAClC;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACe,SAAR,aAA8B;AACnC,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,WAAW,eAAe,CAAC,eAAe,OAAO,WAAW;AAAA,IAChE,aAAa,OAAO,OAAO,YAAY,OAAO,SAAS;AAAA,EACzD,GAAG;AAAA,IACD,cAAc,OAAO;AAAA,EACvB,GAAG;AAAA,IACD,OAAO;AAAA,EACT,GAAG;AAAA,IACD,QAAQ,OAAO,QAAQ,OAAO,KAAK,OAAO;AAAA,EAC5C,GAAG;AAAA,IACD,eAAe,OAAO,QAAQ,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,SAAS;AAAA,EAC7E,GAAG;AAAA,IACD,WAAW,OAAO;AAAA,EACpB,GAAG;AAAA,IACD,OAAO,OAAO;AAAA,EAChB,GAAG;AAAA,IACD,YAAY,OAAO;AAAA,EACrB,GAAG;AAAA,IACD,YAAY,OAAO,WAAW,OAAO;AAAA,EACvC,GAAG;AAAA,IACD,kBAAkB,OAAO;AAAA,EAC3B,CAAC,GAAG,OAAO,sBAAsB;AACjC,aAAW,KAAK,GAAG,QAAQ;AAC3B,KAAG,UAAU,IAAI,GAAG,UAAU;AAC9B,SAAO,qBAAqB;AAC9B;;;ACjDe,SAAR,gBAAiC;AACtC,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,KAAG,UAAU,OAAO,GAAG,UAAU;AACjC,SAAO,qBAAqB;AAC9B;;;ACNA,IAAO,kBAAQ;AAAA,EACb;AAAA,EACA;AACF;;;ACLA,SAAS,gBAAgB;AACvB,QAAM,SAAS;AACf,QAAM;AAAA,IACJ,UAAU;AAAA,IACV;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,MAAI,oBAAoB;AACtB,UAAM,iBAAiB,OAAO,OAAO,SAAS;AAC9C,UAAM,qBAAqB,OAAO,WAAW,cAAc,IAAI,OAAO,gBAAgB,cAAc,IAAI,qBAAqB;AAC7H,WAAO,WAAW,OAAO,OAAO;AAAA,EAClC,OAAO;AACL,WAAO,WAAW,OAAO,SAAS,WAAW;AAAA,EAC/C;AACA,MAAI,OAAO,mBAAmB,MAAM;AAClC,WAAO,iBAAiB,CAAC,OAAO;AAAA,EAClC;AACA,MAAI,OAAO,mBAAmB,MAAM;AAClC,WAAO,iBAAiB,CAAC,OAAO;AAAA,EAClC;AACA,MAAI,aAAa,cAAc,OAAO,UAAU;AAC9C,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,cAAc,OAAO,UAAU;AACjC,WAAO,KAAK,OAAO,WAAW,SAAS,QAAQ;AAAA,EACjD;AACF;AACA,IAAO,yBAAQ;AAAA,EACb;AACF;;;AC/BA,IAAO,mBAAQ;AAAA,EACb,MAAM;AAAA,EACN,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,OAAO;AAAA,EACP,SAAS;AAAA,EACT,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,mBAAmB;AAAA;AAAA,EAEnB,OAAO;AAAA,EACP,QAAQ;AAAA;AAAA,EAER,gCAAgC;AAAA;AAAA,EAEhC,WAAW;AAAA,EACX,KAAK;AAAA;AAAA,EAEL,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,YAAY;AAAA;AAAA,EAEZ,gBAAgB;AAAA;AAAA,EAEhB,kBAAkB;AAAA;AAAA,EAElB,QAAQ;AAAA;AAAA;AAAA,EAIR,aAAa;AAAA,EACb,iBAAiB;AAAA;AAAA,EAEjB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA;AAAA,EAEpB,mBAAmB;AAAA;AAAA,EAEnB,qBAAqB;AAAA,EACrB,0BAA0B;AAAA;AAAA,EAE1B,eAAe;AAAA;AAAA,EAEf,cAAc;AAAA;AAAA,EAEd,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA;AAAA,EAErB,mBAAmB;AAAA;AAAA,EAEnB,YAAY;AAAA,EACZ,iBAAiB;AAAA;AAAA,EAEjB,qBAAqB;AAAA;AAAA,EAErB,YAAY;AAAA;AAAA,EAEZ,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA;AAAA,EAErB,MAAM;AAAA,EACN,cAAc;AAAA,EACd,qBAAqB;AAAA;AAAA,EAErB,QAAQ;AAAA;AAAA,EAER,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,cAAc;AAAA;AAAA,EAEd,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,mBAAmB;AAAA;AAAA,EAEnB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA;AAAA,EAEzB,wBAAwB;AAAA;AAAA,EAExB,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,qBAAqB;AAAA;AAAA,EAErB,oBAAoB;AAAA;AAAA,EAEpB,cAAc;AAChB;;;ACnHe,SAAR,mBAAoC,QAAQ,kBAAkB;AACnE,SAAO,SAAS,aAAa,MAAM,CAAC,GAAG;AACrC,UAAM,kBAAkB,OAAO,KAAK,GAAG,EAAE,CAAC;AAC1C,UAAM,eAAe,IAAI,eAAe;AACxC,QAAI,OAAO,iBAAiB,YAAY,iBAAiB,MAAM;AAC7D,MAAAC,QAAO,kBAAkB,GAAG;AAC5B;AAAA,IACF;AACA,QAAI,CAAC,cAAc,cAAc,WAAW,EAAE,QAAQ,eAAe,KAAK,KAAK,OAAO,eAAe,MAAM,MAAM;AAC/G,aAAO,eAAe,IAAI;AAAA,QACxB,MAAM;AAAA,MACR;AAAA,IACF;AACA,QAAI,EAAE,mBAAmB,UAAU,aAAa,eAAe;AAC7D,MAAAA,QAAO,kBAAkB,GAAG;AAC5B;AAAA,IACF;AACA,QAAI,OAAO,eAAe,MAAM,MAAM;AACpC,aAAO,eAAe,IAAI;AAAA,QACxB,SAAS;AAAA,MACX;AAAA,IACF;AACA,QAAI,OAAO,OAAO,eAAe,MAAM,YAAY,EAAE,aAAa,OAAO,eAAe,IAAI;AAC1F,aAAO,eAAe,EAAE,UAAU;AAAA,IACpC;AACA,QAAI,CAAC,OAAO,eAAe;AAAG,aAAO,eAAe,IAAI;AAAA,QACtD,SAAS;AAAA,MACX;AACA,IAAAA,QAAO,kBAAkB,GAAG;AAAA,EAC9B;AACF;;;ACTA,IAAM,aAAa;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,mBAAmB,CAAC;AAC1B,IAAM,SAAN,MAAa;AAAA,EACX,eAAe,MAAM;AACnB,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,WAAW,KAAK,KAAK,CAAC,EAAE,eAAe,OAAO,UAAU,SAAS,KAAK,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,MAAM,UAAU;AACjH,eAAS,KAAK,CAAC;AAAA,IACjB,OAAO;AACL,OAAC,IAAI,MAAM,IAAI;AAAA,IACjB;AACA,QAAI,CAAC;AAAQ,eAAS,CAAC;AACvB,aAASC,QAAO,CAAC,GAAG,MAAM;AAC1B,QAAI,MAAM,CAAC,OAAO;AAAI,aAAO,KAAK;AAClC,UAAMC,YAAW,YAAY;AAC7B,QAAI,OAAO,MAAM,OAAO,OAAO,OAAO,YAAYA,UAAS,iBAAiB,OAAO,EAAE,EAAE,SAAS,GAAG;AACjG,YAAM,UAAU,CAAC;AACjB,MAAAA,UAAS,iBAAiB,OAAO,EAAE,EAAE,QAAQ,iBAAe;AAC1D,cAAM,YAAYD,QAAO,CAAC,GAAG,QAAQ;AAAA,UACnC,IAAI;AAAA,QACN,CAAC;AACD,gBAAQ,KAAK,IAAI,OAAO,SAAS,CAAC;AAAA,MACpC,CAAC;AAED,aAAO;AAAA,IACT;AAGA,UAAM,SAAS;AACf,WAAO,aAAa;AACpB,WAAO,UAAU,WAAW;AAC5B,WAAO,SAAS,UAAU;AAAA,MACxB,WAAW,OAAO;AAAA,IACpB,CAAC;AACD,WAAO,UAAU,WAAW;AAC5B,WAAO,kBAAkB,CAAC;AAC1B,WAAO,qBAAqB,CAAC;AAC7B,WAAO,UAAU,CAAC,GAAG,OAAO,WAAW;AACvC,QAAI,OAAO,WAAW,MAAM,QAAQ,OAAO,OAAO,GAAG;AACnD,aAAO,QAAQ,KAAK,GAAG,OAAO,OAAO;AAAA,IACvC;AACA,UAAM,mBAAmB,CAAC;AAC1B,WAAO,QAAQ,QAAQ,SAAO;AAC5B,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA,cAAc,mBAAmB,QAAQ,gBAAgB;AAAA,QACzD,IAAI,OAAO,GAAG,KAAK,MAAM;AAAA,QACzB,MAAM,OAAO,KAAK,KAAK,MAAM;AAAA,QAC7B,KAAK,OAAO,IAAI,KAAK,MAAM;AAAA,QAC3B,MAAM,OAAO,KAAK,KAAK,MAAM;AAAA,MAC/B,CAAC;AAAA,IACH,CAAC;AAGD,UAAM,eAAeA,QAAO,CAAC,GAAG,kBAAU,gBAAgB;AAG1D,WAAO,SAASA,QAAO,CAAC,GAAG,cAAc,kBAAkB,MAAM;AACjE,WAAO,iBAAiBA,QAAO,CAAC,GAAG,OAAO,MAAM;AAChD,WAAO,eAAeA,QAAO,CAAC,GAAG,MAAM;AAGvC,QAAI,OAAO,UAAU,OAAO,OAAO,IAAI;AACrC,aAAO,KAAK,OAAO,OAAO,EAAE,EAAE,QAAQ,eAAa;AACjD,eAAO,GAAG,WAAW,OAAO,OAAO,GAAG,SAAS,CAAC;AAAA,MAClD,CAAC;AAAA,IACH;AACA,QAAI,OAAO,UAAU,OAAO,OAAO,OAAO;AACxC,aAAO,MAAM,OAAO,OAAO,KAAK;AAAA,IAClC;AAGA,WAAO,OAAO,QAAQ;AAAA,MACpB,SAAS,OAAO,OAAO;AAAA,MACvB;AAAA;AAAA,MAEA,YAAY,CAAC;AAAA;AAAA,MAEb,QAAQ,CAAC;AAAA,MACT,YAAY,CAAC;AAAA,MACb,UAAU,CAAC;AAAA,MACX,iBAAiB,CAAC;AAAA;AAAA,MAElB,eAAe;AACb,eAAO,OAAO,OAAO,cAAc;AAAA,MACrC;AAAA,MACA,aAAa;AACX,eAAO,OAAO,OAAO,cAAc;AAAA,MACrC;AAAA;AAAA,MAEA,aAAa;AAAA,MACb,WAAW;AAAA;AAAA,MAEX,aAAa;AAAA,MACb,OAAO;AAAA;AAAA,MAEP,WAAW;AAAA,MACX,mBAAmB;AAAA,MACnB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA;AAAA,MAEX,gBAAgB,OAAO,OAAO;AAAA,MAC9B,gBAAgB,OAAO,OAAO;AAAA;AAAA,MAE9B,iBAAiB;AAAA,QACf,WAAW;AAAA,QACX,SAAS;AAAA,QACT,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,oBAAoB;AAAA;AAAA,QAEpB,mBAAmB,OAAO,OAAO;AAAA;AAAA,QAEjC,eAAe;AAAA,QACf,cAAc;AAAA;AAAA,QAEd,YAAY,CAAC;AAAA,QACb,qBAAqB;AAAA,QACrB,aAAa;AAAA,QACb,SAAS,CAAC;AAAA,MACZ;AAAA;AAAA,MAEA,YAAY;AAAA;AAAA,MAEZ,gBAAgB,OAAO,OAAO;AAAA,MAC9B,SAAS;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,UAAU;AAAA,QACV,MAAM;AAAA,MACR;AAAA;AAAA,MAEA,cAAc,CAAC;AAAA,MACf,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,KAAK,SAAS;AAGrB,QAAI,OAAO,OAAO,MAAM;AACtB,aAAO,KAAK;AAAA,IACd;AAIA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,SAAS;AACrB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,gBAAgB,UAAU,IAAI,OAAO,0BAA0B;AAC9E,UAAM,kBAAkB,aAAa,OAAO,CAAC,CAAC;AAC9C,WAAO,aAAa,OAAO,IAAI;AAAA,EACjC;AAAA,EACA,oBAAoB,OAAO;AACzB,WAAO,KAAK,cAAc,KAAK,OAAO,OAAO,aAAW,QAAQ,aAAa,yBAAyB,IAAI,MAAM,KAAK,EAAE,CAAC,CAAC;AAAA,EAC3H;AAAA,EACA,eAAe;AACb,UAAM,SAAS;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,SAAS,gBAAgB,UAAU,IAAI,OAAO,0BAA0B;AAAA,EACjF;AAAA,EACA,SAAS;AACP,UAAM,SAAS;AACf,QAAI,OAAO;AAAS;AACpB,WAAO,UAAU;AACjB,QAAI,OAAO,OAAO,YAAY;AAC5B,aAAO,cAAc;AAAA,IACvB;AACA,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,UAAU;AACR,UAAM,SAAS;AACf,QAAI,CAAC,OAAO;AAAS;AACrB,WAAO,UAAU;AACjB,QAAI,OAAO,OAAO,YAAY;AAC5B,aAAO,gBAAgB;AAAA,IACzB;AACA,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,YAAY,UAAU,OAAO;AAC3B,UAAM,SAAS;AACf,eAAW,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,CAAC;AAC5C,UAAM,MAAM,OAAO,aAAa;AAChC,UAAM,MAAM,OAAO,aAAa;AAChC,UAAM,WAAW,MAAM,OAAO,WAAW;AACzC,WAAO,YAAY,SAAS,OAAO,UAAU,cAAc,IAAI,KAAK;AACpE,WAAO,kBAAkB;AACzB,WAAO,oBAAoB;AAAA,EAC7B;AAAA,EACA,uBAAuB;AACrB,UAAM,SAAS;AACf,QAAI,CAAC,OAAO,OAAO,gBAAgB,CAAC,OAAO;AAAI;AAC/C,UAAM,MAAM,OAAO,GAAG,UAAU,MAAM,GAAG,EAAE,OAAO,eAAa;AAC7D,aAAO,UAAU,QAAQ,QAAQ,MAAM,KAAK,UAAU,QAAQ,OAAO,OAAO,sBAAsB,MAAM;AAAA,IAC1G,CAAC;AACD,WAAO,KAAK,qBAAqB,IAAI,KAAK,GAAG,CAAC;AAAA,EAChD;AAAA,EACA,gBAAgB,SAAS;AACvB,UAAM,SAAS;AACf,QAAI,OAAO;AAAW,aAAO;AAC7B,WAAO,QAAQ,UAAU,MAAM,GAAG,EAAE,OAAO,eAAa;AACtD,aAAO,UAAU,QAAQ,cAAc,MAAM,KAAK,UAAU,QAAQ,OAAO,OAAO,UAAU,MAAM;AAAA,IACpG,CAAC,EAAE,KAAK,GAAG;AAAA,EACb;AAAA,EACA,oBAAoB;AAClB,UAAM,SAAS;AACf,QAAI,CAAC,OAAO,OAAO,gBAAgB,CAAC,OAAO;AAAI;AAC/C,UAAM,UAAU,CAAC;AACjB,WAAO,OAAO,QAAQ,aAAW;AAC/B,YAAM,aAAa,OAAO,gBAAgB,OAAO;AACjD,cAAQ,KAAK;AAAA,QACX;AAAA,QACA;AAAA,MACF,CAAC;AACD,aAAO,KAAK,eAAe,SAAS,UAAU;AAAA,IAChD,CAAC;AACD,WAAO,KAAK,iBAAiB,OAAO;AAAA,EACtC;AAAA,EACA,qBAAqB,OAAO,WAAW,QAAQ,OAAO;AACpD,UAAM,SAAS;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN;AAAA,IACF,IAAI;AACJ,QAAI,MAAM;AACV,QAAI,OAAO,gBAAgB;AACzB,UAAI,YAAY,OAAO,WAAW,EAAE;AACpC,UAAI;AACJ,eAAS,IAAI,cAAc,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACvD,YAAI,OAAO,CAAC,KAAK,CAAC,WAAW;AAC3B,uBAAa,OAAO,CAAC,EAAE;AACvB,iBAAO;AACP,cAAI,YAAY;AAAY,wBAAY;AAAA,QAC1C;AAAA,MACF;AACA,eAAS,IAAI,cAAc,GAAG,KAAK,GAAG,KAAK,GAAG;AAC5C,YAAI,OAAO,CAAC,KAAK,CAAC,WAAW;AAC3B,uBAAa,OAAO,CAAC,EAAE;AACvB,iBAAO;AACP,cAAI,YAAY;AAAY,wBAAY;AAAA,QAC1C;AAAA,MACF;AAAA,IACF,OAAO;AAEL,UAAI,SAAS,WAAW;AACtB,iBAAS,IAAI,cAAc,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACvD,gBAAM,cAAc,QAAQ,WAAW,CAAC,IAAI,gBAAgB,CAAC,IAAI,WAAW,WAAW,IAAI,aAAa,WAAW,CAAC,IAAI,WAAW,WAAW,IAAI;AAClJ,cAAI,aAAa;AACf,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,OAAO;AAEL,iBAAS,IAAI,cAAc,GAAG,KAAK,GAAG,KAAK,GAAG;AAC5C,gBAAM,cAAc,WAAW,WAAW,IAAI,WAAW,CAAC,IAAI;AAC9D,cAAI,aAAa;AACf,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,UAAM,SAAS;AACf,QAAI,CAAC,UAAU,OAAO;AAAW;AACjC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,QAAI,OAAO,aAAa;AACtB,aAAO,cAAc;AAAA,IACvB;AACA,KAAC,GAAG,OAAO,GAAG,iBAAiB,kBAAkB,CAAC,EAAE,QAAQ,aAAW;AACrE,UAAI,QAAQ,UAAU;AACpB,6BAAqB,QAAQ,OAAO;AAAA,MACtC;AAAA,IACF,CAAC;AACD,WAAO,WAAW;AAClB,WAAO,aAAa;AACpB,WAAO,eAAe;AACtB,WAAO,oBAAoB;AAC3B,aAASE,gBAAe;AACtB,YAAM,iBAAiB,OAAO,eAAe,OAAO,YAAY,KAAK,OAAO;AAC5E,YAAM,eAAe,KAAK,IAAI,KAAK,IAAI,gBAAgB,OAAO,aAAa,CAAC,GAAG,OAAO,aAAa,CAAC;AACpG,aAAO,aAAa,YAAY;AAChC,aAAO,kBAAkB;AACzB,aAAO,oBAAoB;AAAA,IAC7B;AACA,QAAI;AACJ,QAAI,OAAO,OAAO,YAAY,OAAO,OAAO,SAAS,SAAS;AAC5D,MAAAA,cAAa;AACb,UAAI,OAAO,OAAO,YAAY;AAC5B,eAAO,iBAAiB;AAAA,MAC1B;AAAA,IACF,OAAO;AACL,WAAK,OAAO,OAAO,kBAAkB,UAAU,OAAO,OAAO,gBAAgB,MAAM,OAAO,SAAS,CAAC,OAAO,OAAO,gBAAgB;AAChI,qBAAa,OAAO,QAAQ,OAAO,OAAO,SAAS,GAAG,GAAG,OAAO,IAAI;AAAA,MACtE,OAAO;AACL,qBAAa,OAAO,QAAQ,OAAO,aAAa,GAAG,OAAO,IAAI;AAAA,MAChE;AACA,UAAI,CAAC,YAAY;AACf,QAAAA,cAAa;AAAA,MACf;AAAA,IACF;AACA,QAAI,OAAO,iBAAiB,aAAa,OAAO,UAAU;AACxD,aAAO,cAAc;AAAA,IACvB;AACA,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,gBAAgB,cAAc,aAAa,MAAM;AAC/C,UAAM,SAAS;AACf,UAAM,mBAAmB,OAAO,OAAO;AACvC,QAAI,CAAC,cAAc;AAEjB,qBAAe,qBAAqB,eAAe,aAAa;AAAA,IAClE;AACA,QAAI,iBAAiB,oBAAoB,iBAAiB,gBAAgB,iBAAiB,YAAY;AACrG,aAAO;AAAA,IACT;AACA,WAAO,GAAG,UAAU,OAAO,GAAG,OAAO,OAAO,yBAAyB,kBAAkB;AACvF,WAAO,GAAG,UAAU,IAAI,GAAG,OAAO,OAAO,yBAAyB,cAAc;AAChF,WAAO,qBAAqB;AAC5B,WAAO,OAAO,YAAY;AAC1B,WAAO,OAAO,QAAQ,aAAW;AAC/B,UAAI,iBAAiB,YAAY;AAC/B,gBAAQ,MAAM,QAAQ;AAAA,MACxB,OAAO;AACL,gBAAQ,MAAM,SAAS;AAAA,MACzB;AAAA,IACF,CAAC;AACD,WAAO,KAAK,iBAAiB;AAC7B,QAAI;AAAY,aAAO,OAAO;AAC9B,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB,WAAW;AACjC,UAAM,SAAS;AACf,QAAI,OAAO,OAAO,cAAc,SAAS,CAAC,OAAO,OAAO,cAAc;AAAO;AAC7E,WAAO,MAAM,cAAc;AAC3B,WAAO,eAAe,OAAO,OAAO,cAAc,gBAAgB,OAAO;AACzE,QAAI,OAAO,KAAK;AACd,aAAO,GAAG,UAAU,IAAI,GAAG,OAAO,OAAO,2BAA2B;AACpE,aAAO,GAAG,MAAM;AAAA,IAClB,OAAO;AACL,aAAO,GAAG,UAAU,OAAO,GAAG,OAAO,OAAO,2BAA2B;AACvE,aAAO,GAAG,MAAM;AAAA,IAClB;AACA,WAAO,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,SAAS;AACb,UAAM,SAAS;AACf,QAAI,OAAO;AAAS,aAAO;AAG3B,QAAI,KAAK,WAAW,OAAO,OAAO;AAClC,QAAI,OAAO,OAAO,UAAU;AAC1B,WAAK,SAAS,cAAc,EAAE;AAAA,IAChC;AACA,QAAI,CAAC,IAAI;AACP,aAAO;AAAA,IACT;AACA,OAAG,SAAS;AACZ,QAAI,GAAG,UAAU;AACf,aAAO,YAAY;AAAA,IACrB;AACA,UAAM,qBAAqB,MAAM;AAC/B,aAAO,KAAK,OAAO,OAAO,gBAAgB,IAAI,KAAK,EAAE,MAAM,GAAG,EAAE,KAAK,GAAG;AAAA,IAC1E;AACA,UAAM,aAAa,MAAM;AACvB,UAAI,MAAM,GAAG,cAAc,GAAG,WAAW,eAAe;AACtD,cAAM,MAAM,GAAG,WAAW,cAAc,mBAAmB,CAAC;AAE5D,eAAO;AAAA,MACT;AACA,aAAO,gBAAgB,IAAI,mBAAmB,CAAC,EAAE,CAAC;AAAA,IACpD;AAEA,QAAI,YAAY,WAAW;AAC3B,QAAI,CAAC,aAAa,OAAO,OAAO,gBAAgB;AAC9C,kBAAY,cAAc,OAAO,OAAO,OAAO,YAAY;AAC3D,SAAG,OAAO,SAAS;AACnB,sBAAgB,IAAI,IAAI,OAAO,OAAO,YAAY,EAAE,QAAQ,aAAW;AACrE,kBAAU,OAAO,OAAO;AAAA,MAC1B,CAAC;AAAA,IACH;AACA,WAAO,OAAO,QAAQ;AAAA,MACpB;AAAA,MACA;AAAA,MACA,UAAU,OAAO,YAAY,KAAK;AAAA,MAClC,SAAS;AAAA;AAAA,MAET,KAAK,GAAG,IAAI,YAAY,MAAM,SAAS,aAAa,IAAI,WAAW,MAAM;AAAA,MACzE,cAAc,OAAO,OAAO,cAAc,iBAAiB,GAAG,IAAI,YAAY,MAAM,SAAS,aAAa,IAAI,WAAW,MAAM;AAAA,MAC/H,UAAU,aAAa,WAAW,SAAS,MAAM;AAAA,IACnD,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,KAAK,IAAI;AACP,UAAM,SAAS;AACf,QAAI,OAAO;AAAa,aAAO;AAC/B,UAAM,UAAU,OAAO,MAAM,EAAE;AAC/B,QAAI,YAAY;AAAO,aAAO;AAC9B,WAAO,KAAK,YAAY;AAGxB,QAAI,OAAO,OAAO,aAAa;AAC7B,aAAO,cAAc;AAAA,IACvB;AAGA,WAAO,WAAW;AAGlB,WAAO,WAAW;AAGlB,WAAO,aAAa;AACpB,QAAI,OAAO,OAAO,eAAe;AAC/B,aAAO,cAAc;AAAA,IACvB;AAGA,QAAI,OAAO,OAAO,cAAc,OAAO,SAAS;AAC9C,aAAO,cAAc;AAAA,IACvB;AAGA,QAAI,OAAO,OAAO,QAAQ,OAAO,WAAW,OAAO,OAAO,QAAQ,SAAS;AACzE,aAAO,QAAQ,OAAO,OAAO,eAAe,OAAO,QAAQ,cAAc,GAAG,OAAO,OAAO,oBAAoB,OAAO,IAAI;AAAA,IAC3H,OAAO;AACL,aAAO,QAAQ,OAAO,OAAO,cAAc,GAAG,OAAO,OAAO,oBAAoB,OAAO,IAAI;AAAA,IAC7F;AAGA,QAAI,OAAO,OAAO,MAAM;AACtB,aAAO,WAAW;AAAA,IACpB;AAGA,WAAO,aAAa;AACpB,KAAC,GAAG,OAAO,GAAG,iBAAiB,kBAAkB,CAAC,EAAE,QAAQ,aAAW;AACrE,UAAI,QAAQ,UAAU;AACpB,6BAAqB,QAAQ,OAAO;AAAA,MACtC,OAAO;AACL,gBAAQ,iBAAiB,QAAQ,OAAK;AACpC,+BAAqB,QAAQ,EAAE,MAAM;AAAA,QACvC,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,YAAQ,MAAM;AAGd,WAAO,cAAc;AACrB,YAAQ,MAAM;AAGd,WAAO,KAAK,MAAM;AAClB,WAAO,KAAK,WAAW;AACvB,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,iBAAiB,MAAM,cAAc,MAAM;AACjD,UAAM,SAAS;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,OAAO,OAAO,WAAW,eAAe,OAAO,WAAW;AAC5D,aAAO;AAAA,IACT;AACA,WAAO,KAAK,eAAe;AAG3B,WAAO,cAAc;AAGrB,WAAO,aAAa;AAGpB,QAAI,OAAO,MAAM;AACf,aAAO,YAAY;AAAA,IACrB;AAGA,QAAI,aAAa;AACf,aAAO,cAAc;AACrB,SAAG,gBAAgB,OAAO;AAC1B,gBAAU,gBAAgB,OAAO;AACjC,UAAI,UAAU,OAAO,QAAQ;AAC3B,eAAO,QAAQ,aAAW;AACxB,kBAAQ,UAAU,OAAO,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,gBAAgB,OAAO,cAAc;AACxH,kBAAQ,gBAAgB,OAAO;AAC/B,kBAAQ,gBAAgB,yBAAyB;AAAA,QACnD,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,KAAK,SAAS;AAGrB,WAAO,KAAK,OAAO,eAAe,EAAE,QAAQ,eAAa;AACvD,aAAO,IAAI,SAAS;AAAA,IACtB,CAAC;AACD,QAAI,mBAAmB,OAAO;AAC5B,aAAO,GAAG,SAAS;AACnB,kBAAY,MAAM;AAAA,IACpB;AACA,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,eAAe,aAAa;AACjC,IAAAF,QAAO,kBAAkB,WAAW;AAAA,EACtC;AAAA,EACA,WAAW,mBAAmB;AAC5B,WAAO;AAAA,EACT;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,cAAc,KAAK;AACxB,QAAI,CAAC,OAAO,UAAU;AAAa,aAAO,UAAU,cAAc,CAAC;AACnE,UAAM,UAAU,OAAO,UAAU;AACjC,QAAI,OAAO,QAAQ,cAAc,QAAQ,QAAQ,GAAG,IAAI,GAAG;AACzD,cAAQ,KAAK,GAAG;AAAA,IAClB;AAAA,EACF;AAAA,EACA,OAAO,IAAI,QAAQ;AACjB,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,aAAO,QAAQ,OAAK,OAAO,cAAc,CAAC,CAAC;AAC3C,aAAO;AAAA,IACT;AACA,WAAO,cAAc,MAAM;AAC3B,WAAO;AAAA,EACT;AACF;AACA,OAAO,KAAK,UAAU,EAAE,QAAQ,oBAAkB;AAChD,SAAO,KAAK,WAAW,cAAc,CAAC,EAAE,QAAQ,iBAAe;AAC7D,WAAO,UAAU,WAAW,IAAI,WAAW,cAAc,EAAE,WAAW;AAAA,EACxE,CAAC;AACH,CAAC;AACD,OAAO,IAAI,CAAC,QAAQ,QAAQ,CAAC;AAC7B,IAAO,eAAQ;;;AC7kBA,SAAR,QAAyB;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,SAAS;AAAA,MACP,SAAS;AAAA,MACT,QAAQ,CAAC;AAAA,MACT,OAAO;AAAA,MACP,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,sBAAsB;AAAA,MACtB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,MAAI;AACJ,QAAMG,YAAW,YAAY;AAC7B,SAAO,UAAU;AAAA,IACf,OAAO,CAAC;AAAA,IACR,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,QAAQ,CAAC;AAAA,IACT,QAAQ;AAAA,IACR,YAAY,CAAC;AAAA,EACf;AACA,QAAM,UAAUA,UAAS,cAAc,KAAK;AAC5C,WAAS,YAAY,OAAO,OAAO;AACjC,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,OAAO,SAAS,OAAO,QAAQ,MAAM,KAAK,GAAG;AAC/C,aAAO,OAAO,QAAQ,MAAM,KAAK;AAAA,IACnC;AAEA,QAAI;AACJ,QAAI,OAAO,aAAa;AACtB,gBAAU,OAAO,YAAY,KAAK,QAAQ,OAAO,KAAK;AACtD,UAAI,OAAO,YAAY,UAAU;AAC/B,gBAAQ,YAAY;AACpB,kBAAU,QAAQ,SAAS,CAAC;AAAA,MAC9B;AAAA,IACF,WAAW,OAAO,WAAW;AAC3B,gBAAU,cAAc,cAAc;AAAA,IACxC,OAAO;AACL,gBAAU,cAAc,OAAO,OAAO,OAAO,UAAU;AAAA,IACzD;AACA,YAAQ,aAAa,2BAA2B,KAAK;AACrD,QAAI,CAAC,OAAO,aAAa;AACvB,cAAQ,YAAY;AAAA,IACtB;AACA,QAAI,OAAO;AAAO,aAAO,QAAQ,MAAM,KAAK,IAAI;AAChD,WAAO;AAAA,EACT;AACA,WAAS,OAAO,OAAO;AACrB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,IACR,IAAI,OAAO;AACX,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO,OAAO;AAClB,UAAM;AAAA,MACJ,MAAM;AAAA,MACN,IAAI;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,IAAI,OAAO;AACX,QAAI,CAAC,OAAO,OAAO,SAAS;AAC1B,aAAO,kBAAkB;AAAA,IAC3B;AACA,UAAM,cAAc,OAAO,eAAe;AAC1C,QAAI;AACJ,QAAI,OAAO;AAAc,mBAAa;AAAA;AAAa,mBAAa,OAAO,aAAa,IAAI,SAAS;AACjG,QAAI;AACJ,QAAI;AACJ,QAAI,gBAAgB;AAClB,oBAAc,KAAK,MAAM,gBAAgB,CAAC,IAAI,iBAAiB;AAC/D,qBAAe,KAAK,MAAM,gBAAgB,CAAC,IAAI,iBAAiB;AAAA,IAClE,OAAO;AACL,oBAAc,iBAAiB,iBAAiB,KAAK;AACrD,sBAAgB,SAAS,gBAAgB,kBAAkB;AAAA,IAC7D;AACA,QAAI,OAAO,cAAc;AACzB,QAAI,KAAK,cAAc;AACvB,QAAI,CAAC,QAAQ;AACX,aAAO,KAAK,IAAI,MAAM,CAAC;AACvB,WAAK,KAAK,IAAI,IAAI,OAAO,SAAS,CAAC;AAAA,IACrC;AACA,QAAI,UAAU,OAAO,WAAW,IAAI,KAAK,MAAM,OAAO,WAAW,CAAC,KAAK;AACvE,QAAI,UAAU,eAAe,cAAc;AACzC,cAAQ;AACR,UAAI,CAAC;AAAgB,kBAAU,OAAO,WAAW,CAAC;AAAA,IACpD,WAAW,UAAU,cAAc,cAAc;AAC/C,aAAO,CAAC;AACR,UAAI;AAAgB,kBAAU,OAAO,WAAW,CAAC;AAAA,IACnD;AACA,WAAO,OAAO,OAAO,SAAS;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,OAAO;AAAA,MACnB;AAAA,MACA;AAAA,IACF,CAAC;AACD,aAAS,aAAa;AACpB,aAAO,aAAa;AACpB,aAAO,eAAe;AACtB,aAAO,oBAAoB;AAC3B,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,iBAAiB,QAAQ,eAAe,MAAM,CAAC,OAAO;AACxD,UAAI,OAAO,eAAe,sBAAsB,WAAW,gBAAgB;AACzE,eAAO,OAAO,QAAQ,aAAW;AAC/B,kBAAQ,MAAM,UAAU,IAAI,GAAG;AAAA,QACjC,CAAC;AAAA,MACH;AACA,aAAO,eAAe;AACtB,WAAK,eAAe;AACpB;AAAA,IACF;AACA,QAAI,OAAO,OAAO,QAAQ,gBAAgB;AACxC,aAAO,OAAO,QAAQ,eAAe,KAAK,QAAQ;AAAA,QAChD;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ,SAAS,YAAY;AAC3B,gBAAM,iBAAiB,CAAC;AACxB,mBAAS,IAAI,MAAM,KAAK,IAAI,KAAK,GAAG;AAClC,2BAAe,KAAK,OAAO,CAAC,CAAC;AAAA,UAC/B;AACA,iBAAO;AAAA,QACT,EAAE;AAAA,MACJ,CAAC;AACD,UAAI,OAAO,OAAO,QAAQ,sBAAsB;AAC9C,mBAAW;AAAA,MACb,OAAO;AACL,aAAK,eAAe;AAAA,MACtB;AACA;AAAA,IACF;AACA,UAAM,iBAAiB,CAAC;AACxB,UAAM,gBAAgB,CAAC;AACvB,UAAM,gBAAgB,WAAS;AAC7B,UAAI,aAAa;AACjB,UAAI,QAAQ,GAAG;AACb,qBAAa,OAAO,SAAS;AAAA,MAC/B,WAAW,cAAc,OAAO,QAAQ;AAEtC,qBAAa,aAAa,OAAO;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AACA,QAAI,OAAO;AACT,aAAO,SAAS,iBAAiB,IAAI,OAAO,OAAO,0BAA0B,EAAE,QAAQ,aAAW;AAChG,gBAAQ,OAAO;AAAA,MACjB,CAAC;AAAA,IACH,OAAO;AACL,eAAS,IAAI,cAAc,KAAK,YAAY,KAAK,GAAG;AAClD,YAAI,IAAI,QAAQ,IAAI,IAAI;AACtB,gBAAM,aAAa,cAAc,CAAC;AAClC,iBAAO,SAAS,iBAAiB,IAAI,OAAO,OAAO,uCAAuC,uDAAuD,cAAc,EAAE,QAAQ,aAAW;AAClL,oBAAQ,OAAO;AAAA,UACjB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,UAAM,WAAW,SAAS,CAAC,OAAO,SAAS;AAC3C,UAAM,SAAS,SAAS,OAAO,SAAS,IAAI,OAAO;AACnD,aAAS,IAAI,UAAU,IAAI,QAAQ,KAAK,GAAG;AACzC,UAAI,KAAK,QAAQ,KAAK,IAAI;AACxB,cAAM,aAAa,cAAc,CAAC;AAClC,YAAI,OAAO,eAAe,eAAe,OAAO;AAC9C,wBAAc,KAAK,UAAU;AAAA,QAC/B,OAAO;AACL,cAAI,IAAI;AAAY,0BAAc,KAAK,UAAU;AACjD,cAAI,IAAI;AAAc,2BAAe,KAAK,UAAU;AAAA,QACtD;AAAA,MACF;AAAA,IACF;AACA,kBAAc,QAAQ,WAAS;AAC7B,aAAO,SAAS,OAAO,YAAY,OAAO,KAAK,GAAG,KAAK,CAAC;AAAA,IAC1D,CAAC;AACD,QAAI,QAAQ;AACV,eAAS,IAAI,eAAe,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AACtD,cAAM,QAAQ,eAAe,CAAC;AAC9B,eAAO,SAAS,QAAQ,YAAY,OAAO,KAAK,GAAG,KAAK,CAAC;AAAA,MAC3D;AAAA,IACF,OAAO;AACL,qBAAe,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACnC,qBAAe,QAAQ,WAAS;AAC9B,eAAO,SAAS,QAAQ,YAAY,OAAO,KAAK,GAAG,KAAK,CAAC;AAAA,MAC3D,CAAC;AAAA,IACH;AACA,oBAAgB,OAAO,UAAU,6BAA6B,EAAE,QAAQ,aAAW;AACjF,cAAQ,MAAM,UAAU,IAAI,GAAG;AAAA,IACjC,CAAC;AACD,eAAW;AAAA,EACb;AACA,WAASC,aAAY,QAAQ;AAC3B,QAAI,OAAO,WAAW,YAAY,YAAY,QAAQ;AACpD,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,YAAI,OAAO,CAAC;AAAG,iBAAO,QAAQ,OAAO,KAAK,OAAO,CAAC,CAAC;AAAA,MACrD;AAAA,IACF,OAAO;AACL,aAAO,QAAQ,OAAO,KAAK,MAAM;AAAA,IACnC;AACA,WAAO,IAAI;AAAA,EACb;AACA,WAASC,cAAa,QAAQ;AAC5B,UAAM,cAAc,OAAO;AAC3B,QAAI,iBAAiB,cAAc;AACnC,QAAI,oBAAoB;AACxB,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,YAAI,OAAO,CAAC;AAAG,iBAAO,QAAQ,OAAO,QAAQ,OAAO,CAAC,CAAC;AAAA,MACxD;AACA,uBAAiB,cAAc,OAAO;AACtC,0BAAoB,OAAO;AAAA,IAC7B,OAAO;AACL,aAAO,QAAQ,OAAO,QAAQ,MAAM;AAAA,IACtC;AACA,QAAI,OAAO,OAAO,QAAQ,OAAO;AAC/B,YAAM,QAAQ,OAAO,QAAQ;AAC7B,YAAM,WAAW,CAAC;AAClB,aAAO,KAAK,KAAK,EAAE,QAAQ,iBAAe;AACxC,cAAM,WAAW,MAAM,WAAW;AAClC,cAAM,gBAAgB,SAAS,aAAa,yBAAyB;AACrE,YAAI,eAAe;AACjB,mBAAS,aAAa,2BAA2B,SAAS,eAAe,EAAE,IAAI,iBAAiB;AAAA,QAClG;AACA,iBAAS,SAAS,aAAa,EAAE,IAAI,iBAAiB,IAAI;AAAA,MAC5D,CAAC;AACD,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,WAAO,IAAI;AACX,WAAO,QAAQ,gBAAgB,CAAC;AAAA,EAClC;AACA,WAASC,aAAY,eAAe;AAClC,QAAI,OAAO,kBAAkB,eAAe,kBAAkB;AAAM;AACpE,QAAI,cAAc,OAAO;AACzB,QAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,eAAS,IAAI,cAAc,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AACrD,eAAO,QAAQ,OAAO,OAAO,cAAc,CAAC,GAAG,CAAC;AAChD,YAAI,OAAO,OAAO,QAAQ,OAAO;AAC/B,iBAAO,OAAO,QAAQ,MAAM,cAAc,CAAC,CAAC;AAAA,QAC9C;AACA,YAAI,cAAc,CAAC,IAAI;AAAa,yBAAe;AACnD,sBAAc,KAAK,IAAI,aAAa,CAAC;AAAA,MACvC;AAAA,IACF,OAAO;AACL,aAAO,QAAQ,OAAO,OAAO,eAAe,CAAC;AAC7C,UAAI,OAAO,OAAO,QAAQ,OAAO;AAC/B,eAAO,OAAO,QAAQ,MAAM,aAAa;AAAA,MAC3C;AACA,UAAI,gBAAgB;AAAa,uBAAe;AAChD,oBAAc,KAAK,IAAI,aAAa,CAAC;AAAA,IACvC;AACA,WAAO,IAAI;AACX,WAAO,QAAQ,aAAa,CAAC;AAAA,EAC/B;AACA,WAASC,mBAAkB;AACzB,WAAO,QAAQ,SAAS,CAAC;AACzB,QAAI,OAAO,OAAO,QAAQ,OAAO;AAC/B,aAAO,QAAQ,QAAQ,CAAC;AAAA,IAC1B;AACA,WAAO,IAAI;AACX,WAAO,QAAQ,GAAG,CAAC;AAAA,EACrB;AACA,KAAG,cAAc,MAAM;AACrB,QAAI,CAAC,OAAO,OAAO,QAAQ;AAAS;AACpC,QAAI;AACJ,QAAI,OAAO,OAAO,aAAa,QAAQ,WAAW,aAAa;AAC7D,YAAM,SAAS,CAAC,GAAG,OAAO,SAAS,QAAQ,EAAE,OAAO,QAAM,GAAG,QAAQ,IAAI,OAAO,OAAO,0BAA0B,CAAC;AAClH,UAAI,UAAU,OAAO,QAAQ;AAC3B,eAAO,QAAQ,SAAS,CAAC,GAAG,MAAM;AAClC,4BAAoB;AACpB,eAAO,QAAQ,CAAC,SAAS,eAAe;AACtC,kBAAQ,aAAa,2BAA2B,UAAU;AAC1D,iBAAO,QAAQ,MAAM,UAAU,IAAI;AACnC,kBAAQ,OAAO;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,CAAC,mBAAmB;AACtB,aAAO,QAAQ,SAAS,OAAO,OAAO,QAAQ;AAAA,IAChD;AACA,WAAO,WAAW,KAAK,GAAG,OAAO,OAAO,+BAA+B;AACvE,WAAO,OAAO,sBAAsB;AACpC,WAAO,eAAe,sBAAsB;AAC5C,QAAI,CAAC,OAAO,OAAO,cAAc;AAC/B,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,gBAAgB,MAAM;AACvB,QAAI,CAAC,OAAO,OAAO,QAAQ;AAAS;AACpC,QAAI,OAAO,OAAO,WAAW,CAAC,OAAO,mBAAmB;AACtD,mBAAa,cAAc;AAC3B,uBAAiB,WAAW,MAAM;AAChC,eAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,sBAAsB,MAAM;AAC7B,QAAI,CAAC,OAAO,OAAO,QAAQ;AAAS;AACpC,QAAI,OAAO,OAAO,SAAS;AACzB,qBAAe,OAAO,WAAW,yBAAyB,GAAG,OAAO,eAAe;AAAA,IACrF;AAAA,EACF,CAAC;AACD,SAAO,OAAO,OAAO,SAAS;AAAA,IAC5B,aAAAH;AAAA,IACA,cAAAC;AAAA,IACA,aAAAC;AAAA,IACA,iBAAAC;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;ACjUe,SAAR,SAA0B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAMC,YAAW,YAAY;AAC7B,QAAMC,UAAS,UAAU;AACzB,SAAO,WAAW;AAAA,IAChB,SAAS;AAAA,EACX;AACA,eAAa;AAAA,IACX,UAAU;AAAA,MACR,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,YAAY;AAAA,IACd;AAAA,EACF,CAAC;AACD,WAAS,OAAOC,QAAO;AACrB,QAAI,CAAC,OAAO;AAAS;AACrB,UAAM;AAAA,MACJ,cAAc;AAAA,IAChB,IAAI;AACJ,QAAI,IAAIA;AACR,QAAI,EAAE;AAAe,UAAI,EAAE;AAC3B,UAAM,KAAK,EAAE,WAAW,EAAE;AAC1B,UAAM,aAAa,OAAO,OAAO,SAAS;AAC1C,UAAM,WAAW,cAAc,OAAO;AACtC,UAAM,aAAa,cAAc,OAAO;AACxC,UAAM,cAAc,OAAO;AAC3B,UAAM,eAAe,OAAO;AAC5B,UAAM,YAAY,OAAO;AACzB,UAAM,cAAc,OAAO;AAE3B,QAAI,CAAC,OAAO,mBAAmB,OAAO,aAAa,KAAK,gBAAgB,OAAO,WAAW,KAAK,eAAe,aAAa;AACzH,aAAO;AAAA,IACT;AACA,QAAI,CAAC,OAAO,mBAAmB,OAAO,aAAa,KAAK,eAAe,OAAO,WAAW,KAAK,aAAa,WAAW;AACpH,aAAO;AAAA,IACT;AACA,QAAI,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS;AACpD,aAAO;AAAA,IACT;AACA,QAAIF,UAAS,iBAAiBA,UAAS,cAAc,aAAaA,UAAS,cAAc,SAAS,YAAY,MAAM,WAAWA,UAAS,cAAc,SAAS,YAAY,MAAM,aAAa;AAC5L,aAAO;AAAA,IACT;AACA,QAAI,OAAO,OAAO,SAAS,mBAAmB,YAAY,cAAc,eAAe,gBAAgB,aAAa,cAAc;AAChI,UAAI,SAAS;AAEb,UAAI,eAAe,OAAO,IAAI,IAAI,OAAO,OAAO,0BAA0B,EAAE,SAAS,KAAK,eAAe,OAAO,IAAI,IAAI,OAAO,OAAO,kBAAkB,EAAE,WAAW,GAAG;AACtK,eAAO;AAAA,MACT;AACA,YAAM,KAAK,OAAO;AAClB,YAAM,cAAc,GAAG;AACvB,YAAM,eAAe,GAAG;AACxB,YAAM,cAAcC,QAAO;AAC3B,YAAM,eAAeA,QAAO;AAC5B,YAAM,eAAe,cAAc,EAAE;AACrC,UAAI;AAAK,qBAAa,QAAQ,GAAG;AACjC,YAAM,cAAc,CAAC,CAAC,aAAa,MAAM,aAAa,GAAG,GAAG,CAAC,aAAa,OAAO,aAAa,aAAa,GAAG,GAAG,CAAC,aAAa,MAAM,aAAa,MAAM,YAAY,GAAG,CAAC,aAAa,OAAO,aAAa,aAAa,MAAM,YAAY,CAAC;AACzO,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK,GAAG;AAC9C,cAAM,QAAQ,YAAY,CAAC;AAC3B,YAAI,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,cAAc;AACzF,cAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM;AAAG;AACtC,mBAAS;AAAA,QACX;AAAA,MACF;AACA,UAAI,CAAC;AAAQ,eAAO;AAAA,IACtB;AACA,QAAI,OAAO,aAAa,GAAG;AACzB,UAAI,YAAY,cAAc,eAAe,cAAc;AACzD,YAAI,EAAE;AAAgB,YAAE,eAAe;AAAA;AAAO,YAAE,cAAc;AAAA,MAChE;AACA,WAAK,cAAc,iBAAiB,CAAC,QAAQ,YAAY,gBAAgB;AAAK,eAAO,UAAU;AAC/F,WAAK,YAAY,gBAAgB,CAAC,QAAQ,cAAc,iBAAiB;AAAK,eAAO,UAAU;AAAA,IACjG,OAAO;AACL,UAAI,YAAY,cAAc,aAAa,aAAa;AACtD,YAAI,EAAE;AAAgB,YAAE,eAAe;AAAA;AAAO,YAAE,cAAc;AAAA,MAChE;AACA,UAAI,cAAc;AAAa,eAAO,UAAU;AAChD,UAAI,YAAY;AAAW,eAAO,UAAU;AAAA,IAC9C;AACA,SAAK,YAAY,EAAE;AACnB,WAAO;AAAA,EACT;AACA,WAAS,SAAS;AAChB,QAAI,OAAO,SAAS;AAAS;AAC7B,IAAAD,UAAS,iBAAiB,WAAW,MAAM;AAC3C,WAAO,SAAS,UAAU;AAAA,EAC5B;AACA,WAAS,UAAU;AACjB,QAAI,CAAC,OAAO,SAAS;AAAS;AAC9B,IAAAA,UAAS,oBAAoB,WAAW,MAAM;AAC9C,WAAO,SAAS,UAAU;AAAA,EAC5B;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,SAAS,SAAS;AAClC,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,QAAI,OAAO,SAAS,SAAS;AAC3B,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,SAAO,OAAO,OAAO,UAAU;AAAA,IAC7B;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;AC7Ge,SAAR,WAA4B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAMG,UAAS,UAAU;AACzB,eAAa;AAAA,IACX,YAAY;AAAA,MACV,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,aAAa;AAAA,MACb,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,eAAe;AAAA,IACjB;AAAA,EACF,CAAC;AACD,SAAO,aAAa;AAAA,IAClB,SAAS;AAAA,EACX;AACA,MAAIC;AACJ,MAAI,iBAAiB,IAAI;AACzB,MAAI;AACJ,QAAM,oBAAoB,CAAC;AAC3B,WAAS,UAAU,GAAG;AAEpB,UAAM,aAAa;AACnB,UAAM,cAAc;AACpB,UAAM,cAAc;AACpB,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK;AAGT,QAAI,YAAY,GAAG;AACjB,WAAK,EAAE;AAAA,IACT;AACA,QAAI,gBAAgB,GAAG;AACrB,WAAK,CAAC,EAAE,aAAa;AAAA,IACvB;AACA,QAAI,iBAAiB,GAAG;AACtB,WAAK,CAAC,EAAE,cAAc;AAAA,IACxB;AACA,QAAI,iBAAiB,GAAG;AACtB,WAAK,CAAC,EAAE,cAAc;AAAA,IACxB;AAGA,QAAI,UAAU,KAAK,EAAE,SAAS,EAAE,iBAAiB;AAC/C,WAAK;AACL,WAAK;AAAA,IACP;AACA,SAAK,KAAK;AACV,SAAK,KAAK;AACV,QAAI,YAAY,GAAG;AACjB,WAAK,EAAE;AAAA,IACT;AACA,QAAI,YAAY,GAAG;AACjB,WAAK,EAAE;AAAA,IACT;AACA,QAAI,EAAE,YAAY,CAAC,IAAI;AAErB,WAAK;AACL,WAAK;AAAA,IACP;AACA,SAAK,MAAM,OAAO,EAAE,WAAW;AAC7B,UAAI,EAAE,cAAc,GAAG;AAErB,cAAM;AACN,cAAM;AAAA,MACR,OAAO;AAEL,cAAM;AACN,cAAM;AAAA,MACR;AAAA,IACF;AAGA,QAAI,MAAM,CAAC,IAAI;AACb,WAAK,KAAK,IAAI,KAAK;AAAA,IACrB;AACA,QAAI,MAAM,CAAC,IAAI;AACb,WAAK,KAAK,IAAI,KAAK;AAAA,IACrB;AACA,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACF;AACA,WAAS,mBAAmB;AAC1B,QAAI,CAAC,OAAO;AAAS;AACrB,WAAO,eAAe;AAAA,EACxB;AACA,WAAS,mBAAmB;AAC1B,QAAI,CAAC,OAAO;AAAS;AACrB,WAAO,eAAe;AAAA,EACxB;AACA,WAAS,cAAc,UAAU;AAC/B,QAAI,OAAO,OAAO,WAAW,kBAAkB,SAAS,QAAQ,OAAO,OAAO,WAAW,gBAAgB;AAEvG,aAAO;AAAA,IACT;AACA,QAAI,OAAO,OAAO,WAAW,iBAAiB,IAAI,IAAI,iBAAiB,OAAO,OAAO,WAAW,eAAe;AAE7G,aAAO;AAAA,IACT;AAKA,QAAI,SAAS,SAAS,KAAK,IAAI,IAAI,iBAAiB,IAAI;AAEtD,aAAO;AAAA,IACT;AAaA,QAAI,SAAS,YAAY,GAAG;AAC1B,WAAK,CAAC,OAAO,SAAS,OAAO,OAAO,SAAS,CAAC,OAAO,WAAW;AAC9D,eAAO,UAAU;AACjB,aAAK,UAAU,SAAS,GAAG;AAAA,MAC7B;AAAA,IACF,YAAY,CAAC,OAAO,eAAe,OAAO,OAAO,SAAS,CAAC,OAAO,WAAW;AAC3E,aAAO,UAAU;AACjB,WAAK,UAAU,SAAS,GAAG;AAAA,IAC7B;AAEA,qBAAiB,IAAID,QAAO,KAAK,EAAE,QAAQ;AAE3C,WAAO;AAAA,EACT;AACA,WAAS,cAAc,UAAU;AAC/B,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,SAAS,YAAY,GAAG;AAC1B,UAAI,OAAO,SAAS,CAAC,OAAO,OAAO,QAAQ,OAAO,gBAAgB;AAEhE,eAAO;AAAA,MACT;AAAA,IACF,WAAW,OAAO,eAAe,CAAC,OAAO,OAAO,QAAQ,OAAO,gBAAgB;AAE7E,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,WAAS,OAAOE,QAAO;AACrB,QAAI,IAAIA;AACR,QAAI,sBAAsB;AAC1B,QAAI,CAAC,OAAO;AAAS;AACrB,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,OAAO,OAAO,SAAS;AACzB,QAAE,eAAe;AAAA,IACnB;AACA,QAAI,WAAW,OAAO;AACtB,QAAI,OAAO,OAAO,WAAW,iBAAiB,aAAa;AACzD,iBAAW,SAAS,cAAc,OAAO,OAAO,WAAW,YAAY;AAAA,IACzE;AACA,UAAM,yBAAyB,YAAY,SAAS,SAAS,EAAE,MAAM;AACrE,QAAI,CAAC,OAAO,gBAAgB,CAAC,0BAA0B,CAAC,OAAO;AAAgB,aAAO;AACtF,QAAI,EAAE;AAAe,UAAI,EAAE;AAC3B,QAAI,QAAQ;AACZ,UAAM,YAAY,OAAO,eAAe,KAAK;AAC7C,UAAM,OAAO,UAAU,CAAC;AACxB,QAAI,OAAO,aAAa;AACtB,UAAI,OAAO,aAAa,GAAG;AACzB,YAAI,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,MAAM;AAAG,kBAAQ,CAAC,KAAK,SAAS;AAAA;AAAe,iBAAO;AAAA,MAClG,WAAW,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,MAAM;AAAG,gBAAQ,CAAC,KAAK;AAAA;AAAY,eAAO;AAAA,IAC7F,OAAO;AACL,cAAQ,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,SAAS,YAAY,CAAC,KAAK;AAAA,IAC3F;AACA,QAAI,UAAU;AAAG,aAAO;AACxB,QAAI,OAAO;AAAQ,cAAQ,CAAC;AAG5B,QAAI,YAAY,OAAO,aAAa,IAAI,QAAQ,OAAO;AACvD,QAAI,aAAa,OAAO,aAAa;AAAG,kBAAY,OAAO,aAAa;AACxE,QAAI,aAAa,OAAO,aAAa;AAAG,kBAAY,OAAO,aAAa;AASxE,0BAAsB,OAAO,OAAO,OAAO,OAAO,EAAE,cAAc,OAAO,aAAa,KAAK,cAAc,OAAO,aAAa;AAC7H,QAAI,uBAAuB,OAAO,OAAO;AAAQ,QAAE,gBAAgB;AACnE,QAAI,CAAC,OAAO,OAAO,YAAY,CAAC,OAAO,OAAO,SAAS,SAAS;AAE9D,YAAM,WAAW;AAAA,QACf,MAAM,IAAI;AAAA,QACV,OAAO,KAAK,IAAI,KAAK;AAAA,QACrB,WAAW,KAAK,KAAK,KAAK;AAAA,QAC1B,KAAKA;AAAA,MACP;AAGA,UAAI,kBAAkB,UAAU,GAAG;AACjC,0BAAkB,MAAM;AAAA,MAC1B;AAEA,YAAM,YAAY,kBAAkB,SAAS,kBAAkB,kBAAkB,SAAS,CAAC,IAAI;AAC/F,wBAAkB,KAAK,QAAQ;AAQ/B,UAAI,WAAW;AACb,YAAI,SAAS,cAAc,UAAU,aAAa,SAAS,QAAQ,UAAU,SAAS,SAAS,OAAO,UAAU,OAAO,KAAK;AAC1H,wBAAc,QAAQ;AAAA,QACxB;AAAA,MACF,OAAO;AACL,sBAAc,QAAQ;AAAA,MACxB;AAIA,UAAI,cAAc,QAAQ,GAAG;AAC3B,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AAOL,YAAM,WAAW;AAAA,QACf,MAAM,IAAI;AAAA,QACV,OAAO,KAAK,IAAI,KAAK;AAAA,QACrB,WAAW,KAAK,KAAK,KAAK;AAAA,MAC5B;AACA,YAAM,oBAAoB,uBAAuB,SAAS,OAAO,oBAAoB,OAAO,OAAO,SAAS,SAAS,oBAAoB,SAAS,SAAS,cAAc,oBAAoB;AAC7L,UAAI,CAAC,mBAAmB;AACtB,8BAAsB;AACtB,YAAI,WAAW,OAAO,aAAa,IAAI,QAAQ,OAAO;AACtD,cAAM,eAAe,OAAO;AAC5B,cAAM,SAAS,OAAO;AACtB,YAAI,YAAY,OAAO,aAAa;AAAG,qBAAW,OAAO,aAAa;AACtE,YAAI,YAAY,OAAO,aAAa;AAAG,qBAAW,OAAO,aAAa;AACtE,eAAO,cAAc,CAAC;AACtB,eAAO,aAAa,QAAQ;AAC5B,eAAO,eAAe;AACtB,eAAO,kBAAkB;AACzB,eAAO,oBAAoB;AAC3B,YAAI,CAAC,gBAAgB,OAAO,eAAe,CAAC,UAAU,OAAO,OAAO;AAClE,iBAAO,oBAAoB;AAAA,QAC7B;AACA,YAAI,OAAO,OAAO,MAAM;AACtB,iBAAO,QAAQ;AAAA,YACb,WAAW,SAAS,YAAY,IAAI,SAAS;AAAA,YAC7C,cAAc;AAAA,UAChB,CAAC;AAAA,QACH;AACA,YAAI,OAAO,OAAO,SAAS,QAAQ;AAYjC,uBAAaD,QAAO;AACpB,UAAAA,WAAU;AACV,cAAI,kBAAkB,UAAU,IAAI;AAClC,8BAAkB,MAAM;AAAA,UAC1B;AAEA,gBAAM,YAAY,kBAAkB,SAAS,kBAAkB,kBAAkB,SAAS,CAAC,IAAI;AAC/F,gBAAM,aAAa,kBAAkB,CAAC;AACtC,4BAAkB,KAAK,QAAQ;AAC/B,cAAI,cAAc,SAAS,QAAQ,UAAU,SAAS,SAAS,cAAc,UAAU,YAAY;AAEjG,8BAAkB,OAAO,CAAC;AAAA,UAC5B,WAAW,kBAAkB,UAAU,MAAM,SAAS,OAAO,WAAW,OAAO,OAAO,WAAW,QAAQ,SAAS,SAAS,KAAK,SAAS,SAAS,GAAG;AAOnJ,kBAAM,kBAAkB,QAAQ,IAAI,MAAM;AAC1C,kCAAsB;AACtB,8BAAkB,OAAO,CAAC;AAC1B,YAAAA,WAAU,SAAS,MAAM;AACvB,qBAAO,eAAe,OAAO,OAAO,OAAO,MAAM,QAAW,eAAe;AAAA,YAC7E,GAAG,CAAC;AAAA,UACN;AAEA,cAAI,CAACA,UAAS;AAIZ,YAAAA,WAAU,SAAS,MAAM;AACvB,oBAAM,kBAAkB;AACxB,oCAAsB;AACtB,gCAAkB,OAAO,CAAC;AAC1B,qBAAO,eAAe,OAAO,OAAO,OAAO,MAAM,QAAW,eAAe;AAAA,YAC7E,GAAG,GAAG;AAAA,UACR;AAAA,QACF;AAGA,YAAI,CAAC;AAAmB,eAAK,UAAU,CAAC;AAGxC,YAAI,OAAO,OAAO,YAAY,OAAO,OAAO;AAA8B,iBAAO,SAAS,KAAK;AAE/F,YAAI,aAAa,OAAO,aAAa,KAAK,aAAa,OAAO,aAAa;AAAG,iBAAO;AAAA,MACvF;AAAA,IACF;AACA,QAAI,EAAE;AAAgB,QAAE,eAAe;AAAA;AAAO,QAAE,cAAc;AAC9D,WAAO;AAAA,EACT;AACA,WAASE,QAAO,QAAQ;AACtB,QAAI,WAAW,OAAO;AACtB,QAAI,OAAO,OAAO,WAAW,iBAAiB,aAAa;AACzD,iBAAW,SAAS,cAAc,OAAO,OAAO,WAAW,YAAY;AAAA,IACzE;AACA,aAAS,MAAM,EAAE,cAAc,gBAAgB;AAC/C,aAAS,MAAM,EAAE,cAAc,gBAAgB;AAC/C,aAAS,MAAM,EAAE,SAAS,MAAM;AAAA,EAClC;AACA,WAAS,SAAS;AAChB,QAAI,OAAO,OAAO,SAAS;AACzB,aAAO,UAAU,oBAAoB,SAAS,MAAM;AACpD,aAAO;AAAA,IACT;AACA,QAAI,OAAO,WAAW;AAAS,aAAO;AACtC,IAAAA,QAAO,kBAAkB;AACzB,WAAO,WAAW,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,WAAS,UAAU;AACjB,QAAI,OAAO,OAAO,SAAS;AACzB,aAAO,UAAU,iBAAiB,OAAO,MAAM;AAC/C,aAAO;AAAA,IACT;AACA,QAAI,CAAC,OAAO,WAAW;AAAS,aAAO;AACvC,IAAAA,QAAO,qBAAqB;AAC5B,WAAO,WAAW,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,CAAC,OAAO,OAAO,WAAW,WAAW,OAAO,OAAO,SAAS;AAC9D,cAAQ;AAAA,IACV;AACA,QAAI,OAAO,OAAO,WAAW;AAAS,aAAO;AAAA,EAC/C,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,QAAI,OAAO,OAAO,SAAS;AACzB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,WAAW;AAAS,cAAQ;AAAA,EACzC,CAAC;AACD,SAAO,OAAO,OAAO,YAAY;AAAA,IAC/B;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;AC7Xe,SAAR,0BAA2C,QAAQ,gBAAgB,QAAQ,YAAY;AAC5F,MAAI,OAAO,OAAO,gBAAgB;AAChC,WAAO,KAAK,UAAU,EAAE,QAAQ,SAAO;AACrC,UAAI,CAAC,OAAO,GAAG,KAAK,OAAO,SAAS,MAAM;AACxC,YAAI,UAAU,gBAAgB,OAAO,IAAI,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC;AACjE,YAAI,CAAC,SAAS;AACZ,oBAAU,cAAc,OAAO,WAAW,GAAG,CAAC;AAC9C,kBAAQ,YAAY,WAAW,GAAG;AAClC,iBAAO,GAAG,OAAO,OAAO;AAAA,QAC1B;AACA,eAAO,GAAG,IAAI;AACd,uBAAe,GAAG,IAAI;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;;;AChBe,SAAR,WAA4B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,YAAY;AAAA,MACV,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,eAAe;AAAA,MACf,aAAa;AAAA,MACb,WAAW;AAAA,MACX,yBAAyB;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,SAAO,aAAa;AAAA,IAClB,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AACA,QAAM,oBAAoB,QAAM;AAC9B,QAAI,CAAC,MAAM,QAAQ,EAAE;AAAG,WAAK,CAAC,EAAE,EAAE,OAAO,OAAK,CAAC,CAAC,CAAC;AACjD,WAAO;AAAA,EACT;AACA,WAAS,MAAM,IAAI;AACjB,QAAI;AACJ,QAAI,MAAM,OAAO,OAAO,YAAY,OAAO,WAAW;AACpD,YAAM,OAAO,GAAG,WAAW,cAAc,EAAE;AAC3C,UAAI;AAAK,eAAO;AAAA,IAClB;AACA,QAAI,IAAI;AACN,UAAI,OAAO,OAAO;AAAU,cAAM,CAAC,GAAG,SAAS,iBAAiB,EAAE,CAAC;AACnE,UAAI,OAAO,OAAO,qBAAqB,OAAO,OAAO,YAAY,IAAI,SAAS,KAAK,OAAO,GAAG,iBAAiB,EAAE,EAAE,WAAW,GAAG;AAC9H,cAAM,OAAO,GAAG,cAAc,EAAE;AAAA,MAClC;AAAA,IACF;AACA,QAAI,MAAM,CAAC;AAAK,aAAO;AAEvB,WAAO;AAAA,EACT;AACA,WAAS,SAAS,IAAI,UAAU;AAC9B,UAAM,SAAS,OAAO,OAAO;AAC7B,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,UAAI,OAAO;AACT,cAAM,UAAU,WAAW,QAAQ,QAAQ,EAAE,GAAG,OAAO,cAAc,MAAM,GAAG,CAAC;AAC/E,YAAI,MAAM,YAAY;AAAU,gBAAM,WAAW;AACjD,YAAI,OAAO,OAAO,iBAAiB,OAAO,SAAS;AACjD,gBAAM,UAAU,OAAO,WAAW,QAAQ,QAAQ,EAAE,OAAO,SAAS;AAAA,QACtE;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,SAAS;AAEhB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,QAAI,OAAO,OAAO,MAAM;AACtB,eAAS,QAAQ,KAAK;AACtB,eAAS,QAAQ,KAAK;AACtB;AAAA,IACF;AACA,aAAS,QAAQ,OAAO,eAAe,CAAC,OAAO,OAAO,MAAM;AAC5D,aAAS,QAAQ,OAAO,SAAS,CAAC,OAAO,OAAO,MAAM;AAAA,EACxD;AACA,WAAS,YAAY,GAAG;AACtB,MAAE,eAAe;AACjB,QAAI,OAAO,eAAe,CAAC,OAAO,OAAO,QAAQ,CAAC,OAAO,OAAO;AAAQ;AACxE,WAAO,UAAU;AACjB,SAAK,gBAAgB;AAAA,EACvB;AACA,WAAS,YAAY,GAAG;AACtB,MAAE,eAAe;AACjB,QAAI,OAAO,SAAS,CAAC,OAAO,OAAO,QAAQ,CAAC,OAAO,OAAO;AAAQ;AAClE,WAAO,UAAU;AACjB,SAAK,gBAAgB;AAAA,EACvB;AACA,WAAS,OAAO;AACd,UAAM,SAAS,OAAO,OAAO;AAC7B,WAAO,OAAO,aAAa,0BAA0B,QAAQ,OAAO,eAAe,YAAY,OAAO,OAAO,YAAY;AAAA,MACvH,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV,CAAC;AACD,QAAI,EAAE,OAAO,UAAU,OAAO;AAAS;AACvC,QAAI,SAAS,MAAM,OAAO,MAAM;AAChC,QAAI,SAAS,MAAM,OAAO,MAAM;AAChC,WAAO,OAAO,OAAO,YAAY;AAAA,MAC/B;AAAA,MACA;AAAA,IACF,CAAC;AACD,aAAS,kBAAkB,MAAM;AACjC,aAAS,kBAAkB,MAAM;AACjC,UAAM,aAAa,CAAC,IAAI,QAAQ;AAC9B,UAAI,IAAI;AACN,WAAG,iBAAiB,SAAS,QAAQ,SAAS,cAAc,WAAW;AAAA,MACzE;AACA,UAAI,CAAC,OAAO,WAAW,IAAI;AACzB,WAAG,UAAU,IAAI,GAAG,OAAO,UAAU,MAAM,GAAG,CAAC;AAAA,MACjD;AAAA,IACF;AACA,WAAO,QAAQ,QAAM,WAAW,IAAI,MAAM,CAAC;AAC3C,WAAO,QAAQ,QAAM,WAAW,IAAI,MAAM,CAAC;AAAA,EAC7C;AACA,WAAS,UAAU;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,aAAS,kBAAkB,MAAM;AACjC,aAAS,kBAAkB,MAAM;AACjC,UAAM,gBAAgB,CAAC,IAAI,QAAQ;AACjC,SAAG,oBAAoB,SAAS,QAAQ,SAAS,cAAc,WAAW;AAC1E,SAAG,UAAU,OAAO,GAAG,OAAO,OAAO,WAAW,cAAc,MAAM,GAAG,CAAC;AAAA,IAC1E;AACA,WAAO,QAAQ,QAAM,cAAc,IAAI,MAAM,CAAC;AAC9C,WAAO,QAAQ,QAAM,cAAc,IAAI,MAAM,CAAC;AAAA,EAChD;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,WAAW,YAAY,OAAO;AAE9C,cAAQ;AAAA,IACV,OAAO;AACL,WAAK;AACL,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,+BAA+B,MAAM;AACtC,WAAO;AAAA,EACT,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,YAAQ;AAAA,EACV,CAAC;AACD,KAAG,kBAAkB,MAAM;AACzB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,aAAS,kBAAkB,MAAM;AACjC,aAAS,kBAAkB,MAAM;AACjC,KAAC,GAAG,QAAQ,GAAG,MAAM,EAAE,OAAO,QAAM,CAAC,CAAC,EAAE,EAAE,QAAQ,QAAM,GAAG,UAAU,OAAO,UAAU,WAAW,KAAK,EAAE,OAAO,OAAO,WAAW,SAAS,CAAC;AAAA,EAC7I,CAAC;AACD,KAAG,SAAS,CAAC,IAAI,MAAM;AACrB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,aAAS,kBAAkB,MAAM;AACjC,aAAS,kBAAkB,MAAM;AACjC,UAAM,WAAW,EAAE;AACnB,QAAI,OAAO,OAAO,WAAW,eAAe,CAAC,OAAO,SAAS,QAAQ,KAAK,CAAC,OAAO,SAAS,QAAQ,GAAG;AACpG,UAAI,OAAO,cAAc,OAAO,OAAO,cAAc,OAAO,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,YAAY,OAAO,WAAW,GAAG,SAAS,QAAQ;AAAI;AAC3K,UAAI;AACJ,UAAI,OAAO,QAAQ;AACjB,mBAAW,OAAO,CAAC,EAAE,UAAU,SAAS,OAAO,OAAO,WAAW,WAAW;AAAA,MAC9E,WAAW,OAAO,QAAQ;AACxB,mBAAW,OAAO,CAAC,EAAE,UAAU,SAAS,OAAO,OAAO,WAAW,WAAW;AAAA,MAC9E;AACA,UAAI,aAAa,MAAM;AACrB,aAAK,gBAAgB;AAAA,MACvB,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AACA,OAAC,GAAG,QAAQ,GAAG,MAAM,EAAE,OAAO,QAAM,CAAC,CAAC,EAAE,EAAE,QAAQ,QAAM,GAAG,UAAU,OAAO,OAAO,OAAO,WAAW,WAAW,CAAC;AAAA,IACnH;AAAA,EACF,CAAC;AACD,QAAM,SAAS,MAAM;AACnB,WAAO,GAAG,UAAU,OAAO,GAAG,OAAO,OAAO,WAAW,wBAAwB,MAAM,GAAG,CAAC;AACzF,SAAK;AACL,WAAO;AAAA,EACT;AACA,QAAM,UAAU,MAAM;AACpB,WAAO,GAAG,UAAU,IAAI,GAAG,OAAO,OAAO,WAAW,wBAAwB,MAAM,GAAG,CAAC;AACtF,YAAQ;AAAA,EACV;AACA,SAAO,OAAO,OAAO,YAAY;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;ACzLe,SAAR,kBAAmC,UAAU,IAAI;AACtD,SAAO,IAAI,QAAQ,KAAK,EAAE,QAAQ,gBAAgB,MAAM,EACvD,QAAQ,MAAM,GAAG;AACpB;;;ACAe,SAAR,WAA4B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,MAAM;AACZ,eAAa;AAAA,IACX,YAAY;AAAA,MACV,IAAI;AAAA,MACJ,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,cAAc;AAAA,MACd,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,qBAAqB;AAAA,MACrB,MAAM;AAAA;AAAA,MAEN,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,uBAAuB,YAAU;AAAA,MACjC,qBAAqB,YAAU;AAAA,MAC/B,aAAa,GAAG;AAAA,MAChB,mBAAmB,GAAG;AAAA,MACtB,eAAe,GAAG;AAAA,MAClB,cAAc,GAAG;AAAA,MACjB,YAAY,GAAG;AAAA,MACf,aAAa,GAAG;AAAA,MAChB,sBAAsB,GAAG;AAAA,MACzB,0BAA0B,GAAG;AAAA,MAC7B,gBAAgB,GAAG;AAAA,MACnB,WAAW,GAAG;AAAA,MACd,iBAAiB,GAAG;AAAA,MACpB,eAAe,GAAG;AAAA,MAClB,yBAAyB,GAAG;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,SAAO,aAAa;AAAA,IAClB,IAAI;AAAA,IACJ,SAAS,CAAC;AAAA,EACZ;AACA,MAAI;AACJ,MAAI,qBAAqB;AACzB,QAAM,oBAAoB,QAAM;AAC9B,QAAI,CAAC,MAAM,QAAQ,EAAE;AAAG,WAAK,CAAC,EAAE,EAAE,OAAO,OAAK,CAAC,CAAC,CAAC;AACjD,WAAO;AAAA,EACT;AACA,WAAS,uBAAuB;AAC9B,WAAO,CAAC,OAAO,OAAO,WAAW,MAAM,CAAC,OAAO,WAAW,MAAM,MAAM,QAAQ,OAAO,WAAW,EAAE,KAAK,OAAO,WAAW,GAAG,WAAW;AAAA,EACzI;AACA,WAAS,eAAe,UAAU,UAAU;AAC1C,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO,OAAO;AAClB,QAAI,CAAC;AAAU;AACf,eAAW,SAAS,GAAG,aAAa,SAAS,aAAa,sBAAsB;AAChF,QAAI,UAAU;AACZ,eAAS,UAAU,IAAI,GAAG,qBAAqB,UAAU;AACzD,iBAAW,SAAS,GAAG,aAAa,SAAS,aAAa,sBAAsB;AAChF,UAAI,UAAU;AACZ,iBAAS,UAAU,IAAI,GAAG,qBAAqB,YAAY,UAAU;AAAA,MACvE;AAAA,IACF;AAAA,EACF;AACA,WAAS,cAAc,GAAG;AACxB,UAAM,WAAW,EAAE,OAAO,QAAQ,kBAAkB,OAAO,OAAO,WAAW,WAAW,CAAC;AACzF,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AACA,MAAE,eAAe;AACjB,UAAM,QAAQ,aAAa,QAAQ,IAAI,OAAO,OAAO;AACrD,QAAI,OAAO,OAAO,MAAM;AACtB,UAAI,OAAO,cAAc;AAAO;AAChC,UAAI,QAAQ,OAAO,gBAAgB,QAAQ,OAAO,OAAO,SAAS,OAAO,cAAc;AACrF,eAAO,QAAQ;AAAA,UACb,WAAW,QAAQ,OAAO,eAAe,SAAS;AAAA,UAClD,kBAAkB;AAAA,UAClB,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,aAAO,YAAY,KAAK;AAAA,IAC1B,OAAO;AACL,aAAO,QAAQ,KAAK;AAAA,IACtB;AAAA,EACF;AACA,WAAS,SAAS;AAEhB,UAAM,MAAM,OAAO;AACnB,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,qBAAqB;AAAG;AAC5B,QAAI,KAAK,OAAO,WAAW;AAC3B,SAAK,kBAAkB,EAAE;AAEzB,QAAI;AACJ,UAAM,eAAe,OAAO,WAAW,OAAO,OAAO,QAAQ,UAAU,OAAO,QAAQ,OAAO,SAAS,OAAO,OAAO;AACpH,UAAM,QAAQ,OAAO,OAAO,OAAO,KAAK,KAAK,eAAe,OAAO,OAAO,cAAc,IAAI,OAAO,SAAS;AAC5G,QAAI,OAAO,OAAO,MAAM;AACtB,gBAAU,OAAO,OAAO,iBAAiB,IAAI,KAAK,MAAM,OAAO,YAAY,OAAO,OAAO,cAAc,IAAI,OAAO;AAAA,IACpH,WAAW,OAAO,OAAO,cAAc,aAAa;AAClD,gBAAU,OAAO;AAAA,IACnB,OAAO;AACL,gBAAU,OAAO,eAAe;AAAA,IAClC;AAEA,QAAI,OAAO,SAAS,aAAa,OAAO,WAAW,WAAW,OAAO,WAAW,QAAQ,SAAS,GAAG;AAClG,YAAM,UAAU,OAAO,WAAW;AAClC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,OAAO,gBAAgB;AACzB,qBAAa,iBAAiB,QAAQ,CAAC,GAAG,OAAO,aAAa,IAAI,UAAU,UAAU,IAAI;AAC1F,WAAG,QAAQ,WAAS;AAClB,gBAAM,MAAM,OAAO,aAAa,IAAI,UAAU,QAAQ,IAAI,GAAG,cAAc,OAAO,qBAAqB;AAAA,QACzG,CAAC;AACD,YAAI,OAAO,qBAAqB,KAAK,OAAO,kBAAkB,QAAW;AACvE,gCAAsB,WAAW,OAAO,iBAAiB;AACzD,cAAI,qBAAqB,OAAO,qBAAqB,GAAG;AACtD,iCAAqB,OAAO,qBAAqB;AAAA,UACnD,WAAW,qBAAqB,GAAG;AACjC,iCAAqB;AAAA,UACvB;AAAA,QACF;AACA,qBAAa,KAAK,IAAI,UAAU,oBAAoB,CAAC;AACrD,oBAAY,cAAc,KAAK,IAAI,QAAQ,QAAQ,OAAO,kBAAkB,IAAI;AAChF,oBAAY,YAAY,cAAc;AAAA,MACxC;AACA,cAAQ,QAAQ,cAAY;AAC1B,cAAM,kBAAkB,CAAC,GAAG,CAAC,IAAI,SAAS,cAAc,SAAS,cAAc,OAAO,EAAE,IAAI,YAAU,GAAG,OAAO,oBAAoB,QAAQ,CAAC,EAAE,IAAI,OAAK,OAAO,MAAM,YAAY,EAAE,SAAS,GAAG,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC,EAAE,KAAK;AAC1N,iBAAS,UAAU,OAAO,GAAG,eAAe;AAAA,MAC9C,CAAC;AACD,UAAI,GAAG,SAAS,GAAG;AACjB,gBAAQ,QAAQ,YAAU;AACxB,gBAAM,cAAc,aAAa,MAAM;AACvC,cAAI,gBAAgB,SAAS;AAC3B,mBAAO,UAAU,IAAI,GAAG,OAAO,kBAAkB,MAAM,GAAG,CAAC;AAAA,UAC7D;AACA,cAAI,OAAO,gBAAgB;AACzB,gBAAI,eAAe,cAAc,eAAe,WAAW;AACzD,qBAAO,UAAU,IAAI,GAAG,GAAG,OAAO,yBAAyB,MAAM,GAAG,CAAC;AAAA,YACvE;AACA,gBAAI,gBAAgB,YAAY;AAC9B,6BAAe,QAAQ,MAAM;AAAA,YAC/B;AACA,gBAAI,gBAAgB,WAAW;AAC7B,6BAAe,QAAQ,MAAM;AAAA,YAC/B;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,cAAM,SAAS,QAAQ,OAAO;AAC9B,YAAI,QAAQ;AACV,iBAAO,UAAU,IAAI,GAAG,OAAO,kBAAkB,MAAM,GAAG,CAAC;AAAA,QAC7D;AACA,YAAI,OAAO,gBAAgB;AACzB,gBAAM,uBAAuB,QAAQ,UAAU;AAC/C,gBAAM,sBAAsB,QAAQ,SAAS;AAC7C,mBAAS,IAAI,YAAY,KAAK,WAAW,KAAK,GAAG;AAC/C,gBAAI,QAAQ,CAAC,GAAG;AACd,sBAAQ,CAAC,EAAE,UAAU,IAAI,GAAG,GAAG,OAAO,yBAAyB,MAAM,GAAG,CAAC;AAAA,YAC3E;AAAA,UACF;AACA,yBAAe,sBAAsB,MAAM;AAC3C,yBAAe,qBAAqB,MAAM;AAAA,QAC5C;AAAA,MACF;AACA,UAAI,OAAO,gBAAgB;AACzB,cAAM,uBAAuB,KAAK,IAAI,QAAQ,QAAQ,OAAO,qBAAqB,CAAC;AACnF,cAAM,iBAAiB,aAAa,uBAAuB,cAAc,IAAI,WAAW;AACxF,cAAM,aAAa,MAAM,UAAU;AACnC,gBAAQ,QAAQ,YAAU;AACxB,iBAAO,MAAM,OAAO,aAAa,IAAI,aAAa,KAAK,IAAI,GAAG;AAAA,QAChE,CAAC;AAAA,MACH;AAAA,IACF;AACA,OAAG,QAAQ,CAAC,OAAO,eAAe;AAChC,UAAI,OAAO,SAAS,YAAY;AAC9B,cAAM,iBAAiB,kBAAkB,OAAO,YAAY,CAAC,EAAE,QAAQ,gBAAc;AACnF,qBAAW,cAAc,OAAO,sBAAsB,UAAU,CAAC;AAAA,QACnE,CAAC;AACD,cAAM,iBAAiB,kBAAkB,OAAO,UAAU,CAAC,EAAE,QAAQ,aAAW;AAC9E,kBAAQ,cAAc,OAAO,oBAAoB,KAAK;AAAA,QACxD,CAAC;AAAA,MACH;AACA,UAAI,OAAO,SAAS,eAAe;AACjC,YAAI;AACJ,YAAI,OAAO,qBAAqB;AAC9B,iCAAuB,OAAO,aAAa,IAAI,aAAa;AAAA,QAC9D,OAAO;AACL,iCAAuB,OAAO,aAAa,IAAI,eAAe;AAAA,QAChE;AACA,cAAM,SAAS,UAAU,KAAK;AAC9B,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,yBAAyB,cAAc;AACzC,mBAAS;AAAA,QACX,OAAO;AACL,mBAAS;AAAA,QACX;AACA,cAAM,iBAAiB,kBAAkB,OAAO,oBAAoB,CAAC,EAAE,QAAQ,gBAAc;AAC3F,qBAAW,MAAM,YAAY,6BAA6B,kBAAkB;AAC5E,qBAAW,MAAM,qBAAqB,GAAG,OAAO,OAAO;AAAA,QACzD,CAAC;AAAA,MACH;AACA,UAAI,OAAO,SAAS,YAAY,OAAO,cAAc;AACnD,cAAM,YAAY,OAAO,aAAa,QAAQ,UAAU,GAAG,KAAK;AAChE,YAAI,eAAe;AAAG,eAAK,oBAAoB,KAAK;AAAA,MACtD,OAAO;AACL,YAAI,eAAe;AAAG,eAAK,oBAAoB,KAAK;AACpD,aAAK,oBAAoB,KAAK;AAAA,MAChC;AACA,UAAI,OAAO,OAAO,iBAAiB,OAAO,SAAS;AACjD,cAAM,UAAU,OAAO,WAAW,QAAQ,QAAQ,EAAE,OAAO,SAAS;AAAA,MACtE;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,SAAS;AAEhB,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,qBAAqB;AAAG;AAC5B,UAAM,eAAe,OAAO,WAAW,OAAO,OAAO,QAAQ,UAAU,OAAO,QAAQ,OAAO,SAAS,OAAO,OAAO;AACpH,QAAI,KAAK,OAAO,WAAW;AAC3B,SAAK,kBAAkB,EAAE;AACzB,QAAI,iBAAiB;AACrB,QAAI,OAAO,SAAS,WAAW;AAC7B,UAAI,kBAAkB,OAAO,OAAO,OAAO,KAAK,KAAK,eAAe,OAAO,OAAO,cAAc,IAAI,OAAO,SAAS;AACpH,UAAI,OAAO,OAAO,YAAY,OAAO,OAAO,SAAS,WAAW,kBAAkB,cAAc;AAC9F,0BAAkB;AAAA,MACpB;AACA,eAAS,IAAI,GAAG,IAAI,iBAAiB,KAAK,GAAG;AAC3C,YAAI,OAAO,cAAc;AACvB,4BAAkB,OAAO,aAAa,KAAK,QAAQ,GAAG,OAAO,WAAW;AAAA,QAC1E,OAAO;AACL,4BAAkB,IAAI,OAAO,wBAAwB,OAAO,kBAAkB,OAAO;AAAA,QACvF;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,SAAS,YAAY;AAC9B,UAAI,OAAO,gBAAgB;AACzB,yBAAiB,OAAO,eAAe,KAAK,QAAQ,OAAO,cAAc,OAAO,UAAU;AAAA,MAC5F,OAAO;AACL,yBAAiB,gBAAgB,OAAO,wCAAkD,OAAO;AAAA,MACnG;AAAA,IACF;AACA,QAAI,OAAO,SAAS,eAAe;AACjC,UAAI,OAAO,mBAAmB;AAC5B,yBAAiB,OAAO,kBAAkB,KAAK,QAAQ,OAAO,oBAAoB;AAAA,MACpF,OAAO;AACL,yBAAiB,gBAAgB,OAAO;AAAA,MAC1C;AAAA,IACF;AACA,WAAO,WAAW,UAAU,CAAC;AAC7B,OAAG,QAAQ,WAAS;AAClB,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,YAAY,kBAAkB;AAAA,MACtC;AACA,UAAI,OAAO,SAAS,WAAW;AAC7B,eAAO,WAAW,QAAQ,KAAK,GAAG,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,CAAC,CAAC;AAAA,MACjG;AAAA,IACF,CAAC;AACD,QAAI,OAAO,SAAS,UAAU;AAC5B,WAAK,oBAAoB,GAAG,CAAC,CAAC;AAAA,IAChC;AAAA,EACF;AACA,WAAS,OAAO;AACd,WAAO,OAAO,aAAa,0BAA0B,QAAQ,OAAO,eAAe,YAAY,OAAO,OAAO,YAAY;AAAA,MACvH,IAAI;AAAA,IACN,CAAC;AACD,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,CAAC,OAAO;AAAI;AAChB,QAAI;AACJ,QAAI,OAAO,OAAO,OAAO,YAAY,OAAO,WAAW;AACrD,WAAK,OAAO,GAAG,WAAW,cAAc,OAAO,EAAE;AAAA,IACnD;AACA,QAAI,CAAC,MAAM,OAAO,OAAO,OAAO,UAAU;AACxC,WAAK,CAAC,GAAG,SAAS,iBAAiB,OAAO,EAAE,CAAC;AAAA,IAC/C;AACA,QAAI,CAAC,IAAI;AACP,WAAK,OAAO;AAAA,IACd;AACA,QAAI,CAAC,MAAM,GAAG,WAAW;AAAG;AAC5B,QAAI,OAAO,OAAO,qBAAqB,OAAO,OAAO,OAAO,YAAY,MAAM,QAAQ,EAAE,KAAK,GAAG,SAAS,GAAG;AAC1G,WAAK,CAAC,GAAG,OAAO,GAAG,iBAAiB,OAAO,EAAE,CAAC;AAE9C,UAAI,GAAG,SAAS,GAAG;AACjB,aAAK,GAAG,OAAO,WAAS;AACtB,cAAI,eAAe,OAAO,SAAS,EAAE,CAAC,MAAM,OAAO;AAAI,mBAAO;AAC9D,iBAAO;AAAA,QACT,CAAC,EAAE,CAAC;AAAA,MACN;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,EAAE,KAAK,GAAG,WAAW;AAAG,WAAK,GAAG,CAAC;AACnD,WAAO,OAAO,OAAO,YAAY;AAAA,MAC/B;AAAA,IACF,CAAC;AACD,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,UAAI,OAAO,SAAS,aAAa,OAAO,WAAW;AACjD,cAAM,UAAU,IAAI,OAAO,cAAc;AAAA,MAC3C;AACA,YAAM,UAAU,IAAI,OAAO,gBAAgB,OAAO,IAAI;AACtD,YAAM,UAAU,IAAI,OAAO,aAAa,IAAI,OAAO,kBAAkB,OAAO,aAAa;AACzF,UAAI,OAAO,SAAS,aAAa,OAAO,gBAAgB;AACtD,cAAM,UAAU,IAAI,GAAG,OAAO,gBAAgB,OAAO,cAAc;AACnE,6BAAqB;AACrB,YAAI,OAAO,qBAAqB,GAAG;AACjC,iBAAO,qBAAqB;AAAA,QAC9B;AAAA,MACF;AACA,UAAI,OAAO,SAAS,iBAAiB,OAAO,qBAAqB;AAC/D,cAAM,UAAU,IAAI,OAAO,wBAAwB;AAAA,MACrD;AACA,UAAI,OAAO,WAAW;AACpB,cAAM,iBAAiB,SAAS,aAAa;AAAA,MAC/C;AACA,UAAI,CAAC,OAAO,SAAS;AACnB,cAAM,UAAU,IAAI,OAAO,SAAS;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,UAAU;AACjB,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,qBAAqB;AAAG;AAC5B,QAAI,KAAK,OAAO,WAAW;AAC3B,QAAI,IAAI;AACN,WAAK,kBAAkB,EAAE;AACzB,SAAG,QAAQ,WAAS;AAClB,cAAM,UAAU,OAAO,OAAO,WAAW;AACzC,cAAM,UAAU,OAAO,OAAO,gBAAgB,OAAO,IAAI;AACzD,cAAM,UAAU,OAAO,OAAO,aAAa,IAAI,OAAO,kBAAkB,OAAO,aAAa;AAC5F,YAAI,OAAO,WAAW;AACpB,gBAAM,oBAAoB,SAAS,aAAa;AAAA,QAClD;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,OAAO,WAAW;AAAS,aAAO,WAAW,QAAQ,QAAQ,WAAS,MAAM,UAAU,OAAO,GAAG,OAAO,kBAAkB,MAAM,GAAG,CAAC,CAAC;AAAA,EAC1I;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,WAAW,YAAY,OAAO;AAE9C,cAAQ;AAAA,IACV,OAAO;AACL,WAAK;AACL,aAAO;AACP,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,qBAAqB,MAAM;AAC5B,QAAI,OAAO,OAAO,cAAc,aAAa;AAC3C,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,mBAAmB,MAAM;AAC1B,WAAO;AAAA,EACT,CAAC;AACD,KAAG,wBAAwB,MAAM;AAC/B,WAAO;AACP,WAAO;AAAA,EACT,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,YAAQ;AAAA,EACV,CAAC;AACD,KAAG,kBAAkB,MAAM;AACzB,QAAI;AAAA,MACF;AAAA,IACF,IAAI,OAAO;AACX,QAAI,IAAI;AACN,WAAK,kBAAkB,EAAE;AACzB,SAAG,QAAQ,WAAS,MAAM,UAAU,OAAO,UAAU,WAAW,KAAK,EAAE,OAAO,OAAO,WAAW,SAAS,CAAC;AAAA,IAC5G;AAAA,EACF,CAAC;AACD,KAAG,eAAe,MAAM;AACtB,WAAO;AAAA,EACT,CAAC;AACD,KAAG,SAAS,CAAC,IAAI,MAAM;AACrB,UAAM,WAAW,EAAE;AACnB,QAAI;AAAA,MACF;AAAA,IACF,IAAI,OAAO;AACX,QAAI,CAAC,MAAM,QAAQ,EAAE;AAAG,WAAK,CAAC,EAAE,EAAE,OAAO,aAAW,CAAC,CAAC,OAAO;AAC7D,QAAI,OAAO,OAAO,WAAW,MAAM,OAAO,OAAO,WAAW,eAAe,MAAM,GAAG,SAAS,KAAK,CAAC,SAAS,UAAU,SAAS,OAAO,OAAO,WAAW,WAAW,GAAG;AACpK,UAAI,OAAO,eAAe,OAAO,WAAW,UAAU,aAAa,OAAO,WAAW,UAAU,OAAO,WAAW,UAAU,aAAa,OAAO,WAAW;AAAS;AACnK,YAAM,WAAW,GAAG,CAAC,EAAE,UAAU,SAAS,OAAO,OAAO,WAAW,WAAW;AAC9E,UAAI,aAAa,MAAM;AACrB,aAAK,gBAAgB;AAAA,MACvB,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AACA,SAAG,QAAQ,WAAS,MAAM,UAAU,OAAO,OAAO,OAAO,WAAW,WAAW,CAAC;AAAA,IAClF;AAAA,EACF,CAAC;AACD,QAAM,SAAS,MAAM;AACnB,WAAO,GAAG,UAAU,OAAO,OAAO,OAAO,WAAW,uBAAuB;AAC3E,QAAI;AAAA,MACF;AAAA,IACF,IAAI,OAAO;AACX,QAAI,IAAI;AACN,WAAK,kBAAkB,EAAE;AACzB,SAAG,QAAQ,WAAS,MAAM,UAAU,OAAO,OAAO,OAAO,WAAW,uBAAuB,CAAC;AAAA,IAC9F;AACA,SAAK;AACL,WAAO;AACP,WAAO;AAAA,EACT;AACA,QAAM,UAAU,MAAM;AACpB,WAAO,GAAG,UAAU,IAAI,OAAO,OAAO,WAAW,uBAAuB;AACxE,QAAI;AAAA,MACF;AAAA,IACF,IAAI,OAAO;AACX,QAAI,IAAI;AACN,WAAK,kBAAkB,EAAE;AACzB,SAAG,QAAQ,WAAS,MAAM,UAAU,IAAI,OAAO,OAAO,WAAW,uBAAuB,CAAC;AAAA,IAC3F;AACA,YAAQ;AAAA,EACV;AACA,SAAO,OAAO,OAAO,YAAY;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;ACxae,SAAR,UAA2B;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAMC,YAAW,YAAY;AAC7B,MAAI,YAAY;AAChB,MAAIC,WAAU;AACd,MAAI,cAAc;AAClB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,eAAa;AAAA,IACX,WAAW;AAAA,MACT,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,MAAM;AAAA,MACN,WAAW;AAAA,MACX,eAAe;AAAA,MACf,WAAW;AAAA,MACX,WAAW;AAAA,MACX,wBAAwB;AAAA,MACxB,iBAAiB;AAAA,MACjB,eAAe;AAAA,IACjB;AAAA,EACF,CAAC;AACD,SAAO,YAAY;AAAA,IACjB,IAAI;AAAA,IACJ,QAAQ;AAAA,EACV;AACA,WAASC,gBAAe;AACtB,QAAI,CAAC,OAAO,OAAO,UAAU,MAAM,CAAC,OAAO,UAAU;AAAI;AACzD,UAAM;AAAA,MACJ;AAAA,MACA,cAAc;AAAA,IAChB,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,WAAW,OAAO,OAAO,OAAO,OAAO,eAAe,OAAO;AACnE,QAAI,UAAU;AACd,QAAI,UAAU,YAAY,YAAY;AACtC,QAAI,KAAK;AACP,eAAS,CAAC;AACV,UAAI,SAAS,GAAG;AACd,kBAAU,WAAW;AACrB,iBAAS;AAAA,MACX,WAAW,CAAC,SAAS,WAAW,WAAW;AACzC,kBAAU,YAAY;AAAA,MACxB;AAAA,IACF,WAAW,SAAS,GAAG;AACrB,gBAAU,WAAW;AACrB,eAAS;AAAA,IACX,WAAW,SAAS,WAAW,WAAW;AACxC,gBAAU,YAAY;AAAA,IACxB;AACA,QAAI,OAAO,aAAa,GAAG;AACzB,aAAO,MAAM,YAAY,eAAe;AACxC,aAAO,MAAM,QAAQ,GAAG;AAAA,IAC1B,OAAO;AACL,aAAO,MAAM,YAAY,oBAAoB;AAC7C,aAAO,MAAM,SAAS,GAAG;AAAA,IAC3B;AACA,QAAI,OAAO,MAAM;AACf,mBAAaD,QAAO;AACpB,SAAG,MAAM,UAAU;AACnB,MAAAA,WAAU,WAAW,MAAM;AACzB,WAAG,MAAM,UAAU;AACnB,WAAG,MAAM,qBAAqB;AAAA,MAChC,GAAG,GAAI;AAAA,IACT;AAAA,EACF;AACA,WAASE,eAAc,UAAU;AAC/B,QAAI,CAAC,OAAO,OAAO,UAAU,MAAM,CAAC,OAAO,UAAU;AAAI;AACzD,WAAO,UAAU,OAAO,MAAM,qBAAqB,GAAG;AAAA,EACxD;AACA,WAASC,cAAa;AACpB,QAAI,CAAC,OAAO,OAAO,UAAU,MAAM,CAAC,OAAO,UAAU;AAAI;AACzD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,MAAM,QAAQ;AACrB,WAAO,MAAM,SAAS;AACtB,gBAAY,OAAO,aAAa,IAAI,GAAG,cAAc,GAAG;AACxD,cAAU,OAAO,QAAQ,OAAO,cAAc,OAAO,OAAO,sBAAsB,OAAO,OAAO,iBAAiB,OAAO,SAAS,CAAC,IAAI;AACtI,QAAI,OAAO,OAAO,UAAU,aAAa,QAAQ;AAC/C,iBAAW,YAAY;AAAA,IACzB,OAAO;AACL,iBAAW,SAAS,OAAO,OAAO,UAAU,UAAU,EAAE;AAAA,IAC1D;AACA,QAAI,OAAO,aAAa,GAAG;AACzB,aAAO,MAAM,QAAQ,GAAG;AAAA,IAC1B,OAAO;AACL,aAAO,MAAM,SAAS,GAAG;AAAA,IAC3B;AACA,QAAI,WAAW,GAAG;AAChB,SAAG,MAAM,UAAU;AAAA,IACrB,OAAO;AACL,SAAG,MAAM,UAAU;AAAA,IACrB;AACA,QAAI,OAAO,OAAO,UAAU,MAAM;AAChC,SAAG,MAAM,UAAU;AAAA,IACrB;AACA,QAAI,OAAO,OAAO,iBAAiB,OAAO,SAAS;AACjD,gBAAU,GAAG,UAAU,OAAO,WAAW,QAAQ,QAAQ,EAAE,OAAO,OAAO,UAAU,SAAS;AAAA,IAC9F;AAAA,EACF;AACA,WAAS,mBAAmB,GAAG;AAC7B,WAAO,OAAO,aAAa,IAAI,EAAE,UAAU,EAAE;AAAA,EAC/C;AACA,WAAS,gBAAgB,GAAG;AAC1B,UAAM;AAAA,MACJ;AAAA,MACA,cAAc;AAAA,IAChB,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI;AACJ,qBAAiB,mBAAmB,CAAC,IAAI,cAAc,EAAE,EAAE,OAAO,aAAa,IAAI,SAAS,KAAK,KAAK,iBAAiB,OAAO,eAAe,WAAW,OAAO,YAAY;AAC3K,oBAAgB,KAAK,IAAI,KAAK,IAAI,eAAe,CAAC,GAAG,CAAC;AACtD,QAAI,KAAK;AACP,sBAAgB,IAAI;AAAA,IACtB;AACA,UAAM,WAAW,OAAO,aAAa,KAAK,OAAO,aAAa,IAAI,OAAO,aAAa,KAAK;AAC3F,WAAO,eAAe,QAAQ;AAC9B,WAAO,aAAa,QAAQ;AAC5B,WAAO,kBAAkB;AACzB,WAAO,oBAAoB;AAAA,EAC7B;AACA,WAAS,YAAY,GAAG;AACtB,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,gBAAY;AACZ,mBAAe,EAAE,WAAW,SAAS,mBAAmB,CAAC,IAAI,EAAE,OAAO,sBAAsB,EAAE,OAAO,aAAa,IAAI,SAAS,KAAK,IAAI;AACxI,MAAE,eAAe;AACjB,MAAE,gBAAgB;AAClB,cAAU,MAAM,qBAAqB;AACrC,WAAO,MAAM,qBAAqB;AAClC,oBAAgB,CAAC;AACjB,iBAAa,WAAW;AACxB,OAAG,MAAM,qBAAqB;AAC9B,QAAI,OAAO,MAAM;AACf,SAAG,MAAM,UAAU;AAAA,IACrB;AACA,QAAI,OAAO,OAAO,SAAS;AACzB,aAAO,UAAU,MAAM,kBAAkB,IAAI;AAAA,IAC/C;AACA,SAAK,sBAAsB,CAAC;AAAA,EAC9B;AACA,WAAS,WAAW,GAAG;AACrB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC;AAAW;AAChB,QAAI,EAAE;AAAgB,QAAE,eAAe;AAAA;AAAO,QAAE,cAAc;AAC9D,oBAAgB,CAAC;AACjB,cAAU,MAAM,qBAAqB;AACrC,OAAG,MAAM,qBAAqB;AAC9B,WAAO,MAAM,qBAAqB;AAClC,SAAK,qBAAqB,CAAC;AAAA,EAC7B;AACA,WAAS,UAAU,GAAG;AACpB,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,CAAC;AAAW;AAChB,gBAAY;AACZ,QAAI,OAAO,OAAO,SAAS;AACzB,aAAO,UAAU,MAAM,kBAAkB,IAAI;AAC7C,gBAAU,MAAM,qBAAqB;AAAA,IACvC;AACA,QAAI,OAAO,MAAM;AACf,mBAAa,WAAW;AACxB,oBAAc,SAAS,MAAM;AAC3B,WAAG,MAAM,UAAU;AACnB,WAAG,MAAM,qBAAqB;AAAA,MAChC,GAAG,GAAI;AAAA,IACT;AACA,SAAK,oBAAoB,CAAC;AAC1B,QAAI,OAAO,eAAe;AACxB,aAAO,eAAe;AAAA,IACxB;AAAA,EACF;AACA,WAASC,QAAO,QAAQ;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,UAAU;AACrB,QAAI,CAAC;AAAI;AACT,UAAM,SAAS;AACf,UAAM,iBAAiB,OAAO,mBAAmB;AAAA,MAC/C,SAAS;AAAA,MACT,SAAS;AAAA,IACX,IAAI;AACJ,UAAM,kBAAkB,OAAO,mBAAmB;AAAA,MAChD,SAAS;AAAA,MACT,SAAS;AAAA,IACX,IAAI;AACJ,QAAI,CAAC;AAAQ;AACb,UAAM,cAAc,WAAW,OAAO,qBAAqB;AAC3D,WAAO,WAAW,EAAE,eAAe,aAAa,cAAc;AAC9D,IAAAL,UAAS,WAAW,EAAE,eAAe,YAAY,cAAc;AAC/D,IAAAA,UAAS,WAAW,EAAE,aAAa,WAAW,eAAe;AAAA,EAC/D;AACA,WAAS,kBAAkB;AACzB,QAAI,CAAC,OAAO,OAAO,UAAU,MAAM,CAAC,OAAO,UAAU;AAAI;AACzD,IAAAK,QAAO,IAAI;AAAA,EACb;AACA,WAAS,mBAAmB;AAC1B,QAAI,CAAC,OAAO,OAAO,UAAU,MAAM,CAAC,OAAO,UAAU;AAAI;AACzD,IAAAA,QAAO,KAAK;AAAA,EACd;AACA,WAAS,OAAO;AACd,UAAM;AAAA,MACJ;AAAA,MACA,IAAI;AAAA,IACN,IAAI;AACJ,WAAO,OAAO,YAAY,0BAA0B,QAAQ,OAAO,eAAe,WAAW,OAAO,OAAO,WAAW;AAAA,MACpH,IAAI;AAAA,IACN,CAAC;AACD,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,CAAC,OAAO;AAAI;AAChB,QAAI;AACJ,QAAI,OAAO,OAAO,OAAO,YAAY,OAAO,WAAW;AACrD,WAAK,OAAO,GAAG,WAAW,cAAc,OAAO,EAAE;AAAA,IACnD;AACA,QAAI,CAAC,MAAM,OAAO,OAAO,OAAO,UAAU;AACxC,WAAKL,UAAS,iBAAiB,OAAO,EAAE;AAAA,IAC1C,WAAW,CAAC,IAAI;AACd,WAAK,OAAO;AAAA,IACd;AACA,QAAI,OAAO,OAAO,qBAAqB,OAAO,OAAO,OAAO,YAAY,GAAG,SAAS,KAAK,SAAS,iBAAiB,OAAO,EAAE,EAAE,WAAW,GAAG;AAC1I,WAAK,SAAS,cAAc,OAAO,EAAE;AAAA,IACvC;AACA,QAAI,GAAG,SAAS;AAAG,WAAK,GAAG,CAAC;AAC5B,OAAG,UAAU,IAAI,OAAO,aAAa,IAAI,OAAO,kBAAkB,OAAO,aAAa;AACtF,QAAI;AACJ,QAAI,IAAI;AACN,eAAS,GAAG,cAAc,IAAI,OAAO,OAAO,UAAU,WAAW;AACjE,UAAI,CAAC,QAAQ;AACX,iBAAS,cAAc,OAAO,OAAO,OAAO,UAAU,SAAS;AAC/D,WAAG,OAAO,MAAM;AAAA,MAClB;AAAA,IACF;AACA,WAAO,OAAO,WAAW;AAAA,MACvB;AAAA,MACA;AAAA,IACF,CAAC;AACD,QAAI,OAAO,WAAW;AACpB,sBAAgB;AAAA,IAClB;AACA,QAAI,IAAI;AACN,SAAG,UAAU,OAAO,UAAU,WAAW,KAAK,EAAE,OAAO,OAAO,UAAU,SAAS;AAAA,IACnF;AAAA,EACF;AACA,WAAS,UAAU;AACjB,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,KAAK,OAAO,UAAU;AAC5B,QAAI,IAAI;AACN,SAAG,UAAU,OAAO,OAAO,aAAa,IAAI,OAAO,kBAAkB,OAAO,aAAa;AAAA,IAC3F;AACA,qBAAiB;AAAA,EACnB;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,UAAU,YAAY,OAAO;AAE7C,cAAQ;AAAA,IACV,OAAO;AACL,WAAK;AACL,MAAAI,YAAW;AACX,MAAAF,cAAa;AAAA,IACf;AAAA,EACF,CAAC;AACD,KAAG,4CAA4C,MAAM;AACnD,IAAAE,YAAW;AAAA,EACb,CAAC;AACD,KAAG,gBAAgB,MAAM;AACvB,IAAAF,cAAa;AAAA,EACf,CAAC;AACD,KAAG,iBAAiB,CAAC,IAAI,aAAa;AACpC,IAAAC,eAAc,QAAQ;AAAA,EACxB,CAAC;AACD,KAAG,kBAAkB,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO;AACX,QAAI,IAAI;AACN,SAAG,UAAU,OAAO,UAAU,WAAW,KAAK,EAAE,OAAO,OAAO,UAAU,SAAS;AAAA,IACnF;AAAA,EACF,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,YAAQ;AAAA,EACV,CAAC;AACD,QAAM,SAAS,MAAM;AACnB,WAAO,GAAG,UAAU,OAAO,OAAO,OAAO,UAAU,sBAAsB;AACzE,QAAI,OAAO,UAAU,IAAI;AACvB,aAAO,UAAU,GAAG,UAAU,OAAO,OAAO,OAAO,UAAU,sBAAsB;AAAA,IACrF;AACA,SAAK;AACL,IAAAC,YAAW;AACX,IAAAF,cAAa;AAAA,EACf;AACA,QAAM,UAAU,MAAM;AACpB,WAAO,GAAG,UAAU,IAAI,OAAO,OAAO,UAAU,sBAAsB;AACtE,QAAI,OAAO,UAAU,IAAI;AACvB,aAAO,UAAU,GAAG,UAAU,IAAI,OAAO,OAAO,UAAU,sBAAsB;AAAA,IAClF;AACA,YAAQ;AAAA,EACV;AACA,SAAO,OAAO,OAAO,WAAW;AAAA,IAC9B;AAAA,IACA;AAAA,IACA,YAAAE;AAAA,IACA,cAAAF;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;AC1Ve,SAAR,SAA0B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,UAAU;AAAA,MACR,SAAS;AAAA,IACX;AAAA,EACF,CAAC;AACD,QAAM,eAAe,CAAC,IAAI,aAAa;AACrC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,YAAY,MAAM,KAAK;AAC7B,UAAM,IAAI,GAAG,aAAa,sBAAsB,KAAK;AACrD,QAAI,IAAI,GAAG,aAAa,wBAAwB;AAChD,QAAI,IAAI,GAAG,aAAa,wBAAwB;AAChD,UAAM,QAAQ,GAAG,aAAa,4BAA4B;AAC1D,UAAM,UAAU,GAAG,aAAa,8BAA8B;AAC9D,UAAM,SAAS,GAAG,aAAa,6BAA6B;AAC5D,QAAI,KAAK,GAAG;AACV,UAAI,KAAK;AACT,UAAI,KAAK;AAAA,IACX,WAAW,OAAO,aAAa,GAAG;AAChC,UAAI;AACJ,UAAI;AAAA,IACN,OAAO;AACL,UAAI;AACJ,UAAI;AAAA,IACN;AACA,QAAI,EAAE,QAAQ,GAAG,KAAK,GAAG;AACvB,UAAI,GAAG,SAAS,GAAG,EAAE,IAAI,WAAW;AAAA,IACtC,OAAO;AACL,UAAI,GAAG,IAAI,WAAW;AAAA,IACxB;AACA,QAAI,EAAE,QAAQ,GAAG,KAAK,GAAG;AACvB,UAAI,GAAG,SAAS,GAAG,EAAE,IAAI;AAAA,IAC3B,OAAO;AACL,UAAI,GAAG,IAAI;AAAA,IACb;AACA,QAAI,OAAO,YAAY,eAAe,YAAY,MAAM;AACtD,YAAM,iBAAiB,WAAW,UAAU,MAAM,IAAI,KAAK,IAAI,QAAQ;AACvE,SAAG,MAAM,UAAU;AAAA,IACrB;AACA,QAAI,YAAY,eAAe,MAAM;AACrC,QAAI,OAAO,UAAU,eAAe,UAAU,MAAM;AAClD,YAAM,eAAe,SAAS,QAAQ,MAAM,IAAI,KAAK,IAAI,QAAQ;AACjE,mBAAa,UAAU;AAAA,IACzB;AACA,QAAI,UAAU,OAAO,WAAW,eAAe,WAAW,MAAM;AAC9D,YAAM,gBAAgB,SAAS,WAAW;AAC1C,mBAAa,WAAW;AAAA,IAC1B;AACA,OAAG,MAAM,YAAY;AAAA,EACvB;AACA,QAAMI,gBAAe,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,oBAAgB,IAAI,0IAA0I,EAAE,QAAQ,WAAS;AAC/K,mBAAa,OAAO,QAAQ;AAAA,IAC9B,CAAC;AACD,WAAO,QAAQ,CAAC,SAAS,eAAe;AACtC,UAAI,gBAAgB,QAAQ;AAC5B,UAAI,OAAO,OAAO,iBAAiB,KAAK,OAAO,OAAO,kBAAkB,QAAQ;AAC9E,yBAAiB,KAAK,KAAK,aAAa,CAAC,IAAI,YAAY,SAAS,SAAS;AAAA,MAC7E;AACA,sBAAgB,KAAK,IAAI,KAAK,IAAI,eAAe,EAAE,GAAG,CAAC;AACvD,cAAQ,iBAAiB,yKAAyK,EAAE,QAAQ,WAAS;AACnN,qBAAa,OAAO,aAAa;AAAA,MACnC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,QAAMC,iBAAgB,CAAC,WAAW,OAAO,OAAO,UAAU;AACxD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,OAAG,iBAAiB,0IAA0I,EAAE,QAAQ,gBAAc;AACpL,UAAI,mBAAmB,SAAS,WAAW,aAAa,+BAA+B,GAAG,EAAE,KAAK;AACjG,UAAI,aAAa;AAAG,2BAAmB;AACvC,iBAAW,MAAM,qBAAqB,GAAG;AAAA,IAC3C,CAAC;AAAA,EACH;AACA,KAAG,cAAc,MAAM;AACrB,QAAI,CAAC,OAAO,OAAO,SAAS;AAAS;AACrC,WAAO,OAAO,sBAAsB;AACpC,WAAO,eAAe,sBAAsB;AAAA,EAC9C,CAAC;AACD,KAAG,QAAQ,MAAM;AACf,QAAI,CAAC,OAAO,OAAO,SAAS;AAAS;AACrC,IAAAD,cAAa;AAAA,EACf,CAAC;AACD,KAAG,gBAAgB,MAAM;AACvB,QAAI,CAAC,OAAO,OAAO,SAAS;AAAS;AACrC,IAAAA,cAAa;AAAA,EACf,CAAC;AACD,KAAG,iBAAiB,CAAC,SAAS,aAAa;AACzC,QAAI,CAAC,OAAO,OAAO,SAAS;AAAS;AACrC,IAAAC,eAAc,QAAQ;AAAA,EACxB,CAAC;AACH;;;ACvGe,SAAR,KAAsB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAMC,UAAS,UAAU;AACzB,eAAa;AAAA,IACX,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,IACpB;AAAA,EACF,CAAC;AACD,SAAO,OAAO;AAAA,IACZ,SAAS;AAAA,EACX;AACA,MAAI,eAAe;AACnB,MAAI,YAAY;AAChB,MAAI;AACJ,MAAI;AACJ,QAAM,UAAU,CAAC;AACjB,QAAM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,SAAS;AAAA,IACT,aAAa;AAAA,IACb,UAAU;AAAA,EACZ;AACA,QAAM,QAAQ;AAAA,IACZ,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,cAAc,CAAC;AAAA,IACf,gBAAgB,CAAC;AAAA,EACnB;AACA,QAAM,WAAW;AAAA,IACf,GAAG;AAAA,IACH,GAAG;AAAA,IACH,eAAe;AAAA,IACf,eAAe;AAAA,IACf,UAAU;AAAA,EACZ;AACA,MAAI,QAAQ;AACZ,SAAO,eAAe,OAAO,MAAM,SAAS;AAAA,IAC1C,MAAM;AACJ,aAAO;AAAA,IACT;AAAA,IACA,IAAI,OAAO;AACT,UAAI,UAAU,OAAO;AACnB,cAAM,UAAU,QAAQ;AACxB,cAAM,UAAU,QAAQ;AACxB,aAAK,cAAc,OAAO,SAAS,OAAO;AAAA,MAC5C;AACA,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,WAAS,4BAA4B;AACnC,QAAI,QAAQ,SAAS;AAAG,aAAO;AAC/B,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,WAAW,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,OAAO,CAAC;AAC1D,WAAO;AAAA,EACT;AACA,WAAS,iBAAiB;AACxB,QAAI,QAAQ,SAAS;AAAG,aAAO;AAAA,QAC7B,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,UAAM,MAAM,QAAQ,QAAQ,sBAAsB;AAClD,WAAO,EAAE,QAAQ,CAAC,EAAE,SAAS,QAAQ,CAAC,EAAE,QAAQ,QAAQ,CAAC,EAAE,SAAS,IAAI,IAAI,KAAK,eAAe,QAAQ,CAAC,EAAE,SAAS,QAAQ,CAAC,EAAE,QAAQ,QAAQ,CAAC,EAAE,SAAS,IAAI,IAAI,KAAK,YAAY;AAAA,EACtL;AACA,WAAS,mBAAmB;AAC1B,WAAO,OAAO,YAAY,iBAAiB,IAAI,OAAO,OAAO;AAAA,EAC/D;AACA,WAAS,iBAAiB,GAAG;AAC3B,UAAM,gBAAgB,iBAAiB;AACvC,QAAI,EAAE,OAAO,QAAQ,aAAa;AAAG,aAAO;AAC5C,QAAI,OAAO,OAAO,OAAO,aAAW,QAAQ,SAAS,EAAE,MAAM,CAAC,EAAE,SAAS;AAAG,aAAO;AACnF,WAAO;AAAA,EACT;AACA,WAAS,yBAAyB,GAAG;AACnC,UAAM,WAAW,IAAI,OAAO,OAAO,KAAK;AACxC,QAAI,EAAE,OAAO,QAAQ,QAAQ;AAAG,aAAO;AACvC,QAAI,CAAC,GAAG,OAAO,GAAG,iBAAiB,QAAQ,CAAC,EAAE,OAAO,iBAAe,YAAY,SAAS,EAAE,MAAM,CAAC,EAAE,SAAS;AAAG,aAAO;AACvH,WAAO;AAAA,EACT;AAGA,WAAS,eAAe,GAAG;AACzB,QAAI,EAAE,gBAAgB,SAAS;AAC7B,cAAQ,OAAO,GAAG,QAAQ,MAAM;AAAA,IAClC;AACA,QAAI,CAAC,iBAAiB,CAAC;AAAG;AAC1B,UAAM,SAAS,OAAO,OAAO;AAC7B,yBAAqB;AACrB,uBAAmB;AACnB,YAAQ,KAAK,CAAC;AACd,QAAI,QAAQ,SAAS,GAAG;AACtB;AAAA,IACF;AACA,yBAAqB;AACrB,YAAQ,aAAa,0BAA0B;AAC/C,QAAI,CAAC,QAAQ,SAAS;AACpB,cAAQ,UAAU,EAAE,OAAO,QAAQ,IAAI,OAAO,OAAO,0BAA0B;AAC/E,UAAI,CAAC,QAAQ;AAAS,gBAAQ,UAAU,OAAO,OAAO,OAAO,WAAW;AACxE,UAAI,UAAU,QAAQ,QAAQ,cAAc,IAAI,OAAO,gBAAgB;AACvE,UAAI,SAAS;AACX,kBAAU,QAAQ,iBAAiB,gDAAgD,EAAE,CAAC;AAAA,MACxF;AACA,cAAQ,UAAU;AAClB,UAAI,SAAS;AACX,gBAAQ,cAAc,eAAe,QAAQ,SAAS,IAAI,OAAO,gBAAgB,EAAE,CAAC;AAAA,MACtF,OAAO;AACL,gBAAQ,cAAc;AAAA,MACxB;AACA,UAAI,CAAC,QAAQ,aAAa;AACxB,gBAAQ,UAAU;AAClB;AAAA,MACF;AACA,cAAQ,WAAW,QAAQ,YAAY,aAAa,kBAAkB,KAAK,OAAO;AAAA,IACpF;AACA,QAAI,QAAQ,SAAS;AACnB,YAAM,CAAC,SAAS,OAAO,IAAI,eAAe;AAC1C,cAAQ,UAAU;AAClB,cAAQ,UAAU;AAClB,cAAQ,QAAQ,MAAM,qBAAqB;AAAA,IAC7C;AACA,gBAAY;AAAA,EACd;AACA,WAAS,gBAAgB,GAAG;AAC1B,QAAI,CAAC,iBAAiB,CAAC;AAAG;AAC1B,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,OAAO,OAAO;AACpB,UAAM,eAAe,QAAQ,UAAU,cAAY,SAAS,cAAc,EAAE,SAAS;AACrF,QAAI,gBAAgB;AAAG,cAAQ,YAAY,IAAI;AAC/C,QAAI,QAAQ,SAAS,GAAG;AACtB;AAAA,IACF;AACA,uBAAmB;AACnB,YAAQ,YAAY,0BAA0B;AAC9C,QAAI,CAAC,QAAQ,SAAS;AACpB;AAAA,IACF;AACA,SAAK,QAAQ,QAAQ,YAAY,QAAQ,aAAa;AACtD,QAAI,KAAK,QAAQ,QAAQ,UAAU;AACjC,WAAK,QAAQ,QAAQ,WAAW,KAAK,KAAK,QAAQ,QAAQ,WAAW,MAAM;AAAA,IAC7E;AACA,QAAI,KAAK,QAAQ,OAAO,UAAU;AAChC,WAAK,QAAQ,OAAO,WAAW,KAAK,OAAO,WAAW,KAAK,QAAQ,MAAM;AAAA,IAC3E;AACA,YAAQ,QAAQ,MAAM,YAAY,4BAA4B,KAAK;AAAA,EACrE;AACA,WAAS,aAAa,GAAG;AACvB,QAAI,CAAC,iBAAiB,CAAC;AAAG;AAC1B,QAAI,EAAE,gBAAgB,WAAW,EAAE,SAAS;AAAc;AAC1D,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,OAAO,OAAO;AACpB,UAAM,eAAe,QAAQ,UAAU,cAAY,SAAS,cAAc,EAAE,SAAS;AACrF,QAAI,gBAAgB;AAAG,cAAQ,OAAO,cAAc,CAAC;AACrD,QAAI,CAAC,sBAAsB,CAAC,kBAAkB;AAC5C;AAAA,IACF;AACA,yBAAqB;AACrB,uBAAmB;AACnB,QAAI,CAAC,QAAQ;AAAS;AACtB,SAAK,QAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,QAAQ,QAAQ,GAAG,OAAO,QAAQ;AAC7E,YAAQ,QAAQ,MAAM,qBAAqB,GAAG,OAAO,OAAO;AAC5D,YAAQ,QAAQ,MAAM,YAAY,4BAA4B,KAAK;AACnE,mBAAe,KAAK;AACpB,gBAAY;AACZ,QAAI,KAAK,QAAQ,KAAK,QAAQ,SAAS;AACrC,cAAQ,QAAQ,UAAU,IAAI,GAAG,OAAO,kBAAkB;AAAA,IAC5D,WAAW,KAAK,SAAS,KAAK,QAAQ,SAAS;AAC7C,cAAQ,QAAQ,UAAU,OAAO,GAAG,OAAO,kBAAkB;AAAA,IAC/D;AACA,QAAI,KAAK,UAAU,GAAG;AACpB,cAAQ,UAAU;AAClB,cAAQ,UAAU;AAClB,cAAQ,UAAU;AAAA,IACpB;AAAA,EACF;AACA,WAASC,cAAa,GAAG;AACvB,UAAM,SAAS,OAAO;AACtB,QAAI,CAAC,QAAQ;AAAS;AACtB,QAAI,MAAM;AAAW;AACrB,QAAI,OAAO,WAAW,EAAE;AAAY,QAAE,eAAe;AACrD,UAAM,YAAY;AAClB,UAAMC,SAAQ,QAAQ,SAAS,IAAI,QAAQ,CAAC,IAAI;AAChD,UAAM,aAAa,IAAIA,OAAM;AAC7B,UAAM,aAAa,IAAIA,OAAM;AAAA,EAC/B;AACA,WAASC,aAAY,GAAG;AACtB,QAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,yBAAyB,CAAC;AAAG;AAC1D,UAAM,OAAO,OAAO;AACpB,QAAI,CAAC,QAAQ;AAAS;AACtB,QAAI,CAAC,MAAM,aAAa,CAAC,QAAQ;AAAS;AAC1C,QAAI,CAAC,MAAM,SAAS;AAClB,YAAM,QAAQ,QAAQ,QAAQ;AAC9B,YAAM,SAAS,QAAQ,QAAQ;AAC/B,YAAM,SAAS,aAAa,QAAQ,aAAa,GAAG,KAAK;AACzD,YAAM,SAAS,aAAa,QAAQ,aAAa,GAAG,KAAK;AACzD,cAAQ,aAAa,QAAQ,QAAQ;AACrC,cAAQ,cAAc,QAAQ,QAAQ;AACtC,cAAQ,YAAY,MAAM,qBAAqB;AAAA,IACjD;AAEA,UAAM,cAAc,MAAM,QAAQ,KAAK;AACvC,UAAM,eAAe,MAAM,SAAS,KAAK;AACzC,QAAI,cAAc,QAAQ,cAAc,eAAe,QAAQ;AAAa;AAC5E,UAAM,OAAO,KAAK,IAAI,QAAQ,aAAa,IAAI,cAAc,GAAG,CAAC;AACjE,UAAM,OAAO,CAAC,MAAM;AACpB,UAAM,OAAO,KAAK,IAAI,QAAQ,cAAc,IAAI,eAAe,GAAG,CAAC;AACnE,UAAM,OAAO,CAAC,MAAM;AACpB,UAAM,eAAe,IAAI,QAAQ,SAAS,IAAI,QAAQ,CAAC,EAAE,QAAQ,EAAE;AACnE,UAAM,eAAe,IAAI,QAAQ,SAAS,IAAI,QAAQ,CAAC,EAAE,QAAQ,EAAE;AACnE,UAAM,cAAc,KAAK,IAAI,KAAK,IAAI,MAAM,eAAe,IAAI,MAAM,aAAa,CAAC,GAAG,KAAK,IAAI,MAAM,eAAe,IAAI,MAAM,aAAa,CAAC,CAAC;AAC7I,QAAI,cAAc,GAAG;AACnB,aAAO,aAAa;AAAA,IACtB;AACA,QAAI,CAAC,MAAM,WAAW,CAAC,WAAW;AAChC,UAAI,OAAO,aAAa,MAAM,KAAK,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,MAAM,eAAe,IAAI,MAAM,aAAa,KAAK,KAAK,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,MAAM,eAAe,IAAI,MAAM,aAAa,IAAI;AAC3O,cAAM,YAAY;AAClB;AAAA,MACF;AACA,UAAI,CAAC,OAAO,aAAa,MAAM,KAAK,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,MAAM,eAAe,IAAI,MAAM,aAAa,KAAK,KAAK,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,MAAM,eAAe,IAAI,MAAM,aAAa,IAAI;AAC5O,cAAM,YAAY;AAClB;AAAA,MACF;AAAA,IACF;AACA,QAAI,EAAE,YAAY;AAChB,QAAE,eAAe;AAAA,IACnB;AACA,MAAE,gBAAgB;AAClB,UAAM,UAAU;AAChB,UAAM,cAAc,KAAK,QAAQ,iBAAiB,QAAQ,WAAW,OAAO,OAAO,KAAK;AACxF,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,MAAM,eAAe,IAAI,MAAM,aAAa,IAAI,MAAM,SAAS,cAAc,MAAM,QAAQ,UAAU;AACtH,UAAM,WAAW,MAAM,eAAe,IAAI,MAAM,aAAa,IAAI,MAAM,SAAS,cAAc,MAAM,SAAS,UAAU;AACvH,QAAI,MAAM,WAAW,MAAM,MAAM;AAC/B,YAAM,WAAW,MAAM,OAAO,KAAK,MAAM,OAAO,MAAM,WAAW,MAAM;AAAA,IACzE;AACA,QAAI,MAAM,WAAW,MAAM,MAAM;AAC/B,YAAM,WAAW,MAAM,OAAO,KAAK,MAAM,WAAW,MAAM,OAAO,MAAM;AAAA,IACzE;AACA,QAAI,MAAM,WAAW,MAAM,MAAM;AAC/B,YAAM,WAAW,MAAM,OAAO,KAAK,MAAM,OAAO,MAAM,WAAW,MAAM;AAAA,IACzE;AACA,QAAI,MAAM,WAAW,MAAM,MAAM;AAC/B,YAAM,WAAW,MAAM,OAAO,KAAK,MAAM,WAAW,MAAM,OAAO,MAAM;AAAA,IACzE;AAGA,QAAI,CAAC,SAAS;AAAe,eAAS,gBAAgB,MAAM,eAAe;AAC3E,QAAI,CAAC,SAAS;AAAe,eAAS,gBAAgB,MAAM,eAAe;AAC3E,QAAI,CAAC,SAAS;AAAU,eAAS,WAAW,KAAK,IAAI;AACrD,aAAS,KAAK,MAAM,eAAe,IAAI,SAAS,kBAAkB,KAAK,IAAI,IAAI,SAAS,YAAY;AACpG,aAAS,KAAK,MAAM,eAAe,IAAI,SAAS,kBAAkB,KAAK,IAAI,IAAI,SAAS,YAAY;AACpG,QAAI,KAAK,IAAI,MAAM,eAAe,IAAI,SAAS,aAAa,IAAI;AAAG,eAAS,IAAI;AAChF,QAAI,KAAK,IAAI,MAAM,eAAe,IAAI,SAAS,aAAa,IAAI;AAAG,eAAS,IAAI;AAChF,aAAS,gBAAgB,MAAM,eAAe;AAC9C,aAAS,gBAAgB,MAAM,eAAe;AAC9C,aAAS,WAAW,KAAK,IAAI;AAC7B,YAAQ,YAAY,MAAM,YAAY,eAAe,MAAM,eAAe,MAAM;AAAA,EAClF;AACA,WAASC,cAAa;AACpB,UAAM,OAAO,OAAO;AACpB,QAAI,CAAC,QAAQ;AAAS;AACtB,QAAI,CAAC,MAAM,aAAa,CAAC,MAAM,SAAS;AACtC,YAAM,YAAY;AAClB,YAAM,UAAU;AAChB;AAAA,IACF;AACA,UAAM,YAAY;AAClB,UAAM,UAAU;AAChB,QAAI,oBAAoB;AACxB,QAAI,oBAAoB;AACxB,UAAM,oBAAoB,SAAS,IAAI;AACvC,UAAM,eAAe,MAAM,WAAW;AACtC,UAAM,oBAAoB,SAAS,IAAI;AACvC,UAAM,eAAe,MAAM,WAAW;AAGtC,QAAI,SAAS,MAAM;AAAG,0BAAoB,KAAK,KAAK,eAAe,MAAM,YAAY,SAAS,CAAC;AAC/F,QAAI,SAAS,MAAM;AAAG,0BAAoB,KAAK,KAAK,eAAe,MAAM,YAAY,SAAS,CAAC;AAC/F,UAAM,mBAAmB,KAAK,IAAI,mBAAmB,iBAAiB;AACtE,UAAM,WAAW;AACjB,UAAM,WAAW;AAEjB,UAAM,cAAc,MAAM,QAAQ,KAAK;AACvC,UAAM,eAAe,MAAM,SAAS,KAAK;AACzC,UAAM,OAAO,KAAK,IAAI,QAAQ,aAAa,IAAI,cAAc,GAAG,CAAC;AACjE,UAAM,OAAO,CAAC,MAAM;AACpB,UAAM,OAAO,KAAK,IAAI,QAAQ,cAAc,IAAI,eAAe,GAAG,CAAC;AACnE,UAAM,OAAO,CAAC,MAAM;AACpB,UAAM,WAAW,KAAK,IAAI,KAAK,IAAI,MAAM,UAAU,MAAM,IAAI,GAAG,MAAM,IAAI;AAC1E,UAAM,WAAW,KAAK,IAAI,KAAK,IAAI,MAAM,UAAU,MAAM,IAAI,GAAG,MAAM,IAAI;AAC1E,YAAQ,YAAY,MAAM,qBAAqB,GAAG;AAClD,YAAQ,YAAY,MAAM,YAAY,eAAe,MAAM,eAAe,MAAM;AAAA,EAClF;AACA,WAAS,kBAAkB;AACzB,UAAM,OAAO,OAAO;AACpB,QAAI,QAAQ,WAAW,OAAO,gBAAgB,OAAO,OAAO,QAAQ,QAAQ,OAAO,GAAG;AACpF,UAAI,QAAQ,SAAS;AACnB,gBAAQ,QAAQ,MAAM,YAAY;AAAA,MACpC;AACA,UAAI,QAAQ,aAAa;AACvB,gBAAQ,YAAY,MAAM,YAAY;AAAA,MACxC;AACA,cAAQ,QAAQ,UAAU,OAAO,GAAG,OAAO,OAAO,KAAK,kBAAkB;AACzE,WAAK,QAAQ;AACb,qBAAe;AACf,cAAQ,UAAU;AAClB,cAAQ,UAAU;AAClB,cAAQ,cAAc;AACtB,cAAQ,UAAU;AAClB,cAAQ,UAAU;AAAA,IACpB;AAAA,EACF;AACA,WAAS,OAAO,GAAG;AACjB,UAAM,OAAO,OAAO;AACpB,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,CAAC,QAAQ,SAAS;AACpB,UAAI,KAAK,EAAE,QAAQ;AACjB,gBAAQ,UAAU,EAAE,OAAO,QAAQ,IAAI,OAAO,OAAO,0BAA0B;AAAA,MACjF;AACA,UAAI,CAAC,QAAQ,SAAS;AACpB,YAAI,OAAO,OAAO,WAAW,OAAO,OAAO,QAAQ,WAAW,OAAO,SAAS;AAC5E,kBAAQ,UAAU,gBAAgB,OAAO,UAAU,IAAI,OAAO,OAAO,kBAAkB,EAAE,CAAC;AAAA,QAC5F,OAAO;AACL,kBAAQ,UAAU,OAAO,OAAO,OAAO,WAAW;AAAA,QACpD;AAAA,MACF;AACA,UAAI,UAAU,QAAQ,QAAQ,cAAc,IAAI,OAAO,gBAAgB;AACvE,UAAI,SAAS;AACX,kBAAU,QAAQ,iBAAiB,gDAAgD,EAAE,CAAC;AAAA,MACxF;AACA,cAAQ,UAAU;AAClB,UAAI,SAAS;AACX,gBAAQ,cAAc,eAAe,QAAQ,SAAS,IAAI,OAAO,gBAAgB,EAAE,CAAC;AAAA,MACtF,OAAO;AACL,gBAAQ,cAAc;AAAA,MACxB;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,WAAW,CAAC,QAAQ;AAAa;AAC9C,QAAI,OAAO,OAAO,SAAS;AACzB,aAAO,UAAU,MAAM,WAAW;AAClC,aAAO,UAAU,MAAM,cAAc;AAAA,IACvC;AACA,YAAQ,QAAQ,UAAU,IAAI,GAAG,OAAO,kBAAkB;AAC1D,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,OAAO,MAAM,aAAa,MAAM,eAAe,GAAG;AACpD,eAAS,EAAE;AACX,eAAS,EAAE;AAAA,IACb,OAAO;AACL,eAAS,MAAM,aAAa;AAC5B,eAAS,MAAM,aAAa;AAAA,IAC9B;AACA,UAAM,iBAAiB,OAAO,MAAM,WAAW,IAAI;AACnD,QAAI,iBAAiB,KAAK,gBAAgB;AACxC,eAAS;AACT,eAAS;AAAA,IACX;AACA,SAAK,QAAQ,kBAAkB,QAAQ,YAAY,aAAa,kBAAkB,KAAK,OAAO;AAC9F,mBAAe,kBAAkB,QAAQ,YAAY,aAAa,kBAAkB,KAAK,OAAO;AAChG,QAAI,KAAK,EAAE,iBAAiB,KAAK,iBAAiB;AAChD,mBAAa,QAAQ,QAAQ;AAC7B,oBAAc,QAAQ,QAAQ;AAC9B,gBAAU,cAAc,QAAQ,OAAO,EAAE,OAAOJ,QAAO;AACvD,gBAAU,cAAc,QAAQ,OAAO,EAAE,MAAMA,QAAO;AACtD,cAAQ,UAAU,aAAa,IAAI;AACnC,cAAQ,UAAU,cAAc,IAAI;AACpC,mBAAa,QAAQ,QAAQ;AAC7B,oBAAc,QAAQ,QAAQ;AAC9B,oBAAc,aAAa,KAAK;AAChC,qBAAe,cAAc,KAAK;AAClC,sBAAgB,KAAK,IAAI,aAAa,IAAI,cAAc,GAAG,CAAC;AAC5D,sBAAgB,KAAK,IAAI,cAAc,IAAI,eAAe,GAAG,CAAC;AAC9D,sBAAgB,CAAC;AACjB,sBAAgB,CAAC;AACjB,mBAAa,QAAQ,KAAK;AAC1B,mBAAa,QAAQ,KAAK;AAC1B,UAAI,aAAa,eAAe;AAC9B,qBAAa;AAAA,MACf;AACA,UAAI,aAAa,eAAe;AAC9B,qBAAa;AAAA,MACf;AACA,UAAI,aAAa,eAAe;AAC9B,qBAAa;AAAA,MACf;AACA,UAAI,aAAa,eAAe;AAC9B,qBAAa;AAAA,MACf;AAAA,IACF,OAAO;AACL,mBAAa;AACb,mBAAa;AAAA,IACf;AACA,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,cAAQ,UAAU;AAClB,cAAQ,UAAU;AAAA,IACpB;AACA,YAAQ,YAAY,MAAM,qBAAqB;AAC/C,YAAQ,YAAY,MAAM,YAAY,eAAe,iBAAiB;AACtE,YAAQ,QAAQ,MAAM,qBAAqB;AAC3C,YAAQ,QAAQ,MAAM,YAAY,4BAA4B,KAAK;AAAA,EACrE;AACA,WAAS,UAAU;AACjB,UAAM,OAAO,OAAO;AACpB,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,CAAC,QAAQ,SAAS;AACpB,UAAI,OAAO,OAAO,WAAW,OAAO,OAAO,QAAQ,WAAW,OAAO,SAAS;AAC5E,gBAAQ,UAAU,gBAAgB,OAAO,UAAU,IAAI,OAAO,OAAO,kBAAkB,EAAE,CAAC;AAAA,MAC5F,OAAO;AACL,gBAAQ,UAAU,OAAO,OAAO,OAAO,WAAW;AAAA,MACpD;AACA,UAAI,UAAU,QAAQ,QAAQ,cAAc,IAAI,OAAO,gBAAgB;AACvE,UAAI,SAAS;AACX,kBAAU,QAAQ,iBAAiB,gDAAgD,EAAE,CAAC;AAAA,MACxF;AACA,cAAQ,UAAU;AAClB,UAAI,SAAS;AACX,gBAAQ,cAAc,eAAe,QAAQ,SAAS,IAAI,OAAO,gBAAgB,EAAE,CAAC;AAAA,MACtF,OAAO;AACL,gBAAQ,cAAc;AAAA,MACxB;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,WAAW,CAAC,QAAQ;AAAa;AAC9C,QAAI,OAAO,OAAO,SAAS;AACzB,aAAO,UAAU,MAAM,WAAW;AAClC,aAAO,UAAU,MAAM,cAAc;AAAA,IACvC;AACA,SAAK,QAAQ;AACb,mBAAe;AACf,YAAQ,YAAY,MAAM,qBAAqB;AAC/C,YAAQ,YAAY,MAAM,YAAY;AACtC,YAAQ,QAAQ,MAAM,qBAAqB;AAC3C,YAAQ,QAAQ,MAAM,YAAY;AAClC,YAAQ,QAAQ,UAAU,OAAO,GAAG,OAAO,kBAAkB;AAC7D,YAAQ,UAAU;AAClB,YAAQ,UAAU;AAClB,YAAQ,UAAU;AAAA,EACpB;AAGA,WAAS,WAAW,GAAG;AACrB,UAAM,OAAO,OAAO;AACpB,QAAI,KAAK,SAAS,KAAK,UAAU,GAAG;AAElC,cAAQ;AAAA,IACV,OAAO;AAEL,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACA,WAAS,eAAe;AACtB,UAAM,kBAAkB,OAAO,OAAO,mBAAmB;AAAA,MACvD,SAAS;AAAA,MACT,SAAS;AAAA,IACX,IAAI;AACJ,UAAM,4BAA4B,OAAO,OAAO,mBAAmB;AAAA,MACjE,SAAS;AAAA,MACT,SAAS;AAAA,IACX,IAAI;AACJ,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAGA,WAAS,SAAS;AAChB,UAAM,OAAO,OAAO;AACpB,QAAI,KAAK;AAAS;AAClB,SAAK,UAAU;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,aAAa;AAGjB,WAAO,UAAU,iBAAiB,eAAe,gBAAgB,eAAe;AAChF,WAAO,UAAU,iBAAiB,eAAe,iBAAiB,yBAAyB;AAC3F,KAAC,aAAa,iBAAiB,YAAY,EAAE,QAAQ,eAAa;AAChE,aAAO,UAAU,iBAAiB,WAAW,cAAc,eAAe;AAAA,IAC5E,CAAC;AAGD,WAAO,UAAU,iBAAiB,eAAeG,cAAa,yBAAyB;AAAA,EACzF;AACA,WAAS,UAAU;AACjB,UAAM,OAAO,OAAO;AACpB,QAAI,CAAC,KAAK;AAAS;AACnB,SAAK,UAAU;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,aAAa;AAGjB,WAAO,UAAU,oBAAoB,eAAe,gBAAgB,eAAe;AACnF,WAAO,UAAU,oBAAoB,eAAe,iBAAiB,yBAAyB;AAC9F,KAAC,aAAa,iBAAiB,YAAY,EAAE,QAAQ,eAAa;AAChE,aAAO,UAAU,oBAAoB,WAAW,cAAc,eAAe;AAAA,IAC/E,CAAC;AAGD,WAAO,UAAU,oBAAoB,eAAeA,cAAa,yBAAyB;AAAA,EAC5F;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,KAAK,SAAS;AAC9B,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,YAAQ;AAAA,EACV,CAAC;AACD,KAAG,cAAc,CAAC,IAAI,MAAM;AAC1B,QAAI,CAAC,OAAO,KAAK;AAAS;AAC1B,IAAAF,cAAa,CAAC;AAAA,EAChB,CAAC;AACD,KAAG,YAAY,CAAC,IAAI,MAAM;AACxB,QAAI,CAAC,OAAO,KAAK;AAAS;AAC1B,IAAAG,YAAW,CAAC;AAAA,EACd,CAAC;AACD,KAAG,aAAa,CAAC,IAAI,MAAM;AACzB,QAAI,CAAC,OAAO,aAAa,OAAO,OAAO,KAAK,WAAW,OAAO,KAAK,WAAW,OAAO,OAAO,KAAK,QAAQ;AACvG,iBAAW,CAAC;AAAA,IACd;AAAA,EACF,CAAC;AACD,KAAG,iBAAiB,MAAM;AACxB,QAAI,OAAO,KAAK,WAAW,OAAO,OAAO,KAAK,SAAS;AACrD,sBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,KAAG,eAAe,MAAM;AACtB,QAAI,OAAO,KAAK,WAAW,OAAO,OAAO,KAAK,WAAW,OAAO,OAAO,SAAS;AAC9E,sBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,SAAO,OAAO,OAAO,MAAM;AAAA,IACzB;AAAA,IACA;AAAA,IACA,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,QAAQ;AAAA,EACV,CAAC;AACH;;;ACxkBe,SAAR,WAA4B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,YAAY;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,IAAI;AAAA;AAAA,IACN;AAAA,EACF,CAAC;AAED,SAAO,aAAa;AAAA,IAClB,SAAS;AAAA,EACX;AACA,WAAS,aAAa,GAAG,GAAG;AAC1B,UAAM,eAAe,SAAS,SAAS;AACrC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,aAAO,CAAC,OAAO,QAAQ;AACrB,mBAAW;AACX,mBAAW,MAAM;AACjB,eAAO,WAAW,WAAW,GAAG;AAC9B,kBAAQ,WAAW,YAAY;AAC/B,cAAI,MAAM,KAAK,KAAK,KAAK;AACvB,uBAAW;AAAA,UACb,OAAO;AACL,uBAAW;AAAA,UACb;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF,EAAE;AACF,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,YAAY,EAAE,SAAS;AAI5B,QAAI;AACJ,QAAI;AACJ,SAAK,cAAc,SAAS,YAAY,IAAI;AAC1C,UAAI,CAAC;AAAI,eAAO;AAGhB,WAAK,aAAa,KAAK,GAAG,EAAE;AAC5B,WAAK,KAAK;AAIV,cAAQ,KAAK,KAAK,EAAE,EAAE,MAAM,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,MAAM,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,KAAK,KAAK,EAAE,EAAE;AAAA,IAC9F;AACA,WAAO;AAAA,EACT;AAEA,WAAS,uBAAuB,GAAG;AACjC,QAAI,CAAC,OAAO,WAAW,QAAQ;AAC7B,aAAO,WAAW,SAAS,OAAO,OAAO,OAAO,IAAI,aAAa,OAAO,YAAY,EAAE,UAAU,IAAI,IAAI,aAAa,OAAO,UAAU,EAAE,QAAQ;AAAA,IAClJ;AAAA,EACF;AACA,WAASC,cAAa,IAAI,cAAc;AACtC,UAAM,aAAa,OAAO,WAAW;AACrC,QAAI;AACJ,QAAI;AACJ,UAAMC,UAAS,OAAO;AACtB,aAAS,uBAAuB,GAAG;AACjC,UAAI,EAAE;AAAW;AAMjB,YAAM,YAAY,OAAO,eAAe,CAAC,OAAO,YAAY,OAAO;AACnE,UAAI,OAAO,OAAO,WAAW,OAAO,SAAS;AAC3C,+BAAuB,CAAC;AAGxB,8BAAsB,CAAC,OAAO,WAAW,OAAO,YAAY,CAAC,SAAS;AAAA,MACxE;AACA,UAAI,CAAC,uBAAuB,OAAO,OAAO,WAAW,OAAO,aAAa;AACvE,sBAAc,EAAE,aAAa,IAAI,EAAE,aAAa,MAAM,OAAO,aAAa,IAAI,OAAO,aAAa;AAClG,+BAAuB,YAAY,OAAO,aAAa,KAAK,aAAa,EAAE,aAAa;AAAA,MAC1F;AACA,UAAI,OAAO,OAAO,WAAW,SAAS;AACpC,8BAAsB,EAAE,aAAa,IAAI;AAAA,MAC3C;AACA,QAAE,eAAe,mBAAmB;AACpC,QAAE,aAAa,qBAAqB,MAAM;AAC1C,QAAE,kBAAkB;AACpB,QAAE,oBAAoB;AAAA,IACxB;AACA,QAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AAC7C,YAAI,WAAW,CAAC,MAAM,gBAAgB,WAAW,CAAC,aAAaA,SAAQ;AACrE,iCAAuB,WAAW,CAAC,CAAC;AAAA,QACtC;AAAA,MACF;AAAA,IACF,WAAW,sBAAsBA,WAAU,iBAAiB,YAAY;AACtE,6BAAuB,UAAU;AAAA,IACnC;AAAA,EACF;AACA,WAASC,eAAc,UAAU,cAAc;AAC7C,UAAMD,UAAS,OAAO;AACtB,UAAM,aAAa,OAAO,WAAW;AACrC,QAAI;AACJ,aAAS,wBAAwB,GAAG;AAClC,UAAI,EAAE;AAAW;AACjB,QAAE,cAAc,UAAU,MAAM;AAChC,UAAI,aAAa,GAAG;AAClB,UAAE,gBAAgB;AAClB,YAAI,EAAE,OAAO,YAAY;AACvB,mBAAS,MAAM;AACb,cAAE,iBAAiB;AAAA,UACrB,CAAC;AAAA,QACH;AACA,6BAAqB,EAAE,WAAW,MAAM;AACtC,cAAI,CAAC;AAAY;AACjB,YAAE,cAAc;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,WAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AACzC,YAAI,WAAW,CAAC,MAAM,gBAAgB,WAAW,CAAC,aAAaA,SAAQ;AACrE,kCAAwB,WAAW,CAAC,CAAC;AAAA,QACvC;AAAA,MACF;AAAA,IACF,WAAW,sBAAsBA,WAAU,iBAAiB,YAAY;AACtE,8BAAwB,UAAU;AAAA,IACpC;AAAA,EACF;AACA,WAAS,eAAe;AACtB,QAAI,CAAC,OAAO,WAAW;AAAS;AAChC,QAAI,OAAO,WAAW,QAAQ;AAC5B,aAAO,WAAW,SAAS;AAC3B,aAAO,OAAO,WAAW;AAAA,IAC3B;AAAA,EACF;AACA,KAAG,cAAc,MAAM;AACrB,QAAI,OAAO,WAAW;AAAA,KAEtB,OAAO,OAAO,OAAO,WAAW,YAAY,YAAY,OAAO,OAAO,WAAW,mBAAmB,cAAc;AAChH,YAAM,iBAAiB,SAAS,cAAc,OAAO,OAAO,WAAW,OAAO;AAC9E,UAAI,kBAAkB,eAAe,QAAQ;AAC3C,eAAO,WAAW,UAAU,eAAe;AAAA,MAC7C,WAAW,gBAAgB;AACzB,cAAM,qBAAqB,OAAK;AAC9B,iBAAO,WAAW,UAAU,EAAE,OAAO,CAAC;AACtC,iBAAO,OAAO;AACd,yBAAe,oBAAoB,QAAQ,kBAAkB;AAAA,QAC/D;AACA,uBAAe,iBAAiB,QAAQ,kBAAkB;AAAA,MAC5D;AACA;AAAA,IACF;AACA,WAAO,WAAW,UAAU,OAAO,OAAO,WAAW;AAAA,EACvD,CAAC;AACD,KAAG,UAAU,MAAM;AACjB,iBAAa;AAAA,EACf,CAAC;AACD,KAAG,UAAU,MAAM;AACjB,iBAAa;AAAA,EACf,CAAC;AACD,KAAG,kBAAkB,MAAM;AACzB,iBAAa;AAAA,EACf,CAAC;AACD,KAAG,gBAAgB,CAAC,IAAI,WAAW,iBAAiB;AAClD,QAAI,CAAC,OAAO,WAAW;AAAS;AAChC,WAAO,WAAW,aAAa,WAAW,YAAY;AAAA,EACxD,CAAC;AACD,KAAG,iBAAiB,CAAC,IAAI,UAAU,iBAAiB;AAClD,QAAI,CAAC,OAAO,WAAW;AAAS;AAChC,WAAO,WAAW,cAAc,UAAU,YAAY;AAAA,EACxD,CAAC;AACD,SAAO,OAAO,OAAO,YAAY;AAAA,IAC/B,cAAAD;AAAA,IACA,eAAAE;AAAA,EACF,CAAC;AACH;;;ACpLe,SAAR,KAAsB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,yBAAyB;AAAA,MACzB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,iCAAiC;AAAA,MACjC,4BAA4B;AAAA,MAC5B,WAAW;AAAA,MACX,IAAI;AAAA,IACN;AAAA,EACF,CAAC;AACD,SAAO,OAAO;AAAA,IACZ,SAAS;AAAA,EACX;AACA,MAAI,aAAa;AACjB,WAAS,OAAO,SAAS;AACvB,UAAM,eAAe;AACrB,QAAI,aAAa,WAAW;AAAG;AAC/B,iBAAa,YAAY;AACzB,iBAAa,YAAY;AAAA,EAC3B;AACA,QAAM,oBAAoB,QAAM;AAC9B,QAAI,CAAC,MAAM,QAAQ,EAAE;AAAG,WAAK,CAAC,EAAE,EAAE,OAAO,OAAK,CAAC,CAAC,CAAC;AACjD,WAAO;AAAA,EACT;AACA,WAAS,gBAAgB,OAAO,IAAI;AAClC,UAAM,aAAa,MAAM,KAAK,MAAM,KAAK,KAAK,OAAO,CAAC,EAAE,SAAS,EAAE;AACnE,WAAO,IAAI,OAAO,IAAI,EAAE,QAAQ,MAAM,UAAU;AAAA,EAClD;AACA,WAAS,gBAAgB,IAAI;AAC3B,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,YAAY,GAAG;AAAA,IACpC,CAAC;AAAA,EACH;AACA,WAAS,mBAAmB,IAAI;AAC9B,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,YAAY,IAAI;AAAA,IACrC,CAAC;AAAA,EACH;AACA,WAAS,UAAU,IAAI,MAAM;AAC3B,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,QAAQ,IAAI;AAAA,IACjC,CAAC;AAAA,EACH;AACA,WAAS,qBAAqB,IAAI,aAAa;AAC7C,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,wBAAwB,WAAW;AAAA,IACxD,CAAC;AAAA,EACH;AACA,WAAS,cAAc,IAAI,UAAU;AACnC,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,iBAAiB,QAAQ;AAAA,IAC9C,CAAC;AAAA,EACH;AACA,WAAS,WAAW,IAAI,OAAO;AAC7B,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,cAAc,KAAK;AAAA,IACxC,CAAC;AAAA,EACH;AACA,WAAS,QAAQ,IAAI,IAAI;AACvB,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,MAAM,EAAE;AAAA,IAC7B,CAAC;AAAA,EACH;AACA,WAAS,UAAU,IAAI,MAAM;AAC3B,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,aAAa,IAAI;AAAA,IACtC,CAAC;AAAA,EACH;AACA,WAAS,UAAU,IAAI;AACrB,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,iBAAiB,IAAI;AAAA,IAC1C,CAAC;AAAA,EACH;AACA,WAAS,SAAS,IAAI;AACpB,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,iBAAiB,KAAK;AAAA,IAC3C,CAAC;AAAA,EACH;AACA,WAAS,kBAAkB,GAAG;AAC5B,QAAI,EAAE,YAAY,MAAM,EAAE,YAAY;AAAI;AAC1C,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,WAAW,EAAE;AACnB,QAAI,OAAO,cAAc,OAAO,WAAW,OAAO,aAAa,OAAO,WAAW,MAAM,OAAO,WAAW,GAAG,SAAS,EAAE,MAAM,IAAI;AAC/H,UAAI,CAAC,EAAE,OAAO,QAAQ,kBAAkB,OAAO,OAAO,WAAW,WAAW,CAAC;AAAG;AAAA,IAClF;AACA,QAAI,OAAO,cAAc,OAAO,WAAW,UAAU,aAAa,OAAO,WAAW,QAAQ;AAC1F,UAAI,EAAE,OAAO,SAAS,CAAC,OAAO,OAAO,OAAO;AAC1C,eAAO,UAAU;AAAA,MACnB;AACA,UAAI,OAAO,OAAO;AAChB,eAAO,OAAO,gBAAgB;AAAA,MAChC,OAAO;AACL,eAAO,OAAO,gBAAgB;AAAA,MAChC;AAAA,IACF;AACA,QAAI,OAAO,cAAc,OAAO,WAAW,UAAU,aAAa,OAAO,WAAW,QAAQ;AAC1F,UAAI,EAAE,OAAO,eAAe,CAAC,OAAO,OAAO,OAAO;AAChD,eAAO,UAAU;AAAA,MACnB;AACA,UAAI,OAAO,aAAa;AACtB,eAAO,OAAO,iBAAiB;AAAA,MACjC,OAAO;AACL,eAAO,OAAO,gBAAgB;AAAA,MAChC;AAAA,IACF;AACA,QAAI,OAAO,cAAc,SAAS,QAAQ,kBAAkB,OAAO,OAAO,WAAW,WAAW,CAAC,GAAG;AAClG,eAAS,MAAM;AAAA,IACjB;AAAA,EACF;AACA,WAAS,mBAAmB;AAC1B,QAAI,OAAO,OAAO,QAAQ,OAAO,OAAO,UAAU,CAAC,OAAO;AAAY;AACtE,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,QAAI,QAAQ;AACV,UAAI,OAAO,aAAa;AACtB,kBAAU,MAAM;AAChB,2BAAmB,MAAM;AAAA,MAC3B,OAAO;AACL,iBAAS,MAAM;AACf,wBAAgB,MAAM;AAAA,MACxB;AAAA,IACF;AACA,QAAI,QAAQ;AACV,UAAI,OAAO,OAAO;AAChB,kBAAU,MAAM;AAChB,2BAAmB,MAAM;AAAA,MAC3B,OAAO;AACL,iBAAS,MAAM;AACf,wBAAgB,MAAM;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACA,WAAS,gBAAgB;AACvB,WAAO,OAAO,cAAc,OAAO,WAAW,WAAW,OAAO,WAAW,QAAQ;AAAA,EACrF;AACA,WAAS,yBAAyB;AAChC,WAAO,cAAc,KAAK,OAAO,OAAO,WAAW;AAAA,EACrD;AACA,WAAS,mBAAmB;AAC1B,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,CAAC,cAAc;AAAG;AACtB,WAAO,WAAW,QAAQ,QAAQ,cAAY;AAC5C,UAAI,OAAO,OAAO,WAAW,WAAW;AACtC,wBAAgB,QAAQ;AACxB,YAAI,CAAC,OAAO,OAAO,WAAW,cAAc;AAC1C,oBAAU,UAAU,QAAQ;AAC5B,qBAAW,UAAU,OAAO,wBAAwB,QAAQ,iBAAiB,aAAa,QAAQ,IAAI,CAAC,CAAC;AAAA,QAC1G;AAAA,MACF;AACA,UAAI,SAAS,QAAQ,kBAAkB,OAAO,OAAO,WAAW,iBAAiB,CAAC,GAAG;AACnF,iBAAS,aAAa,gBAAgB,MAAM;AAAA,MAC9C,OAAO;AACL,iBAAS,gBAAgB,cAAc;AAAA,MACzC;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,YAAY,CAAC,IAAI,WAAW,YAAY;AAC5C,oBAAgB,EAAE;AAClB,QAAI,GAAG,YAAY,UAAU;AAC3B,gBAAU,IAAI,QAAQ;AACtB,SAAG,iBAAiB,WAAW,iBAAiB;AAAA,IAClD;AACA,eAAW,IAAI,OAAO;AACtB,kBAAc,IAAI,SAAS;AAAA,EAC7B;AACA,QAAM,oBAAoB,MAAM;AAC9B,WAAO,KAAK,UAAU;AAAA,EACxB;AACA,QAAM,kBAAkB,MAAM;AAC5B,0BAAsB,MAAM;AAC1B,4BAAsB,MAAM;AAC1B,YAAI,CAAC,OAAO,WAAW;AACrB,iBAAO,KAAK,UAAU;AAAA,QACxB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,QAAM,cAAc,OAAK;AACvB,QAAI,OAAO,KAAK;AAAS;AACzB,UAAM,UAAU,EAAE,OAAO,QAAQ,IAAI,OAAO,OAAO,0BAA0B;AAC7E,QAAI,CAAC,WAAW,CAAC,OAAO,OAAO,SAAS,OAAO;AAAG;AAClD,UAAM,WAAW,OAAO,OAAO,QAAQ,OAAO,MAAM,OAAO;AAC3D,UAAM,YAAY,OAAO,OAAO,uBAAuB,OAAO,iBAAiB,OAAO,cAAc,SAAS,OAAO;AACpH,QAAI,YAAY;AAAW;AAC3B,QAAI,EAAE,sBAAsB,EAAE,mBAAmB;AAAkB;AACnE,QAAI,OAAO,aAAa,GAAG;AACzB,aAAO,GAAG,aAAa;AAAA,IACzB,OAAO;AACL,aAAO,GAAG,YAAY;AAAA,IACxB;AACA,WAAO,QAAQ,OAAO,OAAO,QAAQ,OAAO,GAAG,CAAC;AAAA,EAClD;AACA,QAAM,aAAa,MAAM;AACvB,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,OAAO,4BAA4B;AACrC,2BAAqB,OAAO,QAAQ,OAAO,0BAA0B;AAAA,IACvE;AACA,QAAI,OAAO,WAAW;AACpB,gBAAU,OAAO,QAAQ,OAAO,SAAS;AAAA,IAC3C;AACA,UAAM,eAAe,OAAO,OAAO;AACnC,QAAI,OAAO,mBAAmB;AAC5B,aAAO,OAAO,QAAQ,CAAC,SAAS,UAAU;AACxC,cAAM,aAAa,OAAO,OAAO,OAAO,SAAS,QAAQ,aAAa,yBAAyB,GAAG,EAAE,IAAI;AACxG,cAAM,mBAAmB,OAAO,kBAAkB,QAAQ,iBAAiB,aAAa,CAAC,EAAE,QAAQ,wBAAwB,YAAY;AACvI,mBAAW,SAAS,gBAAgB;AAAA,MACtC,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,OAAO,MAAM;AACjB,UAAM,SAAS,OAAO,OAAO;AAC7B,WAAO,GAAG,OAAO,UAAU;AAG3B,UAAM,cAAc,OAAO;AAC3B,QAAI,OAAO,iCAAiC;AAC1C,2BAAqB,aAAa,OAAO,+BAA+B;AAAA,IAC1E;AACA,QAAI,OAAO,kBAAkB;AAC3B,iBAAW,aAAa,OAAO,gBAAgB;AAAA,IACjD;AAGA,UAAM,YAAY,OAAO;AACzB,UAAM,YAAY,OAAO,MAAM,UAAU,aAAa,IAAI,KAAK,kBAAkB,gBAAgB,EAAE;AACnG,UAAM,OAAO,OAAO,OAAO,YAAY,OAAO,OAAO,SAAS,UAAU,QAAQ;AAChF,YAAQ,WAAW,SAAS;AAC5B,cAAU,WAAW,IAAI;AAGzB,eAAW;AAGX,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,OAAO,aAAa,OAAO,aAAa,CAAC;AAC7C,aAAS,kBAAkB,MAAM;AACjC,aAAS,kBAAkB,MAAM;AACjC,QAAI,QAAQ;AACV,aAAO,QAAQ,QAAM,UAAU,IAAI,WAAW,OAAO,gBAAgB,CAAC;AAAA,IACxE;AACA,QAAI,QAAQ;AACV,aAAO,QAAQ,QAAM,UAAU,IAAI,WAAW,OAAO,gBAAgB,CAAC;AAAA,IACxE;AAGA,QAAI,uBAAuB,GAAG;AAC5B,YAAM,eAAe,MAAM,QAAQ,OAAO,WAAW,EAAE,IAAI,OAAO,WAAW,KAAK,CAAC,OAAO,WAAW,EAAE;AACvG,mBAAa,QAAQ,QAAM;AACzB,WAAG,iBAAiB,WAAW,iBAAiB;AAAA,MAClD,CAAC;AAAA,IACH;AAGA,WAAO,GAAG,iBAAiB,SAAS,aAAa,IAAI;AACrD,WAAO,GAAG,iBAAiB,eAAe,mBAAmB,IAAI;AACjE,WAAO,GAAG,iBAAiB,aAAa,iBAAiB,IAAI;AAAA,EAC/D;AACA,WAAS,UAAU;AACjB,QAAI,cAAc,WAAW,SAAS;AAAG,iBAAW,OAAO;AAC3D,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,OAAO,aAAa,OAAO,aAAa,CAAC;AAC7C,aAAS,kBAAkB,MAAM;AACjC,aAAS,kBAAkB,MAAM;AACjC,QAAI,QAAQ;AACV,aAAO,QAAQ,QAAM,GAAG,oBAAoB,WAAW,iBAAiB,CAAC;AAAA,IAC3E;AACA,QAAI,QAAQ;AACV,aAAO,QAAQ,QAAM,GAAG,oBAAoB,WAAW,iBAAiB,CAAC;AAAA,IAC3E;AAGA,QAAI,uBAAuB,GAAG;AAC5B,YAAM,eAAe,MAAM,QAAQ,OAAO,WAAW,EAAE,IAAI,OAAO,WAAW,KAAK,CAAC,OAAO,WAAW,EAAE;AACvG,mBAAa,QAAQ,QAAM;AACzB,WAAG,oBAAoB,WAAW,iBAAiB;AAAA,MACrD,CAAC;AAAA,IACH;AAGA,WAAO,GAAG,oBAAoB,SAAS,aAAa,IAAI;AACxD,WAAO,GAAG,oBAAoB,eAAe,mBAAmB,IAAI;AACpE,WAAO,GAAG,oBAAoB,aAAa,iBAAiB,IAAI;AAAA,EAClE;AACA,KAAG,cAAc,MAAM;AACrB,iBAAa,cAAc,QAAQ,OAAO,OAAO,KAAK,iBAAiB;AACvE,eAAW,aAAa,aAAa,WAAW;AAChD,eAAW,aAAa,eAAe,MAAM;AAC7C,QAAI,OAAO,WAAW;AACpB,iBAAW,aAAa,QAAQ,eAAe;AAAA,IACjD;AAAA,EACF,CAAC;AACD,KAAG,aAAa,MAAM;AACpB,QAAI,CAAC,OAAO,OAAO,KAAK;AAAS;AACjC,SAAK;AAAA,EACP,CAAC;AACD,KAAG,kEAAkE,MAAM;AACzE,QAAI,CAAC,OAAO,OAAO,KAAK;AAAS;AACjC,eAAW;AAAA,EACb,CAAC;AACD,KAAG,yCAAyC,MAAM;AAChD,QAAI,CAAC,OAAO,OAAO,KAAK;AAAS;AACjC,qBAAiB;AAAA,EACnB,CAAC;AACD,KAAG,oBAAoB,MAAM;AAC3B,QAAI,CAAC,OAAO,OAAO,KAAK;AAAS;AACjC,qBAAiB;AAAA,EACnB,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,QAAI,CAAC,OAAO,OAAO,KAAK;AAAS;AACjC,YAAQ;AAAA,EACV,CAAC;AACH;;;ACpVe,SAAR,QAAyB;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,SAAS;AAAA,MACP,SAAS;AAAA,MACT,MAAM;AAAA,MACN,cAAc;AAAA,MACd,KAAK;AAAA,MACL,WAAW;AAAA,IACb;AAAA,EACF,CAAC;AACD,MAAI,cAAc;AAClB,MAAI,QAAQ,CAAC;AACb,QAAM,UAAU,UAAQ;AACtB,WAAO,KAAK,SAAS,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,YAAY,EAAE,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,EAAE;AAAA,EAC/H;AACA,QAAM,gBAAgB,iBAAe;AACnC,UAAMC,UAAS,UAAU;AACzB,QAAI;AACJ,QAAI,aAAa;AACf,iBAAW,IAAI,IAAI,WAAW;AAAA,IAChC,OAAO;AACL,iBAAWA,QAAO;AAAA,IACpB;AACA,UAAM,YAAY,SAAS,SAAS,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,OAAO,UAAQ,SAAS,EAAE;AAClF,UAAM,QAAQ,UAAU;AACxB,UAAM,MAAM,UAAU,QAAQ,CAAC;AAC/B,UAAM,QAAQ,UAAU,QAAQ,CAAC;AACjC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,CAAC,KAAK,UAAU;AACjC,UAAMA,UAAS,UAAU;AACzB,QAAI,CAAC,eAAe,CAAC,OAAO,OAAO,QAAQ;AAAS;AACpD,QAAI;AACJ,QAAI,OAAO,OAAO,KAAK;AACrB,iBAAW,IAAI,IAAI,OAAO,OAAO,GAAG;AAAA,IACtC,OAAO;AACL,iBAAWA,QAAO;AAAA,IACpB;AACA,UAAM,QAAQ,OAAO,OAAO,KAAK;AACjC,QAAI,QAAQ,QAAQ,MAAM,aAAa,cAAc,CAAC;AACtD,QAAI,OAAO,OAAO,QAAQ,KAAK,SAAS,GAAG;AACzC,UAAI,OAAO,OAAO,OAAO,QAAQ;AACjC,UAAI,KAAK,KAAK,SAAS,CAAC,MAAM;AAAK,eAAO,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC;AACvE,cAAQ,GAAG,QAAQ,MAAM,GAAG,SAAS,KAAK;AAAA,IAC5C,WAAW,CAAC,SAAS,SAAS,SAAS,GAAG,GAAG;AAC3C,cAAQ,GAAG,MAAM,GAAG,SAAS,KAAK;AAAA,IACpC;AACA,QAAI,OAAO,OAAO,QAAQ,WAAW;AACnC,eAAS,SAAS;AAAA,IACpB;AACA,UAAM,eAAeA,QAAO,QAAQ;AACpC,QAAI,gBAAgB,aAAa,UAAU,OAAO;AAChD;AAAA,IACF;AACA,QAAI,OAAO,OAAO,QAAQ,cAAc;AACtC,MAAAA,QAAO,QAAQ,aAAa;AAAA,QAC1B;AAAA,MACF,GAAG,MAAM,KAAK;AAAA,IAChB,OAAO;AACL,MAAAA,QAAO,QAAQ,UAAU;AAAA,QACvB;AAAA,MACF,GAAG,MAAM,KAAK;AAAA,IAChB;AAAA,EACF;AACA,QAAM,gBAAgB,CAAC,OAAO,OAAO,iBAAiB;AACpD,QAAI,OAAO;AACT,eAAS,IAAI,GAAG,SAAS,OAAO,OAAO,QAAQ,IAAI,QAAQ,KAAK,GAAG;AACjE,cAAM,QAAQ,OAAO,OAAO,CAAC;AAC7B,cAAM,eAAe,QAAQ,MAAM,aAAa,cAAc,CAAC;AAC/D,YAAI,iBAAiB,OAAO;AAC1B,gBAAM,QAAQ,OAAO,cAAc,KAAK;AACxC,iBAAO,QAAQ,OAAO,OAAO,YAAY;AAAA,QAC3C;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,QAAQ,GAAG,OAAO,YAAY;AAAA,IACvC;AAAA,EACF;AACA,QAAM,qBAAqB,MAAM;AAC/B,YAAQ,cAAc,OAAO,OAAO,GAAG;AACvC,kBAAc,OAAO,OAAO,OAAO,MAAM,OAAO,KAAK;AAAA,EACvD;AACA,QAAM,OAAO,MAAM;AACjB,UAAMA,UAAS,UAAU;AACzB,QAAI,CAAC,OAAO,OAAO;AAAS;AAC5B,QAAI,CAACA,QAAO,WAAW,CAACA,QAAO,QAAQ,WAAW;AAChD,aAAO,OAAO,QAAQ,UAAU;AAChC,aAAO,OAAO,eAAe,UAAU;AACvC;AAAA,IACF;AACA,kBAAc;AACd,YAAQ,cAAc,OAAO,OAAO,GAAG;AACvC,QAAI,CAAC,MAAM,OAAO,CAAC,MAAM,OAAO;AAC9B,UAAI,CAAC,OAAO,OAAO,QAAQ,cAAc;AACvC,QAAAA,QAAO,iBAAiB,YAAY,kBAAkB;AAAA,MACxD;AACA;AAAA,IACF;AACA,kBAAc,GAAG,MAAM,OAAO,OAAO,OAAO,kBAAkB;AAC9D,QAAI,CAAC,OAAO,OAAO,QAAQ,cAAc;AACvC,MAAAA,QAAO,iBAAiB,YAAY,kBAAkB;AAAA,IACxD;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,UAAMA,UAAS,UAAU;AACzB,QAAI,CAAC,OAAO,OAAO,QAAQ,cAAc;AACvC,MAAAA,QAAO,oBAAoB,YAAY,kBAAkB;AAAA,IAC3D;AAAA,EACF;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,QAAQ,SAAS;AACjC,WAAK;AAAA,IACP;AAAA,EACF,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,QAAI,OAAO,OAAO,QAAQ,SAAS;AACjC,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,KAAG,4CAA4C,MAAM;AACnD,QAAI,aAAa;AACf,iBAAW,OAAO,OAAO,QAAQ,KAAK,OAAO,WAAW;AAAA,IAC1D;AAAA,EACF,CAAC;AACD,KAAG,eAAe,MAAM;AACtB,QAAI,eAAe,OAAO,OAAO,SAAS;AACxC,iBAAW,OAAO,OAAO,QAAQ,KAAK,OAAO,WAAW;AAAA,IAC1D;AAAA,EACF,CAAC;AACH;;;ACvIe,SAAR,eAAgC;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,cAAc;AAClB,QAAMC,YAAW,YAAY;AAC7B,QAAMC,UAAS,UAAU;AACzB,eAAa;AAAA,IACX,gBAAgB;AAAA,MACd,SAAS;AAAA,MACT,cAAc;AAAA,MACd,YAAY;AAAA,IACd;AAAA,EACF,CAAC;AACD,QAAM,eAAe,MAAM;AACzB,SAAK,YAAY;AACjB,UAAM,UAAUD,UAAS,SAAS,KAAK,QAAQ,KAAK,EAAE;AACtD,UAAM,kBAAkB,OAAO,OAAO,OAAO,WAAW,EAAE,aAAa,WAAW;AAClF,QAAI,YAAY,iBAAiB;AAC/B,YAAM,WAAW,OAAO,cAAc,gBAAgB,OAAO,UAAU,IAAI,OAAO,OAAO,yBAAyB,sCAAsC,WAAW,EAAE,CAAC,CAAC;AACvK,UAAI,OAAO,aAAa;AAAa;AACrC,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,QAAI,CAAC,eAAe,CAAC,OAAO,OAAO,eAAe;AAAS;AAC3D,QAAI,OAAO,OAAO,eAAe,gBAAgBC,QAAO,WAAWA,QAAO,QAAQ,cAAc;AAC9F,MAAAA,QAAO,QAAQ,aAAa,MAAM,MAAM,IAAI,OAAO,OAAO,OAAO,WAAW,EAAE,aAAa,WAAW,OAAO,EAAE;AAC/G,WAAK,SAAS;AAAA,IAChB,OAAO;AACL,YAAM,QAAQ,OAAO,OAAO,OAAO,WAAW;AAC9C,YAAM,OAAO,MAAM,aAAa,WAAW,KAAK,MAAM,aAAa,cAAc;AACjF,MAAAD,UAAS,SAAS,OAAO,QAAQ;AACjC,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AACA,QAAM,OAAO,MAAM;AACjB,QAAI,CAAC,OAAO,OAAO,eAAe,WAAW,OAAO,OAAO,WAAW,OAAO,OAAO,QAAQ;AAAS;AACrG,kBAAc;AACd,UAAM,OAAOA,UAAS,SAAS,KAAK,QAAQ,KAAK,EAAE;AACnD,QAAI,MAAM;AACR,YAAM,QAAQ;AACd,eAAS,IAAI,GAAG,SAAS,OAAO,OAAO,QAAQ,IAAI,QAAQ,KAAK,GAAG;AACjE,cAAM,QAAQ,OAAO,OAAO,CAAC;AAC7B,cAAM,YAAY,MAAM,aAAa,WAAW,KAAK,MAAM,aAAa,cAAc;AACtF,YAAI,cAAc,MAAM;AACtB,gBAAM,QAAQ,OAAO,cAAc,KAAK;AACxC,iBAAO,QAAQ,OAAO,OAAO,OAAO,OAAO,oBAAoB,IAAI;AAAA,QACrE;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,OAAO,eAAe,YAAY;AAC3C,MAAAC,QAAO,iBAAiB,cAAc,YAAY;AAAA,IACpD;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,QAAI,OAAO,OAAO,eAAe,YAAY;AAC3C,MAAAA,QAAO,oBAAoB,cAAc,YAAY;AAAA,IACvD;AAAA,EACF;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,eAAe,SAAS;AACxC,WAAK;AAAA,IACP;AAAA,EACF,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,QAAI,OAAO,OAAO,eAAe,SAAS;AACxC,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,KAAG,4CAA4C,MAAM;AACnD,QAAI,aAAa;AACf,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,KAAG,eAAe,MAAM;AACtB,QAAI,eAAe,OAAO,OAAO,SAAS;AACxC,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH;;;ACjFe,SAAR,SAA0B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO,WAAW;AAAA,IAChB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AACA,eAAa;AAAA,IACX,UAAU;AAAA,MACR,SAAS;AAAA,MACT,OAAO;AAAA,MACP,mBAAmB;AAAA,MACnB,sBAAsB;AAAA,MACtB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,IACrB;AAAA,EACF,CAAC;AACD,MAAIC;AACJ,MAAI;AACJ,MAAI,qBAAqB,UAAU,OAAO,WAAW,OAAO,SAAS,QAAQ;AAC7E,MAAI,uBAAuB,UAAU,OAAO,WAAW,OAAO,SAAS,QAAQ;AAC/E,MAAI;AACJ,MAAI,qBAAoB,oBAAI,KAAK,GAAE;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,WAAS,gBAAgB,GAAG;AAC1B,QAAI,CAAC,UAAU,OAAO,aAAa,CAAC,OAAO;AAAW;AACtD,QAAI,EAAE,WAAW,OAAO;AAAW;AACnC,WAAO,UAAU,oBAAoB,iBAAiB,eAAe;AACrE,WAAO;AAAA,EACT;AACA,QAAM,eAAe,MAAM;AACzB,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS;AAAS;AAClD,QAAI,OAAO,SAAS,QAAQ;AAC1B,kBAAY;AAAA,IACd,WAAW,WAAW;AACpB,6BAAuB;AACvB,kBAAY;AAAA,IACd;AACA,UAAM,WAAW,OAAO,SAAS,SAAS,mBAAmB,oBAAoB,wBAAuB,oBAAI,KAAK,GAAE,QAAQ;AAC3H,WAAO,SAAS,WAAW;AAC3B,SAAK,oBAAoB,UAAU,WAAW,kBAAkB;AAChE,UAAM,sBAAsB,MAAM;AAChC,mBAAa;AAAA,IACf,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,MAAM;AAC1B,QAAI;AACJ,QAAI,OAAO,WAAW,OAAO,OAAO,QAAQ,SAAS;AACnD,sBAAgB,OAAO,OAAO,OAAO,aAAW,QAAQ,UAAU,SAAS,qBAAqB,CAAC,EAAE,CAAC;AAAA,IACtG,OAAO;AACL,sBAAgB,OAAO,OAAO,OAAO,WAAW;AAAA,IAClD;AACA,QAAI,CAAC;AAAe,aAAO;AAC3B,UAAM,oBAAoB,SAAS,cAAc,aAAa,sBAAsB,GAAG,EAAE;AACzF,WAAO;AAAA,EACT;AACA,QAAM,MAAM,gBAAc;AACxB,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS;AAAS;AAClD,yBAAqB,GAAG;AACxB,iBAAa;AACb,QAAI,QAAQ,OAAO,eAAe,cAAc,OAAO,OAAO,SAAS,QAAQ;AAC/E,yBAAqB,OAAO,OAAO,SAAS;AAC5C,2BAAuB,OAAO,OAAO,SAAS;AAC9C,UAAM,oBAAoB,cAAc;AACxC,QAAI,CAAC,OAAO,MAAM,iBAAiB,KAAK,oBAAoB,KAAK,OAAO,eAAe,aAAa;AAClG,cAAQ;AACR,2BAAqB;AACrB,6BAAuB;AAAA,IACzB;AACA,uBAAmB;AACnB,UAAM,QAAQ,OAAO,OAAO;AAC5B,UAAM,UAAU,MAAM;AACpB,UAAI,CAAC,UAAU,OAAO;AAAW;AACjC,UAAI,OAAO,OAAO,SAAS,kBAAkB;AAC3C,YAAI,CAAC,OAAO,eAAe,OAAO,OAAO,QAAQ,OAAO,OAAO,QAAQ;AACrE,iBAAO,UAAU,OAAO,MAAM,IAAI;AAClC,eAAK,UAAU;AAAA,QACjB,WAAW,CAAC,OAAO,OAAO,SAAS,iBAAiB;AAClD,iBAAO,QAAQ,OAAO,OAAO,SAAS,GAAG,OAAO,MAAM,IAAI;AAC1D,eAAK,UAAU;AAAA,QACjB;AAAA,MACF,OAAO;AACL,YAAI,CAAC,OAAO,SAAS,OAAO,OAAO,QAAQ,OAAO,OAAO,QAAQ;AAC/D,iBAAO,UAAU,OAAO,MAAM,IAAI;AAClC,eAAK,UAAU;AAAA,QACjB,WAAW,CAAC,OAAO,OAAO,SAAS,iBAAiB;AAClD,iBAAO,QAAQ,GAAG,OAAO,MAAM,IAAI;AACnC,eAAK,UAAU;AAAA,QACjB;AAAA,MACF;AACA,UAAI,OAAO,OAAO,SAAS;AACzB,6BAAoB,oBAAI,KAAK,GAAE,QAAQ;AACvC,8BAAsB,MAAM;AAC1B,cAAI;AAAA,QACN,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,QAAQ,GAAG;AACb,mBAAaA,QAAO;AACpB,MAAAA,WAAU,WAAW,MAAM;AACzB,gBAAQ;AAAA,MACV,GAAG,KAAK;AAAA,IACV,OAAO;AACL,4BAAsB,MAAM;AAC1B,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAGA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,MAAM;AAClB,WAAO,SAAS,UAAU;AAC1B,QAAI;AACJ,SAAK,eAAe;AAAA,EACtB;AACA,QAAM,OAAO,MAAM;AACjB,WAAO,SAAS,UAAU;AAC1B,iBAAaA,QAAO;AACpB,yBAAqB,GAAG;AACxB,SAAK,cAAc;AAAA,EACrB;AACA,QAAM,QAAQ,CAAC,UAAU,UAAU;AACjC,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS;AAAS;AAClD,iBAAaA,QAAO;AACpB,QAAI,CAAC,UAAU;AACb,4BAAsB;AAAA,IACxB;AACA,UAAM,UAAU,MAAM;AACpB,WAAK,eAAe;AACpB,UAAI,OAAO,OAAO,SAAS,mBAAmB;AAC5C,eAAO,UAAU,iBAAiB,iBAAiB,eAAe;AAAA,MACpE,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,SAAS,SAAS;AACzB,QAAI,OAAO;AACT,UAAI,cAAc;AAChB,2BAAmB,OAAO,OAAO,SAAS;AAAA,MAC5C;AACA,qBAAe;AACf,cAAQ;AACR;AAAA,IACF;AACA,UAAM,QAAQ,oBAAoB,OAAO,OAAO,SAAS;AACzD,uBAAmB,UAAS,oBAAI,KAAK,GAAE,QAAQ,IAAI;AACnD,QAAI,OAAO,SAAS,mBAAmB,KAAK,CAAC,OAAO,OAAO;AAAM;AACjE,QAAI,mBAAmB;AAAG,yBAAmB;AAC7C,YAAQ;AAAA,EACV;AACA,QAAM,SAAS,MAAM;AACnB,QAAI,OAAO,SAAS,mBAAmB,KAAK,CAAC,OAAO,OAAO,QAAQ,OAAO,aAAa,CAAC,OAAO,SAAS;AAAS;AACjH,yBAAoB,oBAAI,KAAK,GAAE,QAAQ;AACvC,QAAI,qBAAqB;AACvB,4BAAsB;AACtB,UAAI,gBAAgB;AAAA,IACtB,OAAO;AACL,UAAI;AAAA,IACN;AACA,WAAO,SAAS,SAAS;AACzB,SAAK,gBAAgB;AAAA,EACvB;AACA,QAAM,qBAAqB,MAAM;AAC/B,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS;AAAS;AAClD,UAAMC,YAAW,YAAY;AAC7B,QAAIA,UAAS,oBAAoB,UAAU;AACzC,4BAAsB;AACtB,YAAM,IAAI;AAAA,IACZ;AACA,QAAIA,UAAS,oBAAoB,WAAW;AAC1C,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,iBAAiB,OAAK;AAC1B,QAAI,EAAE,gBAAgB;AAAS;AAC/B,0BAAsB;AACtB,UAAM,IAAI;AAAA,EACZ;AACA,QAAM,iBAAiB,OAAK;AAC1B,QAAI,EAAE,gBAAgB;AAAS;AAC/B,QAAI,OAAO,SAAS,QAAQ;AAC1B,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,oBAAoB,MAAM;AAC9B,QAAI,OAAO,OAAO,SAAS,mBAAmB;AAC5C,aAAO,GAAG,iBAAiB,gBAAgB,cAAc;AACzD,aAAO,GAAG,iBAAiB,gBAAgB,cAAc;AAAA,IAC3D;AAAA,EACF;AACA,QAAM,oBAAoB,MAAM;AAC9B,WAAO,GAAG,oBAAoB,gBAAgB,cAAc;AAC5D,WAAO,GAAG,oBAAoB,gBAAgB,cAAc;AAAA,EAC9D;AACA,QAAM,uBAAuB,MAAM;AACjC,UAAMA,YAAW,YAAY;AAC7B,IAAAA,UAAS,iBAAiB,oBAAoB,kBAAkB;AAAA,EAClE;AACA,QAAM,uBAAuB,MAAM;AACjC,UAAMA,YAAW,YAAY;AAC7B,IAAAA,UAAS,oBAAoB,oBAAoB,kBAAkB;AAAA,EACrE;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,SAAS,SAAS;AAClC,wBAAkB;AAClB,2BAAqB;AACrB,2BAAoB,oBAAI,KAAK,GAAE,QAAQ;AACvC,YAAM;AAAA,IACR;AAAA,EACF,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,sBAAkB;AAClB,yBAAqB;AACrB,QAAI,OAAO,SAAS,SAAS;AAC3B,WAAK;AAAA,IACP;AAAA,EACF,CAAC;AACD,KAAG,yBAAyB,CAAC,IAAI,OAAO,aAAa;AACnD,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS;AAAS;AAClD,QAAI,YAAY,CAAC,OAAO,OAAO,SAAS,sBAAsB;AAC5D,YAAM,MAAM,IAAI;AAAA,IAClB,OAAO;AACL,WAAK;AAAA,IACP;AAAA,EACF,CAAC;AACD,KAAG,mBAAmB,MAAM;AAC1B,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS;AAAS;AAClD,QAAI,OAAO,OAAO,SAAS,sBAAsB;AAC/C,WAAK;AACL;AAAA,IACF;AACA,gBAAY;AACZ,oBAAgB;AAChB,0BAAsB;AACtB,wBAAoB,WAAW,MAAM;AACnC,4BAAsB;AACtB,sBAAgB;AAChB,YAAM,IAAI;AAAA,IACZ,GAAG,GAAG;AAAA,EACR,CAAC;AACD,KAAG,YAAY,MAAM;AACnB,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS,WAAW,CAAC;AAAW;AAChE,iBAAa,iBAAiB;AAC9B,iBAAaD,QAAO;AACpB,QAAI,OAAO,OAAO,SAAS,sBAAsB;AAC/C,sBAAgB;AAChB,kBAAY;AACZ;AAAA,IACF;AACA,QAAI,iBAAiB,OAAO,OAAO;AAAS,aAAO;AACnD,oBAAgB;AAChB,gBAAY;AAAA,EACd,CAAC;AACD,KAAG,eAAe,MAAM;AACtB,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS;AAAS;AAClD,mBAAe;AAAA,EACjB,CAAC;AACD,SAAO,OAAO,OAAO,UAAU;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;ACpRe,SAAR,MAAuB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,sBAAsB;AAAA,MACtB,kBAAkB;AAAA,MAClB,uBAAuB;AAAA,MACvB,sBAAsB;AAAA,IACxB;AAAA,EACF,CAAC;AACD,MAAI,cAAc;AAClB,MAAI,gBAAgB;AACpB,SAAO,SAAS;AAAA,IACd,QAAQ;AAAA,EACV;AACA,WAAS,eAAe;AACtB,UAAM,eAAe,OAAO,OAAO;AACnC,QAAI,CAAC,gBAAgB,aAAa;AAAW;AAC7C,UAAM,eAAe,aAAa;AAClC,UAAM,eAAe,aAAa;AAClC,QAAI,gBAAgB,aAAa,UAAU,SAAS,OAAO,OAAO,OAAO,qBAAqB;AAAG;AACjG,QAAI,OAAO,iBAAiB,eAAe,iBAAiB;AAAM;AAClE,QAAI;AACJ,QAAI,aAAa,OAAO,MAAM;AAC5B,qBAAe,SAAS,aAAa,aAAa,aAAa,yBAAyB,GAAG,EAAE;AAAA,IAC/F,OAAO;AACL,qBAAe;AAAA,IACjB;AACA,QAAI,OAAO,OAAO,MAAM;AACtB,aAAO,YAAY,YAAY;AAAA,IACjC,OAAO;AACL,aAAO,QAAQ,YAAY;AAAA,IAC7B;AAAA,EACF;AACA,WAAS,OAAO;AACd,UAAM;AAAA,MACJ,QAAQ;AAAA,IACV,IAAI,OAAO;AACX,QAAI;AAAa,aAAO;AACxB,kBAAc;AACd,UAAM,cAAc,OAAO;AAC3B,QAAI,aAAa,kBAAkB,aAAa;AAC9C,aAAO,OAAO,SAAS,aAAa;AACpC,aAAO,OAAO,OAAO,OAAO,OAAO,gBAAgB;AAAA,QACjD,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,MACvB,CAAC;AACD,aAAO,OAAO,OAAO,OAAO,OAAO,QAAQ;AAAA,QACzC,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,MACvB,CAAC;AACD,aAAO,OAAO,OAAO,OAAO;AAAA,IAC9B,WAAWE,UAAS,aAAa,MAAM,GAAG;AACxC,YAAM,qBAAqB,OAAO,OAAO,CAAC,GAAG,aAAa,MAAM;AAChE,aAAO,OAAO,oBAAoB;AAAA,QAChC,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,MACvB,CAAC;AACD,aAAO,OAAO,SAAS,IAAI,YAAY,kBAAkB;AACzD,sBAAgB;AAAA,IAClB;AACA,WAAO,OAAO,OAAO,GAAG,UAAU,IAAI,OAAO,OAAO,OAAO,oBAAoB;AAC/E,WAAO,OAAO,OAAO,GAAG,OAAO,YAAY;AAC3C,WAAO;AAAA,EACT;AACA,WAAS,OAAO,SAAS;AACvB,UAAM,eAAe,OAAO,OAAO;AACnC,QAAI,CAAC,gBAAgB,aAAa;AAAW;AAC7C,UAAM,gBAAgB,aAAa,OAAO,kBAAkB,SAAS,aAAa,qBAAqB,IAAI,aAAa,OAAO;AAG/H,QAAI,mBAAmB;AACvB,UAAM,mBAAmB,OAAO,OAAO,OAAO;AAC9C,QAAI,OAAO,OAAO,gBAAgB,KAAK,CAAC,OAAO,OAAO,gBAAgB;AACpE,yBAAmB,OAAO,OAAO;AAAA,IACnC;AACA,QAAI,CAAC,OAAO,OAAO,OAAO,sBAAsB;AAC9C,yBAAmB;AAAA,IACrB;AACA,uBAAmB,KAAK,MAAM,gBAAgB;AAC9C,iBAAa,OAAO,QAAQ,aAAW,QAAQ,UAAU,OAAO,gBAAgB,CAAC;AACjF,QAAI,aAAa,OAAO,QAAQ,aAAa,OAAO,WAAW,aAAa,OAAO,QAAQ,SAAS;AAClG,eAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK,GAAG;AAC5C,wBAAgB,aAAa,UAAU,6BAA6B,OAAO,YAAY,KAAK,EAAE,QAAQ,aAAW;AAC/G,kBAAQ,UAAU,IAAI,gBAAgB;AAAA,QACxC,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,eAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK,GAAG;AAC5C,YAAI,aAAa,OAAO,OAAO,YAAY,CAAC,GAAG;AAC7C,uBAAa,OAAO,OAAO,YAAY,CAAC,EAAE,UAAU,IAAI,gBAAgB;AAAA,QAC1E;AAAA,MACF;AAAA,IACF;AACA,UAAM,mBAAmB,OAAO,OAAO,OAAO;AAC9C,UAAM,YAAY,oBAAoB,CAAC,aAAa,OAAO;AAC3D,QAAI,OAAO,cAAc,aAAa,aAAa,WAAW;AAC5D,YAAM,qBAAqB,aAAa;AACxC,UAAI;AACJ,UAAI;AACJ,UAAI,aAAa,OAAO,MAAM;AAC5B,cAAM,iBAAiB,aAAa,OAAO,OAAO,aAAW,QAAQ,aAAa,yBAAyB,MAAM,GAAG,OAAO,WAAW,EAAE,CAAC;AACzI,yBAAiB,aAAa,OAAO,QAAQ,cAAc;AAC3D,oBAAY,OAAO,cAAc,OAAO,gBAAgB,SAAS;AAAA,MACnE,OAAO;AACL,yBAAiB,OAAO;AACxB,oBAAY,iBAAiB,OAAO,gBAAgB,SAAS;AAAA,MAC/D;AACA,UAAI,WAAW;AACb,0BAAkB,cAAc,SAAS,mBAAmB,KAAK;AAAA,MACnE;AACA,UAAI,aAAa,wBAAwB,aAAa,qBAAqB,QAAQ,cAAc,IAAI,GAAG;AACtG,YAAI,aAAa,OAAO,gBAAgB;AACtC,cAAI,iBAAiB,oBAAoB;AACvC,6BAAiB,iBAAiB,KAAK,MAAM,gBAAgB,CAAC,IAAI;AAAA,UACpE,OAAO;AACL,6BAAiB,iBAAiB,KAAK,MAAM,gBAAgB,CAAC,IAAI;AAAA,UACpE;AAAA,QACF,WAAW,iBAAiB,sBAAsB,aAAa,OAAO,mBAAmB,GAAG;AAAA,QAE5F;AACA,qBAAa,QAAQ,gBAAgB,UAAU,IAAI,MAAS;AAAA,MAC9D;AAAA,IACF;AAAA,EACF;AACA,KAAG,cAAc,MAAM;AACrB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO;AACX,QAAI,CAAC,UAAU,CAAC,OAAO;AAAQ;AAC/B,QAAI,OAAO,OAAO,WAAW,YAAY,OAAO,kBAAkB,aAAa;AAC7E,YAAMC,YAAW,YAAY;AAC7B,YAAM,0BAA0B,MAAM;AACpC,cAAM,gBAAgB,OAAO,OAAO,WAAW,WAAWA,UAAS,cAAc,OAAO,MAAM,IAAI,OAAO;AACzG,YAAI,iBAAiB,cAAc,QAAQ;AACzC,iBAAO,SAAS,cAAc;AAC9B,eAAK;AACL,iBAAO,IAAI;AAAA,QACb,WAAW,eAAe;AACxB,gBAAM,iBAAiB,OAAK;AAC1B,mBAAO,SAAS,EAAE,OAAO,CAAC;AAC1B,0BAAc,oBAAoB,QAAQ,cAAc;AACxD,iBAAK;AACL,mBAAO,IAAI;AACX,mBAAO,OAAO,OAAO;AACrB,mBAAO,OAAO;AAAA,UAChB;AACA,wBAAc,iBAAiB,QAAQ,cAAc;AAAA,QACvD;AACA,eAAO;AAAA,MACT;AACA,YAAM,yBAAyB,MAAM;AACnC,YAAI,OAAO;AAAW;AACtB,cAAM,gBAAgB,wBAAwB;AAC9C,YAAI,CAAC,eAAe;AAClB,gCAAsB,sBAAsB;AAAA,QAC9C;AAAA,MACF;AACA,4BAAsB,sBAAsB;AAAA,IAC9C,OAAO;AACL,WAAK;AACL,aAAO,IAAI;AAAA,IACb;AAAA,EACF,CAAC;AACD,KAAG,4CAA4C,MAAM;AACnD,WAAO;AAAA,EACT,CAAC;AACD,KAAG,iBAAiB,CAAC,IAAI,aAAa;AACpC,UAAM,eAAe,OAAO,OAAO;AACnC,QAAI,CAAC,gBAAgB,aAAa;AAAW;AAC7C,iBAAa,cAAc,QAAQ;AAAA,EACrC,CAAC;AACD,KAAG,iBAAiB,MAAM;AACxB,UAAM,eAAe,OAAO,OAAO;AACnC,QAAI,CAAC,gBAAgB,aAAa;AAAW;AAC7C,QAAI,eAAe;AACjB,mBAAa,QAAQ;AAAA,IACvB;AAAA,EACF,CAAC;AACD,SAAO,OAAO,OAAO,QAAQ;AAAA,IAC3B;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;AC5Le,SAAR,SAA0B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,UAAU;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,uBAAuB;AAAA,MACvB,QAAQ;AAAA,MACR,iBAAiB;AAAA,IACnB;AAAA,EACF,CAAC;AACD,WAASC,gBAAe;AACtB,UAAM,YAAY,OAAO,aAAa;AACtC,WAAO,aAAa,SAAS;AAC7B,WAAO,cAAc,CAAC;AACtB,WAAO,gBAAgB,WAAW,SAAS;AAC3C,WAAO,SAAS,WAAW;AAAA,MACzB,YAAY,OAAO,MAAM,OAAO,YAAY,CAAC,OAAO;AAAA,IACtD,CAAC;AAAA,EACH;AACA,WAASC,eAAc;AACrB,UAAM;AAAA,MACJ,iBAAiB;AAAA,MACjB;AAAA,IACF,IAAI;AAEJ,QAAI,KAAK,WAAW,WAAW,GAAG;AAChC,WAAK,WAAW,KAAK;AAAA,QACnB,UAAU,QAAQ,OAAO,aAAa,IAAI,WAAW,QAAQ;AAAA,QAC7D,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH;AACA,SAAK,WAAW,KAAK;AAAA,MACnB,UAAU,QAAQ,OAAO,aAAa,IAAI,aAAa,UAAU;AAAA,MACjE,MAAM,IAAI;AAAA,IACZ,CAAC;AAAA,EACH;AACA,WAASC,YAAW;AAAA,IAClB;AAAA,EACF,GAAG;AACD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA,iBAAiB;AAAA,IACnB,IAAI;AAEJ,UAAM,eAAe,IAAI;AACzB,UAAM,WAAW,eAAe,KAAK;AACrC,QAAI,aAAa,CAAC,OAAO,aAAa,GAAG;AACvC,aAAO,QAAQ,OAAO,WAAW;AACjC;AAAA,IACF;AACA,QAAI,aAAa,CAAC,OAAO,aAAa,GAAG;AACvC,UAAI,OAAO,OAAO,SAAS,SAAS,QAAQ;AAC1C,eAAO,QAAQ,SAAS,SAAS,CAAC;AAAA,MACpC,OAAO;AACL,eAAO,QAAQ,OAAO,OAAO,SAAS,CAAC;AAAA,MACzC;AACA;AAAA,IACF;AACA,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI,KAAK,WAAW,SAAS,GAAG;AAC9B,cAAM,gBAAgB,KAAK,WAAW,IAAI;AAC1C,cAAM,gBAAgB,KAAK,WAAW,IAAI;AAC1C,cAAM,WAAW,cAAc,WAAW,cAAc;AACxD,cAAM,OAAO,cAAc,OAAO,cAAc;AAChD,eAAO,WAAW,WAAW;AAC7B,eAAO,YAAY;AACnB,YAAI,KAAK,IAAI,OAAO,QAAQ,IAAI,OAAO,SAAS,iBAAiB;AAC/D,iBAAO,WAAW;AAAA,QACpB;AAGA,YAAI,OAAO,OAAO,IAAI,IAAI,cAAc,OAAO,KAAK;AAClD,iBAAO,WAAW;AAAA,QACpB;AAAA,MACF,OAAO;AACL,eAAO,WAAW;AAAA,MACpB;AACA,aAAO,YAAY,OAAO,SAAS;AACnC,WAAK,WAAW,SAAS;AACzB,UAAI,mBAAmB,MAAO,OAAO,SAAS;AAC9C,YAAM,mBAAmB,OAAO,WAAW;AAC3C,UAAI,cAAc,OAAO,YAAY;AACrC,UAAI;AAAK,sBAAc,CAAC;AACxB,UAAI,WAAW;AACf,UAAI;AACJ,YAAM,eAAe,KAAK,IAAI,OAAO,QAAQ,IAAI,KAAK,OAAO,SAAS;AACtE,UAAI;AACJ,UAAI,cAAc,OAAO,aAAa,GAAG;AACvC,YAAI,OAAO,SAAS,gBAAgB;AAClC,cAAI,cAAc,OAAO,aAAa,IAAI,CAAC,cAAc;AACvD,0BAAc,OAAO,aAAa,IAAI;AAAA,UACxC;AACA,gCAAsB,OAAO,aAAa;AAC1C,qBAAW;AACX,eAAK,sBAAsB;AAAA,QAC7B,OAAO;AACL,wBAAc,OAAO,aAAa;AAAA,QACpC;AACA,YAAI,OAAO,QAAQ,OAAO;AAAgB,yBAAe;AAAA,MAC3D,WAAW,cAAc,OAAO,aAAa,GAAG;AAC9C,YAAI,OAAO,SAAS,gBAAgB;AAClC,cAAI,cAAc,OAAO,aAAa,IAAI,cAAc;AACtD,0BAAc,OAAO,aAAa,IAAI;AAAA,UACxC;AACA,gCAAsB,OAAO,aAAa;AAC1C,qBAAW;AACX,eAAK,sBAAsB;AAAA,QAC7B,OAAO;AACL,wBAAc,OAAO,aAAa;AAAA,QACpC;AACA,YAAI,OAAO,QAAQ,OAAO;AAAgB,yBAAe;AAAA,MAC3D,WAAW,OAAO,SAAS,QAAQ;AACjC,YAAI;AACJ,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,cAAI,SAAS,CAAC,IAAI,CAAC,aAAa;AAC9B,wBAAY;AACZ;AAAA,UACF;AAAA,QACF;AACA,YAAI,KAAK,IAAI,SAAS,SAAS,IAAI,WAAW,IAAI,KAAK,IAAI,SAAS,YAAY,CAAC,IAAI,WAAW,KAAK,OAAO,mBAAmB,QAAQ;AACrI,wBAAc,SAAS,SAAS;AAAA,QAClC,OAAO;AACL,wBAAc,SAAS,YAAY,CAAC;AAAA,QACtC;AACA,sBAAc,CAAC;AAAA,MACjB;AACA,UAAI,cAAc;AAChB,aAAK,iBAAiB,MAAM;AAC1B,iBAAO,QAAQ;AAAA,QACjB,CAAC;AAAA,MACH;AAEA,UAAI,OAAO,aAAa,GAAG;AACzB,YAAI,KAAK;AACP,6BAAmB,KAAK,KAAK,CAAC,cAAc,OAAO,aAAa,OAAO,QAAQ;AAAA,QACjF,OAAO;AACL,6BAAmB,KAAK,KAAK,cAAc,OAAO,aAAa,OAAO,QAAQ;AAAA,QAChF;AACA,YAAI,OAAO,SAAS,QAAQ;AAQ1B,gBAAM,eAAe,KAAK,KAAK,MAAM,CAAC,cAAc,eAAe,OAAO,SAAS;AACnF,gBAAM,mBAAmB,OAAO,gBAAgB,OAAO,WAAW;AAClE,cAAI,eAAe,kBAAkB;AACnC,+BAAmB,OAAO;AAAA,UAC5B,WAAW,eAAe,IAAI,kBAAkB;AAC9C,+BAAmB,OAAO,QAAQ;AAAA,UACpC,OAAO;AACL,+BAAmB,OAAO,QAAQ;AAAA,UACpC;AAAA,QACF;AAAA,MACF,WAAW,OAAO,SAAS,QAAQ;AACjC,eAAO,eAAe;AACtB;AAAA,MACF;AACA,UAAI,OAAO,SAAS,kBAAkB,UAAU;AAC9C,eAAO,eAAe,mBAAmB;AACzC,eAAO,cAAc,gBAAgB;AACrC,eAAO,aAAa,WAAW;AAC/B,eAAO,gBAAgB,MAAM,OAAO,cAAc;AAClD,eAAO,YAAY;AACnB,6BAAqB,WAAW,MAAM;AACpC,cAAI,CAAC,UAAU,OAAO,aAAa,CAAC,KAAK;AAAqB;AAC9D,eAAK,gBAAgB;AACrB,iBAAO,cAAc,OAAO,KAAK;AACjC,qBAAW,MAAM;AACf,mBAAO,aAAa,mBAAmB;AACvC,iCAAqB,WAAW,MAAM;AACpC,kBAAI,CAAC,UAAU,OAAO;AAAW;AACjC,qBAAO,cAAc;AAAA,YACvB,CAAC;AAAA,UACH,GAAG,CAAC;AAAA,QACN,CAAC;AAAA,MACH,WAAW,OAAO,UAAU;AAC1B,aAAK,4BAA4B;AACjC,eAAO,eAAe,WAAW;AACjC,eAAO,cAAc,gBAAgB;AACrC,eAAO,aAAa,WAAW;AAC/B,eAAO,gBAAgB,MAAM,OAAO,cAAc;AAClD,YAAI,CAAC,OAAO,WAAW;AACrB,iBAAO,YAAY;AACnB,+BAAqB,WAAW,MAAM;AACpC,gBAAI,CAAC,UAAU,OAAO;AAAW;AACjC,mBAAO,cAAc;AAAA,UACvB,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,eAAO,eAAe,WAAW;AAAA,MACnC;AACA,aAAO,kBAAkB;AACzB,aAAO,oBAAoB;AAAA,IAC7B,WAAW,OAAO,SAAS,QAAQ;AACjC,aAAO,eAAe;AACtB;AAAA,IACF,WAAW,OAAO,UAAU;AAC1B,WAAK,4BAA4B;AAAA,IACnC;AACA,QAAI,CAAC,OAAO,SAAS,YAAY,YAAY,OAAO,cAAc;AAChE,aAAO,eAAe;AACtB,aAAO,kBAAkB;AACzB,aAAO,oBAAoB;AAAA,IAC7B;AAAA,EACF;AACA,SAAO,OAAO,QAAQ;AAAA,IACpB,UAAU;AAAA,MACR,cAAAF;AAAA,MACA,aAAAC;AAAA,MACA,YAAAC;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;ACnOe,SAAR,KAAsB;AAAA,EAC3B;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,EACF,CAAC;AACD,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,aAAa,kBAAgB;AACjC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO;AACX,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO,OAAO;AAClB,mBAAe,yBAAyB;AACxC,qBAAiB,KAAK,MAAM,eAAe,IAAI;AAC/C,QAAI,KAAK,MAAM,eAAe,IAAI,MAAM,eAAe,MAAM;AAC3D,+BAAyB;AAAA,IAC3B,OAAO;AACL,+BAAyB,KAAK,KAAK,eAAe,IAAI,IAAI;AAAA,IAC5D;AACA,QAAI,kBAAkB,UAAU,SAAS,OAAO;AAC9C,+BAAyB,KAAK,IAAI,wBAAwB,gBAAgB,IAAI;AAAA,IAChF;AAAA,EACF;AACA,QAAM,cAAc,CAAC,GAAG,OAAO,cAAc,sBAAsB;AACjE,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO,OAAO;AAElB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS,SAAS,iBAAiB,GAAG;AACxC,YAAM,aAAa,KAAK,MAAM,KAAK,iBAAiB,KAAK;AACzD,YAAM,oBAAoB,IAAI,OAAO,iBAAiB;AACtD,YAAM,iBAAiB,eAAe,IAAI,iBAAiB,KAAK,IAAI,KAAK,MAAM,eAAe,aAAa,OAAO,kBAAkB,IAAI,GAAG,cAAc;AACzJ,YAAM,KAAK,MAAM,oBAAoB,cAAc;AACnD,eAAS,oBAAoB,MAAM,iBAAiB,aAAa;AACjE,2BAAqB,SAAS,MAAM,yBAAyB;AAC7D,YAAM,MAAM,QAAQ;AAAA,IACtB,WAAW,SAAS,UAAU;AAC5B,eAAS,KAAK,MAAM,IAAI,IAAI;AAC5B,YAAM,IAAI,SAAS;AACnB,UAAI,SAAS,kBAAkB,WAAW,kBAAkB,QAAQ,OAAO,GAAG;AAC5E,eAAO;AACP,YAAI,OAAO,MAAM;AACf,gBAAM;AACN,oBAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,KAAK,MAAM,IAAI,YAAY;AACjC,eAAS,IAAI,MAAM;AAAA,IACrB;AACA,UAAM,MAAM,kBAAkB,YAAY,CAAC,IAAI,QAAQ,IAAI,gBAAgB,GAAG,mBAAmB;AAAA,EACnG;AACA,QAAM,oBAAoB,CAAC,WAAW,UAAU,sBAAsB;AACpE,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO,OAAO;AAClB,WAAO,eAAe,YAAY,gBAAgB;AAClD,WAAO,cAAc,KAAK,KAAK,OAAO,cAAc,IAAI,IAAI;AAC5D,WAAO,UAAU,MAAM,kBAAkB,OAAO,CAAC,IAAI,GAAG,OAAO,cAAc;AAC7E,QAAI,gBAAgB;AAClB,YAAM,gBAAgB,CAAC;AACvB,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,YAAI,iBAAiB,SAAS,CAAC;AAC/B,YAAI;AAAc,2BAAiB,KAAK,MAAM,cAAc;AAC5D,YAAI,SAAS,CAAC,IAAI,OAAO,cAAc,SAAS,CAAC;AAAG,wBAAc,KAAK,cAAc;AAAA,MACvF;AACA,eAAS,OAAO,GAAG,SAAS,MAAM;AAClC,eAAS,KAAK,GAAG,aAAa;AAAA,IAChC;AAAA,EACF;AACA,SAAO,OAAO;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACjGe,SAAR,YAA6B,QAAQ;AAC1C,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,OAAO,MAAM;AACf,WAAO,YAAY;AAAA,EACrB;AACA,QAAM,gBAAgB,aAAW;AAC/B,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,cAAQ,YAAY;AACpB,eAAS,OAAO,QAAQ,SAAS,CAAC,CAAC;AACnC,cAAQ,YAAY;AAAA,IACtB,OAAO;AACL,eAAS,OAAO,OAAO;AAAA,IACzB;AAAA,EACF;AACA,MAAI,OAAO,WAAW,YAAY,YAAY,QAAQ;AACpD,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,UAAI,OAAO,CAAC;AAAG,sBAAc,OAAO,CAAC,CAAC;AAAA,IACxC;AAAA,EACF,OAAO;AACL,kBAAc,MAAM;AAAA,EACtB;AACA,SAAO,aAAa;AACpB,MAAI,OAAO,MAAM;AACf,WAAO,WAAW;AAAA,EACpB;AACA,MAAI,CAAC,OAAO,YAAY,OAAO,WAAW;AACxC,WAAO,OAAO;AAAA,EAChB;AACF;;;ACjCe,SAAR,aAA8B,QAAQ;AAC3C,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,OAAO,MAAM;AACf,WAAO,YAAY;AAAA,EACrB;AACA,MAAI,iBAAiB,cAAc;AACnC,QAAM,iBAAiB,aAAW;AAChC,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,cAAQ,YAAY;AACpB,eAAS,QAAQ,QAAQ,SAAS,CAAC,CAAC;AACpC,cAAQ,YAAY;AAAA,IACtB,OAAO;AACL,eAAS,QAAQ,OAAO;AAAA,IAC1B;AAAA,EACF;AACA,MAAI,OAAO,WAAW,YAAY,YAAY,QAAQ;AACpD,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,UAAI,OAAO,CAAC;AAAG,uBAAe,OAAO,CAAC,CAAC;AAAA,IACzC;AACA,qBAAiB,cAAc,OAAO;AAAA,EACxC,OAAO;AACL,mBAAe,MAAM;AAAA,EACvB;AACA,SAAO,aAAa;AACpB,MAAI,OAAO,MAAM;AACf,WAAO,WAAW;AAAA,EACpB;AACA,MAAI,CAAC,OAAO,YAAY,OAAO,WAAW;AACxC,WAAO,OAAO;AAAA,EAChB;AACA,SAAO,QAAQ,gBAAgB,GAAG,KAAK;AACzC;;;ACrCe,SAAR,SAA0B,OAAO,QAAQ;AAC9C,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,oBAAoB;AACxB,MAAI,OAAO,MAAM;AACf,yBAAqB,OAAO;AAC5B,WAAO,YAAY;AACnB,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,aAAa,OAAO,OAAO;AACjC,MAAI,SAAS,GAAG;AACd,WAAO,aAAa,MAAM;AAC1B;AAAA,EACF;AACA,MAAI,SAAS,YAAY;AACvB,WAAO,YAAY,MAAM;AACzB;AAAA,EACF;AACA,MAAI,iBAAiB,oBAAoB,QAAQ,oBAAoB,IAAI;AACzE,QAAM,eAAe,CAAC;AACtB,WAAS,IAAI,aAAa,GAAG,KAAK,OAAO,KAAK,GAAG;AAC/C,UAAM,eAAe,OAAO,OAAO,CAAC;AACpC,iBAAa,OAAO;AACpB,iBAAa,QAAQ,YAAY;AAAA,EACnC;AACA,MAAI,OAAO,WAAW,YAAY,YAAY,QAAQ;AACpD,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,UAAI,OAAO,CAAC;AAAG,iBAAS,OAAO,OAAO,CAAC,CAAC;AAAA,IAC1C;AACA,qBAAiB,oBAAoB,QAAQ,oBAAoB,OAAO,SAAS;AAAA,EACnF,OAAO;AACL,aAAS,OAAO,MAAM;AAAA,EACxB;AACA,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK,GAAG;AAC/C,aAAS,OAAO,aAAa,CAAC,CAAC;AAAA,EACjC;AACA,SAAO,aAAa;AACpB,MAAI,OAAO,MAAM;AACf,WAAO,WAAW;AAAA,EACpB;AACA,MAAI,CAAC,OAAO,YAAY,OAAO,WAAW;AACxC,WAAO,OAAO;AAAA,EAChB;AACA,MAAI,OAAO,MAAM;AACf,WAAO,QAAQ,iBAAiB,OAAO,cAAc,GAAG,KAAK;AAAA,EAC/D,OAAO;AACL,WAAO,QAAQ,gBAAgB,GAAG,KAAK;AAAA,EACzC;AACF;;;ACpDe,SAAR,YAA6B,eAAe;AACjD,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,oBAAoB;AACxB,MAAI,OAAO,MAAM;AACf,yBAAqB,OAAO;AAC5B,WAAO,YAAY;AAAA,EACrB;AACA,MAAI,iBAAiB;AACrB,MAAI;AACJ,MAAI,OAAO,kBAAkB,YAAY,YAAY,eAAe;AAClE,aAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK,GAAG;AAChD,sBAAgB,cAAc,CAAC;AAC/B,UAAI,OAAO,OAAO,aAAa;AAAG,eAAO,OAAO,aAAa,EAAE,OAAO;AACtE,UAAI,gBAAgB;AAAgB,0BAAkB;AAAA,IACxD;AACA,qBAAiB,KAAK,IAAI,gBAAgB,CAAC;AAAA,EAC7C,OAAO;AACL,oBAAgB;AAChB,QAAI,OAAO,OAAO,aAAa;AAAG,aAAO,OAAO,aAAa,EAAE,OAAO;AACtE,QAAI,gBAAgB;AAAgB,wBAAkB;AACtD,qBAAiB,KAAK,IAAI,gBAAgB,CAAC;AAAA,EAC7C;AACA,SAAO,aAAa;AACpB,MAAI,OAAO,MAAM;AACf,WAAO,WAAW;AAAA,EACpB;AACA,MAAI,CAAC,OAAO,YAAY,OAAO,WAAW;AACxC,WAAO,OAAO;AAAA,EAChB;AACA,MAAI,OAAO,MAAM;AACf,WAAO,QAAQ,iBAAiB,OAAO,cAAc,GAAG,KAAK;AAAA,EAC/D,OAAO;AACL,WAAO,QAAQ,gBAAgB,GAAG,KAAK;AAAA,EACzC;AACF;;;ACtCe,SAAR,kBAAmC;AACxC,QAAM,SAAS;AACf,QAAM,gBAAgB,CAAC;AACvB,WAAS,IAAI,GAAG,IAAI,OAAO,OAAO,QAAQ,KAAK,GAAG;AAChD,kBAAc,KAAK,CAAC;AAAA,EACtB;AACA,SAAO,YAAY,aAAa;AAClC;;;ACFe,SAAR,aAA8B;AAAA,EACnC;AACF,GAAG;AACD,SAAO,OAAO,QAAQ;AAAA,IACpB,aAAa,YAAY,KAAK,MAAM;AAAA,IACpC,cAAc,aAAa,KAAK,MAAM;AAAA,IACtC,UAAU,SAAS,KAAK,MAAM;AAAA,IAC9B,aAAa,YAAY,KAAK,MAAM;AAAA,IACpC,iBAAiB,gBAAgB,KAAK,MAAM;AAAA,EAC9C,CAAC;AACH;;;ACfe,SAAR,WAA4B,QAAQ;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAAC;AAAA,IACA,eAAAC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,KAAG,cAAc,MAAM;AACrB,QAAI,OAAO,OAAO,WAAW;AAAQ;AACrC,WAAO,WAAW,KAAK,GAAG,OAAO,OAAO,yBAAyB,QAAQ;AACzE,QAAI,eAAe,YAAY,GAAG;AAChC,aAAO,WAAW,KAAK,GAAG,OAAO,OAAO,0BAA0B;AAAA,IACpE;AACA,UAAM,wBAAwB,kBAAkB,gBAAgB,IAAI,CAAC;AACrE,WAAO,OAAO,OAAO,QAAQ,qBAAqB;AAClD,WAAO,OAAO,OAAO,gBAAgB,qBAAqB;AAAA,EAC5D,CAAC;AACD,KAAG,gBAAgB,MAAM;AACvB,QAAI,OAAO,OAAO,WAAW;AAAQ;AACrC,IAAAD,cAAa;AAAA,EACf,CAAC;AACD,KAAG,iBAAiB,CAAC,IAAI,aAAa;AACpC,QAAI,OAAO,OAAO,WAAW;AAAQ;AACrC,IAAAC,eAAc,QAAQ;AAAA,EACxB,CAAC;AACD,KAAG,iBAAiB,MAAM;AACxB,QAAI,OAAO,OAAO,WAAW;AAAQ;AACrC,QAAI,iBAAiB;AACnB,UAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE;AAAc;AAEzD,aAAO,OAAO,QAAQ,aAAW;AAC/B,gBAAQ,iBAAiB,8GAA8G,EAAE,QAAQ,cAAY,SAAS,OAAO,CAAC;AAAA,MAChL,CAAC;AAED,sBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,MAAI;AACJ,KAAG,iBAAiB,MAAM;AACxB,QAAI,OAAO,OAAO,WAAW;AAAQ;AACrC,QAAI,CAAC,OAAO,OAAO,QAAQ;AACzB,+BAAyB;AAAA,IAC3B;AACA,0BAAsB,MAAM;AAC1B,UAAI,0BAA0B,OAAO,UAAU,OAAO,OAAO,QAAQ;AACnE,QAAAD,cAAa;AACb,iCAAyB;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;;;ACtDe,SAAR,aAA8B,cAAc,SAAS;AAC1D,QAAM,cAAc,oBAAoB,OAAO;AAC/C,MAAI,gBAAgB,SAAS;AAC3B,gBAAY,MAAM,qBAAqB;AACvC,gBAAY,MAAM,6BAA6B,IAAI;AAAA,EACrD;AACA,SAAO;AACT;;;ACPe,SAAR,2BAA4C;AAAA,EACjD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,QAAM;AACrB,QAAI,CAAC,GAAG,eAAe;AAErB,YAAM,QAAQ,OAAO,OAAO,OAAO,aAAW,QAAQ,YAAY,QAAQ,aAAa,GAAG,UAAU,EAAE,CAAC;AACvG,aAAO;AAAA,IACT;AACA,WAAO,GAAG;AAAA,EACZ;AACA,MAAI,OAAO,OAAO,oBAAoB,aAAa,GAAG;AACpD,QAAI,iBAAiB;AACrB,QAAI;AACJ,QAAI,WAAW;AACb,4BAAsB;AAAA,IACxB,OAAO;AACL,4BAAsB,kBAAkB,OAAO,iBAAe;AAC5D,cAAM,KAAK,YAAY,UAAU,SAAS,wBAAwB,IAAI,SAAS,WAAW,IAAI;AAC9F,eAAO,OAAO,cAAc,EAAE,MAAM;AAAA,MACtC,CAAC;AAAA,IACH;AACA,wBAAoB,QAAQ,QAAM;AAChC,2BAAqB,IAAI,MAAM;AAC7B,YAAI;AAAgB;AACpB,YAAI,CAAC,UAAU,OAAO;AAAW;AACjC,yBAAiB;AACjB,eAAO,YAAY;AACnB,cAAM,MAAM,IAAI,OAAO,YAAY,iBAAiB;AAAA,UAClD,SAAS;AAAA,UACT,YAAY;AAAA,QACd,CAAC;AACD,eAAO,UAAU,cAAc,GAAG;AAAA,MACpC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;;;ACvCe,SAAR,WAA4B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,YAAY;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF,CAAC;AACD,QAAME,gBAAe,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,OAAO,OAAO;AAC7B,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,YAAM,UAAU,OAAO,OAAO,CAAC;AAC/B,YAAM,SAAS,QAAQ;AACvB,UAAI,KAAK,CAAC;AACV,UAAI,CAAC,OAAO,OAAO;AAAkB,cAAM,OAAO;AAClD,UAAI,KAAK;AACT,UAAI,CAAC,OAAO,aAAa,GAAG;AAC1B,aAAK;AACL,aAAK;AAAA,MACP;AACA,YAAM,eAAe,OAAO,OAAO,WAAW,YAAY,KAAK,IAAI,IAAI,KAAK,IAAI,QAAQ,QAAQ,GAAG,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,EAAE,GAAG,CAAC;AACtJ,YAAM,WAAW,aAAa,QAAQ,OAAO;AAC7C,eAAS,MAAM,UAAU;AACzB,eAAS,MAAM,YAAY,eAAe,SAAS;AAAA,IACrD;AAAA,EACF;AACA,QAAMC,iBAAgB,cAAY;AAChC,UAAM,oBAAoB,OAAO,OAAO,IAAI,aAAW,oBAAoB,OAAO,CAAC;AACnF,sBAAkB,QAAQ,QAAM;AAC9B,SAAG,MAAM,qBAAqB,GAAG;AAAA,IACnC,CAAC;AACD,+BAA2B;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AACA,aAAW;AAAA,IACT,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA,cAAAD;AAAA,IACA,eAAAC;AAAA,IACA,iBAAiB,OAAO;AAAA,MACtB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,cAAc;AAAA,MACd,kBAAkB,CAAC,OAAO,OAAO;AAAA,IACnC;AAAA,EACF,CAAC;AACH;;;AC3De,SAAR,WAA4B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,YAAY;AAAA,MACV,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,aAAa;AAAA,IACf;AAAA,EACF,CAAC;AACD,QAAM,qBAAqB,CAAC,SAAS,UAAU,iBAAiB;AAC9D,QAAI,eAAe,eAAe,QAAQ,cAAc,2BAA2B,IAAI,QAAQ,cAAc,0BAA0B;AACvI,QAAI,cAAc,eAAe,QAAQ,cAAc,4BAA4B,IAAI,QAAQ,cAAc,6BAA6B;AAC1I,QAAI,CAAC,cAAc;AACjB,qBAAe,cAAc,OAAO,uBAAuB,eAAe,SAAS,OAAO;AAC1F,cAAQ,OAAO,YAAY;AAAA,IAC7B;AACA,QAAI,CAAC,aAAa;AAChB,oBAAc,cAAc,OAAO,uBAAuB,eAAe,UAAU,UAAU;AAC7F,cAAQ,OAAO,WAAW;AAAA,IAC5B;AACA,QAAI;AAAc,mBAAa,MAAM,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC;AACpE,QAAI;AAAa,kBAAY,MAAM,UAAU,KAAK,IAAI,UAAU,CAAC;AAAA,EACnE;AACA,QAAM,kBAAkB,MAAM;AAE5B,UAAM,eAAe,OAAO,aAAa;AACzC,WAAO,OAAO,QAAQ,aAAW;AAC/B,YAAM,WAAW,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG,EAAE;AAC3D,yBAAmB,SAAS,UAAU,YAAY;AAAA,IACpD,CAAC;AAAA,EACH;AACA,QAAMC,gBAAe,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,MAAM;AAAA,MACN,SAAAC;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,eAAe,OAAO,aAAa;AACzC,UAAM,YAAY,OAAO,WAAW,OAAO,OAAO,QAAQ;AAC1D,QAAI,gBAAgB;AACpB,QAAI;AACJ,QAAI,OAAO,QAAQ;AACjB,UAAI,cAAc;AAChB,uBAAe,OAAO,SAAS,cAAc,qBAAqB;AAClE,YAAI,CAAC,cAAc;AACjB,yBAAe,cAAc,OAAO,oBAAoB;AACxD,iBAAO,SAAS,OAAO,YAAY;AAAA,QACrC;AACA,qBAAa,MAAM,SAAS,GAAG;AAAA,MACjC,OAAO;AACL,uBAAe,GAAG,cAAc,qBAAqB;AACrD,YAAI,CAAC,cAAc;AACjB,yBAAe,cAAc,OAAO,oBAAoB;AACxD,aAAG,OAAO,YAAY;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,YAAM,UAAU,OAAO,CAAC;AACxB,UAAI,aAAa;AACjB,UAAI,WAAW;AACb,qBAAa,SAAS,QAAQ,aAAa,yBAAyB,GAAG,EAAE;AAAA,MAC3E;AACA,UAAI,aAAa,aAAa;AAC9B,UAAI,QAAQ,KAAK,MAAM,aAAa,GAAG;AACvC,UAAI,KAAK;AACP,qBAAa,CAAC;AACd,gBAAQ,KAAK,MAAM,CAAC,aAAa,GAAG;AAAA,MACtC;AACA,YAAM,WAAW,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG,EAAE;AAC3D,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,aAAa,MAAM,GAAG;AACxB,aAAK,CAAC,QAAQ,IAAI;AAClB,aAAK;AAAA,MACP,YAAY,aAAa,KAAK,MAAM,GAAG;AACrC,aAAK;AACL,aAAK,CAAC,QAAQ,IAAI;AAAA,MACpB,YAAY,aAAa,KAAK,MAAM,GAAG;AACrC,aAAK,aAAa,QAAQ,IAAI;AAC9B,aAAK;AAAA,MACP,YAAY,aAAa,KAAK,MAAM,GAAG;AACrC,aAAK,CAAC;AACN,aAAK,IAAI,aAAa,aAAa,IAAI;AAAA,MACzC;AACA,UAAI,KAAK;AACP,aAAK,CAAC;AAAA,MACR;AACA,UAAI,CAAC,cAAc;AACjB,aAAK;AACL,aAAK;AAAA,MACP;AACA,YAAM,YAAY,WAAW,eAAe,IAAI,CAAC,0BAA0B,eAAe,aAAa,qBAAqB,SAAS,SAAS;AAC9I,UAAI,YAAY,KAAK,WAAW,IAAI;AAClC,wBAAgB,aAAa,KAAK,WAAW;AAC7C,YAAI;AAAK,0BAAgB,CAAC,aAAa,KAAK,WAAW;AAAA,MACzD;AACA,cAAQ,MAAM,YAAY;AAC1B,UAAI,OAAO,cAAc;AACvB,2BAAmB,SAAS,UAAU,YAAY;AAAA,MACpD;AAAA,IACF;AACA,cAAU,MAAM,kBAAkB,YAAY,aAAa;AAC3D,cAAU,MAAM,0BAA0B,IAAI,YAAY,aAAa;AACvE,QAAI,OAAO,QAAQ;AACjB,UAAI,cAAc;AAChB,qBAAa,MAAM,YAAY,oBAAoB,cAAc,IAAI,OAAO,mBAAmB,CAAC,cAAc,2CAA2C,OAAO;AAAA,MAClK,OAAO;AACL,cAAM,cAAc,KAAK,IAAI,aAAa,IAAI,KAAK,MAAM,KAAK,IAAI,aAAa,IAAI,EAAE,IAAI;AACzF,cAAM,aAAa,OAAO,KAAK,IAAI,cAAc,IAAI,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,cAAc,IAAI,KAAK,KAAK,GAAG,IAAI;AACtH,cAAM,SAAS,OAAO;AACtB,cAAM,SAAS,OAAO,cAAc;AACpC,cAAM,SAAS,OAAO;AACtB,qBAAa,MAAM,YAAY,WAAW,cAAc,4BAA4B,eAAe,IAAI,aAAa,CAAC,eAAe,IAAI;AAAA,MAC1I;AAAA,IACF;AACA,UAAM,WAAWA,SAAQ,YAAYA,SAAQ,cAAcA,SAAQ,qBAAqB,CAAC,aAAa,IAAI;AAC1G,cAAU,MAAM,YAAY,qBAAqB,sBAAsB,OAAO,aAAa,IAAI,IAAI,6BAA6B,OAAO,aAAa,IAAI,CAAC,gBAAgB;AACzK,cAAU,MAAM,YAAY,6BAA6B,GAAG,WAAW;AAAA,EACzE;AACA,QAAMC,iBAAgB,cAAY;AAChC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,QAAQ,aAAW;AACxB,cAAQ,MAAM,qBAAqB,GAAG;AACtC,cAAQ,iBAAiB,8GAA8G,EAAE,QAAQ,WAAS;AACxJ,cAAM,MAAM,qBAAqB,GAAG;AAAA,MACtC,CAAC;AAAA,IACH,CAAC;AACD,QAAI,OAAO,OAAO,WAAW,UAAU,CAAC,OAAO,aAAa,GAAG;AAC7D,YAAM,WAAW,GAAG,cAAc,qBAAqB;AACvD,UAAI;AAAU,iBAAS,MAAM,qBAAqB,GAAG;AAAA,IACvD;AAAA,EACF;AACA,aAAW;AAAA,IACT,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA,cAAAF;AAAA,IACA,eAAAE;AAAA,IACA;AAAA,IACA,iBAAiB,MAAM,OAAO,OAAO;AAAA,IACrC,aAAa,MAAM;AAAA,IACnB,iBAAiB,OAAO;AAAA,MACtB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,IACpB;AAAA,EACF,CAAC;AACH;;;ACvKe,SAAR,aAA8B,QAAQ,SAAS,MAAM;AAC1D,QAAM,cAAc,sBAAsB,OAAO,IAAI,SAAS;AAC9D,QAAM,kBAAkB,oBAAoB,OAAO;AACnD,MAAI,WAAW,gBAAgB,cAAc,IAAI,aAAa;AAC9D,MAAI,CAAC,UAAU;AACb,eAAW,cAAc,OAAO,sBAAsB,OAAO,IAAI,SAAS,IAAI;AAC9E,oBAAgB,OAAO,QAAQ;AAAA,EACjC;AACA,SAAO;AACT;;;ACLe,SAAR,WAA4B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,YAAY;AAAA,MACV,cAAc;AAAA,MACd,eAAe;AAAA,IACjB;AAAA,EACF,CAAC;AACD,QAAM,qBAAqB,CAAC,SAAS,UAAU,WAAW;AACxD,QAAI,eAAe,OAAO,aAAa,IAAI,QAAQ,cAAc,2BAA2B,IAAI,QAAQ,cAAc,0BAA0B;AAChJ,QAAI,cAAc,OAAO,aAAa,IAAI,QAAQ,cAAc,4BAA4B,IAAI,QAAQ,cAAc,6BAA6B;AACnJ,QAAI,CAAC,cAAc;AACjB,qBAAe,aAAa,QAAQ,SAAS,OAAO,aAAa,IAAI,SAAS,KAAK;AAAA,IACrF;AACA,QAAI,CAAC,aAAa;AAChB,oBAAc,aAAa,QAAQ,SAAS,OAAO,aAAa,IAAI,UAAU,QAAQ;AAAA,IACxF;AACA,QAAI;AAAc,mBAAa,MAAM,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC;AACpE,QAAI;AAAa,kBAAY,MAAM,UAAU,KAAK,IAAI,UAAU,CAAC;AAAA,EACnE;AACA,QAAM,kBAAkB,MAAM;AAE5B,UAAM,SAAS,OAAO,OAAO;AAC7B,WAAO,OAAO,QAAQ,aAAW;AAC/B,UAAI,WAAW,QAAQ;AACvB,UAAI,OAAO,OAAO,WAAW,eAAe;AAC1C,mBAAW,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG,EAAE;AAAA,MACvD;AACA,yBAAmB,SAAS,UAAU,MAAM;AAAA,IAC9C,CAAC;AAAA,EACH;AACA,QAAMC,gBAAe,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,MACA,cAAc;AAAA,IAChB,IAAI;AACJ,UAAM,SAAS,OAAO,OAAO;AAC7B,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,YAAM,UAAU,OAAO,CAAC;AACxB,UAAI,WAAW,QAAQ;AACvB,UAAI,OAAO,OAAO,WAAW,eAAe;AAC1C,mBAAW,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG,EAAE;AAAA,MACvD;AACA,YAAM,SAAS,QAAQ;AACvB,YAAM,SAAS,OAAO;AACtB,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,KAAK,OAAO,OAAO,UAAU,CAAC,SAAS,OAAO,YAAY,CAAC;AAC/D,UAAI,KAAK;AACT,UAAI,CAAC,OAAO,aAAa,GAAG;AAC1B,aAAK;AACL,aAAK;AACL,kBAAU,CAAC;AACX,kBAAU;AAAA,MACZ,WAAW,KAAK;AACd,kBAAU,CAAC;AAAA,MACb;AACA,cAAQ,MAAM,SAAS,CAAC,KAAK,IAAI,KAAK,MAAM,QAAQ,CAAC,IAAI,OAAO;AAChE,UAAI,OAAO,cAAc;AACvB,2BAAmB,SAAS,UAAU,MAAM;AAAA,MAC9C;AACA,YAAM,YAAY,eAAe,SAAS,sBAAsB,uBAAuB;AACvF,YAAM,WAAW,aAAa,QAAQ,OAAO;AAC7C,eAAS,MAAM,YAAY;AAAA,IAC7B;AAAA,EACF;AACA,QAAMC,iBAAgB,cAAY;AAChC,UAAM,oBAAoB,OAAO,OAAO,IAAI,aAAW,oBAAoB,OAAO,CAAC;AACnF,sBAAkB,QAAQ,QAAM;AAC9B,SAAG,MAAM,qBAAqB,GAAG;AACjC,SAAG,iBAAiB,8GAA8G,EAAE,QAAQ,cAAY;AACtJ,iBAAS,MAAM,qBAAqB,GAAG;AAAA,MACzC,CAAC;AAAA,IACH,CAAC;AACD,+BAA2B;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAW;AAAA,IACT,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA,cAAAD;AAAA,IACA,eAAAC;AAAA,IACA;AAAA,IACA,iBAAiB,MAAM,OAAO,OAAO;AAAA,IACrC,aAAa,MAAM;AAAA,IACnB,iBAAiB,OAAO;AAAA,MACtB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,cAAc;AAAA,MACd,kBAAkB,CAAC,OAAO,OAAO;AAAA,IACnC;AAAA,EACF,CAAC;AACH;;;ACrGe,SAAR,gBAAiC;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,iBAAiB;AAAA,MACf,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,OAAO;AAAA,MACP,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,QAAMC,gBAAe,MAAM;AACzB,UAAM;AAAA,MACJ,OAAO;AAAA,MACP,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,eAAe,OAAO,aAAa;AACzC,UAAM,YAAY,OAAO;AACzB,UAAM,SAAS,eAAe,CAAC,YAAY,cAAc,IAAI,CAAC,YAAY,eAAe;AACzF,UAAM,SAAS,eAAe,OAAO,SAAS,CAAC,OAAO;AACtD,UAAM,YAAY,OAAO;AAEzB,aAAS,IAAI,GAAG,SAAS,OAAO,QAAQ,IAAI,QAAQ,KAAK,GAAG;AAC1D,YAAM,UAAU,OAAO,CAAC;AACxB,YAAM,YAAY,gBAAgB,CAAC;AACnC,YAAM,cAAc,QAAQ;AAC5B,YAAM,gBAAgB,SAAS,cAAc,YAAY,KAAK;AAC9D,YAAM,mBAAmB,OAAO,OAAO,aAAa,aAAa,OAAO,SAAS,YAAY,IAAI,eAAe,OAAO;AACvH,UAAI,UAAU,eAAe,SAAS,mBAAmB;AACzD,UAAI,UAAU,eAAe,IAAI,SAAS;AAE1C,UAAI,aAAa,CAAC,YAAY,KAAK,IAAI,gBAAgB;AACvD,UAAI,UAAU,OAAO;AAErB,UAAI,OAAO,YAAY,YAAY,QAAQ,QAAQ,GAAG,MAAM,IAAI;AAC9D,kBAAU,WAAW,OAAO,OAAO,IAAI,MAAM;AAAA,MAC/C;AACA,UAAI,aAAa,eAAe,IAAI,UAAU;AAC9C,UAAI,aAAa,eAAe,UAAU,mBAAmB;AAC7D,UAAI,QAAQ,KAAK,IAAI,OAAO,SAAS,KAAK,IAAI,gBAAgB;AAG9D,UAAI,KAAK,IAAI,UAAU,IAAI;AAAO,qBAAa;AAC/C,UAAI,KAAK,IAAI,UAAU,IAAI;AAAO,qBAAa;AAC/C,UAAI,KAAK,IAAI,UAAU,IAAI;AAAO,qBAAa;AAC/C,UAAI,KAAK,IAAI,OAAO,IAAI;AAAO,kBAAU;AACzC,UAAI,KAAK,IAAI,OAAO,IAAI;AAAO,kBAAU;AACzC,UAAI,KAAK,IAAI,KAAK,IAAI;AAAO,gBAAQ;AACrC,YAAM,iBAAiB,eAAe,gBAAgB,gBAAgB,0BAA0B,uBAAuB,qBAAqB;AAC5I,YAAM,WAAW,aAAa,QAAQ,OAAO;AAC7C,eAAS,MAAM,YAAY;AAC3B,cAAQ,MAAM,SAAS,CAAC,KAAK,IAAI,KAAK,MAAM,gBAAgB,CAAC,IAAI;AACjE,UAAI,OAAO,cAAc;AAEvB,YAAI,iBAAiB,eAAe,QAAQ,cAAc,2BAA2B,IAAI,QAAQ,cAAc,0BAA0B;AACzI,YAAI,gBAAgB,eAAe,QAAQ,cAAc,4BAA4B,IAAI,QAAQ,cAAc,6BAA6B;AAC5I,YAAI,CAAC,gBAAgB;AACnB,2BAAiB,aAAa,QAAQ,SAAS,eAAe,SAAS,KAAK;AAAA,QAC9E;AACA,YAAI,CAAC,eAAe;AAClB,0BAAgB,aAAa,QAAQ,SAAS,eAAe,UAAU,QAAQ;AAAA,QACjF;AACA,YAAI;AAAgB,yBAAe,MAAM,UAAU,mBAAmB,IAAI,mBAAmB;AAC7F,YAAI;AAAe,wBAAc,MAAM,UAAU,CAAC,mBAAmB,IAAI,CAAC,mBAAmB;AAAA,MAC/F;AAAA,IACF;AAAA,EACF;AACA,QAAMC,iBAAgB,cAAY;AAChC,UAAM,oBAAoB,OAAO,OAAO,IAAI,aAAW,oBAAoB,OAAO,CAAC;AACnF,sBAAkB,QAAQ,QAAM;AAC9B,SAAG,MAAM,qBAAqB,GAAG;AACjC,SAAG,iBAAiB,8GAA8G,EAAE,QAAQ,cAAY;AACtJ,iBAAS,MAAM,qBAAqB,GAAG;AAAA,MACzC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,aAAW;AAAA,IACT,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA,cAAAD;AAAA,IACA,eAAAC;AAAA,IACA,aAAa,MAAM;AAAA,IACnB,iBAAiB,OAAO;AAAA,MACtB,qBAAqB;AAAA,IACvB;AAAA,EACF,CAAC;AACH;;;AC7Fe,SAAR,eAAgC;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,gBAAgB;AAAA,MACd,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,aAAa;AAAA,MACb,MAAM;AAAA,QACJ,WAAW,CAAC,GAAG,GAAG,CAAC;AAAA,QACnB,QAAQ,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,MAAM;AAAA,QACJ,WAAW,CAAC,GAAG,GAAG,CAAC;AAAA,QACnB,QAAQ,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,oBAAoB,WAAS;AACjC,QAAI,OAAO,UAAU;AAAU,aAAO;AACtC,WAAO,GAAG;AAAA,EACZ;AACA,QAAMC,gBAAe,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM;AAAA,MACJ,oBAAoB;AAAA,IACtB,IAAI;AACJ,UAAM,mBAAmB,OAAO,OAAO;AACvC,QAAI,kBAAkB;AACpB,YAAM,SAAS,gBAAgB,CAAC,IAAI,IAAI,OAAO,OAAO,sBAAsB;AAC5E,gBAAU,MAAM,YAAY,yBAAyB;AAAA,IACvD;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,YAAM,UAAU,OAAO,CAAC;AACxB,YAAM,gBAAgB,QAAQ;AAC9B,YAAM,WAAW,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,CAAC,OAAO,aAAa,GAAG,OAAO,aAAa;AACjG,UAAI,mBAAmB;AACvB,UAAI,CAAC,kBAAkB;AACrB,2BAAmB,KAAK,IAAI,KAAK,IAAI,QAAQ,kBAAkB,CAAC,OAAO,aAAa,GAAG,OAAO,aAAa;AAAA,MAC7G;AACA,YAAM,SAAS,QAAQ;AACvB,YAAM,IAAI,CAAC,OAAO,OAAO,UAAU,CAAC,SAAS,OAAO,YAAY,CAAC,QAAQ,GAAG,CAAC;AAC7E,YAAM,IAAI,CAAC,GAAG,GAAG,CAAC;AAClB,UAAI,SAAS;AACb,UAAI,CAAC,OAAO,aAAa,GAAG;AAC1B,UAAE,CAAC,IAAI,EAAE,CAAC;AACV,UAAE,CAAC,IAAI;AAAA,MACT;AACA,UAAI,OAAO;AAAA,QACT,WAAW,CAAC,GAAG,GAAG,CAAC;AAAA,QACnB,QAAQ,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AACA,UAAI,WAAW,GAAG;AAChB,eAAO,OAAO;AACd,iBAAS;AAAA,MACX,WAAW,WAAW,GAAG;AACvB,eAAO,OAAO;AACd,iBAAS;AAAA,MACX;AAEA,QAAE,QAAQ,CAAC,OAAO,UAAU;AAC1B,UAAE,KAAK,IAAI,QAAQ,cAAc,kBAAkB,KAAK,UAAU,KAAK,CAAC,OAAO,KAAK,IAAI,WAAW,UAAU;AAAA,MAC/G,CAAC;AAED,QAAE,QAAQ,CAAC,OAAO,UAAU;AAC1B,UAAE,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,KAAK,IAAI,WAAW,UAAU;AAAA,MAChE,CAAC;AACD,cAAQ,MAAM,SAAS,CAAC,KAAK,IAAI,KAAK,MAAM,aAAa,CAAC,IAAI,OAAO;AACrE,YAAM,kBAAkB,EAAE,KAAK,IAAI;AACnC,YAAM,eAAe,WAAW,EAAE,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,EAAE,CAAC;AAC3E,YAAM,cAAc,mBAAmB,IAAI,SAAS,KAAK,IAAI,KAAK,SAAS,mBAAmB,gBAAgB,SAAS,KAAK,IAAI,KAAK,SAAS,mBAAmB;AACjK,YAAM,gBAAgB,mBAAmB,IAAI,KAAK,IAAI,KAAK,WAAW,mBAAmB,aAAa,KAAK,IAAI,KAAK,WAAW,mBAAmB;AAClJ,YAAM,YAAY,eAAe,oBAAoB,gBAAgB;AAGrE,UAAI,UAAU,KAAK,UAAU,CAAC,QAAQ;AACpC,YAAI,WAAW,QAAQ,cAAc,sBAAsB;AAC3D,YAAI,CAAC,YAAY,KAAK,QAAQ;AAC5B,qBAAW,aAAa,QAAQ,OAAO;AAAA,QACzC;AACA,YAAI,UAAU;AACZ,gBAAM,gBAAgB,OAAO,oBAAoB,YAAY,IAAI,OAAO,iBAAiB;AACzF,mBAAS,MAAM,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,aAAa,GAAG,CAAC,GAAG,CAAC;AAAA,QAC3E;AAAA,MACF;AACA,YAAM,WAAW,aAAa,QAAQ,OAAO;AAC7C,eAAS,MAAM,YAAY;AAC3B,eAAS,MAAM,UAAU;AACzB,UAAI,KAAK,QAAQ;AACf,iBAAS,MAAM,kBAAkB,KAAK;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACA,QAAMC,iBAAgB,cAAY;AAChC,UAAM,oBAAoB,OAAO,OAAO,IAAI,aAAW,oBAAoB,OAAO,CAAC;AACnF,sBAAkB,QAAQ,QAAM;AAC9B,SAAG,MAAM,qBAAqB,GAAG;AACjC,SAAG,iBAAiB,sBAAsB,EAAE,QAAQ,cAAY;AAC9D,iBAAS,MAAM,qBAAqB,GAAG;AAAA,MACzC,CAAC;AAAA,IACH,CAAC;AACD,+BAA2B;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AACA,aAAW;AAAA,IACT,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA,cAAAD;AAAA,IACA,eAAAC;AAAA,IACA,aAAa,MAAM,OAAO,OAAO,eAAe;AAAA,IAChD,iBAAiB,OAAO;AAAA,MACtB,qBAAqB;AAAA,MACrB,kBAAkB,CAAC,OAAO,OAAO;AAAA,IACnC;AAAA,EACF,CAAC;AACH;;;ACtIe,SAAR,YAA6B;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,eAAa;AAAA,IACX,aAAa;AAAA,MACX,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,QAAMC,gBAAe,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,UAAM,mBAAmB,OAAO;AAChC,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,YAAM,UAAU,OAAO,CAAC;AACxB,YAAM,gBAAgB,QAAQ;AAC9B,YAAM,WAAW,KAAK,IAAI,KAAK,IAAI,eAAe,EAAE,GAAG,CAAC;AACxD,UAAI,SAAS,QAAQ;AACrB,UAAI,OAAO,OAAO,kBAAkB,CAAC,OAAO,OAAO,SAAS;AAC1D,eAAO,UAAU,MAAM,YAAY,cAAc,OAAO,aAAa;AAAA,MACvE;AACA,UAAI,OAAO,OAAO,kBAAkB,OAAO,OAAO,SAAS;AACzD,kBAAU,OAAO,CAAC,EAAE;AAAA,MACtB;AACA,UAAI,KAAK,OAAO,OAAO,UAAU,CAAC,SAAS,OAAO,YAAY,CAAC;AAC/D,UAAI,KAAK;AACT,YAAM,KAAK,OAAO,KAAK,IAAI,QAAQ;AACnC,UAAI,QAAQ;AACZ,UAAI,SAAS,CAAC,OAAO,iBAAiB;AACtC,UAAI,QAAQ,OAAO,iBAAiB,KAAK,IAAI,QAAQ,IAAI;AACzD,YAAM,aAAa,OAAO,WAAW,OAAO,OAAO,QAAQ,UAAU,OAAO,QAAQ,OAAO,IAAI;AAC/F,YAAM,iBAAiB,eAAe,eAAe,eAAe,cAAc,MAAM,WAAW,KAAK,WAAW,MAAM,aAAa,OAAO,OAAO,YAAY,mBAAmB;AACnL,YAAM,iBAAiB,eAAe,eAAe,eAAe,cAAc,MAAM,WAAW,KAAK,WAAW,OAAO,aAAa,OAAO,OAAO,YAAY,mBAAmB;AACpL,UAAI,iBAAiB,eAAe;AAClC,cAAM,eAAe,IAAI,KAAK,KAAK,KAAK,IAAI,QAAQ,IAAI,OAAO,GAAG,MAAM;AACxE,kBAAU,MAAM,WAAW;AAC3B,iBAAS,OAAO;AAChB,iBAAS,KAAK;AACd,aAAK,GAAG,MAAM,cAAc,KAAK,IAAI,QAAQ;AAAA,MAC/C;AACA,UAAI,WAAW,GAAG;AAEhB,aAAK,QAAQ,WAAW,QAAQ,KAAK,IAAI,QAAQ;AAAA,MACnD,WAAW,WAAW,GAAG;AAEvB,aAAK,QAAQ,YAAY,QAAQ,KAAK,IAAI,QAAQ;AAAA,MACpD,OAAO;AACL,aAAK,GAAG;AAAA,MACV;AACA,UAAI,CAAC,OAAO,aAAa,GAAG;AAC1B,cAAM,QAAQ;AACd,aAAK;AACL,aAAK;AAAA,MACP;AACA,YAAM,cAAc,WAAW,IAAI,GAAG,KAAK,IAAI,SAAS,aAAa,GAAG,KAAK,IAAI,SAAS;AAC1F,YAAM,YAAY;AAAA,sBACF,OAAO,OAAO;AAAA,kBAClB,OAAO,SAAS,SAAS;AAAA,gBAC3B;AAAA;AAEV,UAAI,OAAO,cAAc;AAEvB,YAAI,WAAW,QAAQ,cAAc,sBAAsB;AAC3D,YAAI,CAAC,UAAU;AACb,qBAAW,aAAa,QAAQ,OAAO;AAAA,QACzC;AACA,YAAI;AAAU,mBAAS,MAAM,UAAU,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,QAAQ,IAAI,OAAO,KAAK,CAAC,GAAG,CAAC;AAAA,MAClG;AACA,cAAQ,MAAM,SAAS,CAAC,KAAK,IAAI,KAAK,MAAM,aAAa,CAAC,IAAI,OAAO;AACrE,YAAM,WAAW,aAAa,QAAQ,OAAO;AAC7C,eAAS,MAAM,YAAY;AAAA,IAC7B;AAAA,EACF;AACA,QAAMC,iBAAgB,cAAY;AAChC,UAAM,oBAAoB,OAAO,OAAO,IAAI,aAAW,oBAAoB,OAAO,CAAC;AACnF,sBAAkB,QAAQ,QAAM;AAC9B,SAAG,MAAM,qBAAqB,GAAG;AACjC,SAAG,iBAAiB,sBAAsB,EAAE,QAAQ,cAAY;AAC9D,iBAAS,MAAM,qBAAqB,GAAG;AAAA,MACzC,CAAC;AAAA,IACH,CAAC;AACD,+BAA2B;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAW;AAAA,IACT,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA,cAAAD;AAAA,IACA,eAAAC;AAAA,IACA,aAAa,MAAM;AAAA,IACnB,iBAAiB,OAAO;AAAA,MACtB,qBAAqB;AAAA,MACrB,kBAAkB,CAAC,OAAO,OAAO;AAAA,IACnC;AAAA,EACF,CAAC;AACH;", "names": ["getComputedStyle", "window", "isObject", "extend", "window", "document", "window", "document", "support", "window", "window", "window", "window", "observerUpdate", "events", "event", "realIndex", "minTranslate", "maxTranslate", "transitionEnd", "transitionEnd", "slideTo", "setTranslate", "event", "document", "window", "event", "document", "event", "increment", "document", "extend", "window", "extend", "extend", "document", "setTranslate", "document", "appendSlide", "prependSlide", "removeSlide", "removeAllSlides", "document", "window", "event", "window", "timeout", "event", "events", "document", "timeout", "setTranslate", "setTransition", "updateSize", "events", "setTranslate", "setTransition", "window", "onTouchStart", "event", "onTouchMove", "onTouchEnd", "setTranslate", "Swiper", "setTransition", "window", "document", "window", "timeout", "document", "isObject", "document", "onTouchStart", "onTouchMove", "onTouchEnd", "setTranslate", "setTransition", "setTranslate", "setTransition", "setTranslate", "browser", "setTransition", "setTranslate", "setTransition", "setTranslate", "setTransition", "setTranslate", "setTransition", "setTranslate", "setTransition"]}