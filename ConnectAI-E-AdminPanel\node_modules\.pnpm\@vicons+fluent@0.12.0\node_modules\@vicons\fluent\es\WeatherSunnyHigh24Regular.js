import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12 2a.75.75 0 0 1 .75.75v.5a.75.75 0 0 1-1.5 0v-.5A.75.75 0 0 1 12 2zm0 3a4 4 0 1 0 0 8a4 4 0 0 0 0-8zM9.5 9a2.5 2.5 0 1 1 5 0a2.5 2.5 0 0 1-5 0zm3.25 5.75a.75.75 0 0 0-1.5 0v.5a.75.75 0 0 0 1.5 0v-.5zM5.75 8a.75.75 0 0 0 0 1.5h.5a.75.75 0 0 0 0-1.5h-.5zM17 8.75a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1-.75-.75zM6.72 5.78a.75.75 0 0 0 1.06-1.06l-.5-.5a.75.75 0 0 0-1.06 1.06l.5.5zm1.06 6.44a.75.75 0 0 0-1.06 0l-.5.5a.75.75 0 1 0 1.06 1.06l.5-.5a.75.75 0 0 0 0-1.06zm9.5-6.44a.75.75 0 1 1-1.06-1.06l.5-.5a.75.75 0 1 1 1.06 1.06l-.5.5zm-1.06 6.44a.75.75 0 0 1 1.06 0l.5.5a.75.75 0 1 1-1.06 1.06l-.5-.5a.75.75 0 0 1 0-1.06zM3.218 21.836a.75.75 0 0 1-1.054-.117c-.408-.51.119-1.055.119-1.055v-.002l.016-.011a3.49 3.49 0 0 1 .183-.137c.124-.09.303-.215.534-.364c.463-.297 1.134-.69 1.982-1.081C6.693 18.287 9.114 17.5 12 17.5s5.307.787 7.002 1.569a16.4 16.4 0 0 1 1.982 1.081a11.798 11.798 0 0 1 .717.5l.012.01l.003.003l.003.001a.75.75 0 0 1-.937 1.172l-.005-.004l-.025-.02a10.254 10.254 0 0 0-.58-.4a14.885 14.885 0 0 0-1.799-.981C16.818 19.713 14.614 19 12 19c-2.614 0-4.818.713-6.373 1.431c-.776.358-1.387.716-1.8.981a10.269 10.269 0 0 0-.579.4l-.025.02l-.005.004z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSunnyHigh24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
