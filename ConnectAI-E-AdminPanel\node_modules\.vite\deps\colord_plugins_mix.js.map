{"version": 3, "sources": ["../../.pnpm/colord@2.9.3/node_modules/colord/plugins/mix.mjs"], "sourcesContent": ["var t=function(t,a,n){return void 0===a&&(a=0),void 0===n&&(n=1),t>n?n:t>a?t:a},a=function(t){var a=t/255;return a<.04045?a/12.92:Math.pow((a+.055)/1.055,2.4)},n=function(t){return 255*(t>.0031308?1.055*Math.pow(t,1/2.4)-.055:12.92*t)},r=96.422,o=100,u=82.521,e=function(a){var r,o,u={x:.9555766*(r=a).x+-.0230393*r.y+.0631636*r.z,y:-.0282895*r.x+1.0099416*r.y+.0210077*r.z,z:.0122982*r.x+-.020483*r.y+1.3299098*r.z};return o={r:n(.032404542*u.x-.015371385*u.y-.004985314*u.z),g:n(-.00969266*u.x+.018760108*u.y+41556e-8*u.z),b:n(556434e-9*u.x-.002040259*u.y+.010572252*u.z),a:a.a},{r:t(o.r,0,255),g:t(o.g,0,255),b:t(o.b,0,255),a:t(o.a)}},i=function(n){var e=a(n.r),i=a(n.g),p=a(n.b);return function(a){return{x:t(a.x,0,r),y:t(a.y,0,o),z:t(a.z,0,u),a:t(a.a)}}(function(t){return{x:1.0478112*t.x+.0228866*t.y+-.050127*t.z,y:.0295424*t.x+.9904844*t.y+-.0170491*t.z,z:-.0092345*t.x+.0150436*t.y+.7521316*t.z,a:t.a}}({x:100*(.4124564*e+.3575761*i+.1804375*p),y:100*(.2126729*e+.7151522*i+.072175*p),z:100*(.0193339*e+.119192*i+.9503041*p),a:n.a}))},p=216/24389,h=24389/27,f=function(t){var a=i(t),n=a.x/r,e=a.y/o,f=a.z/u;return n=n>p?Math.cbrt(n):(h*n+16)/116,{l:116*(e=e>p?Math.cbrt(e):(h*e+16)/116)-16,a:500*(n-e),b:200*(e-(f=f>p?Math.cbrt(f):(h*f+16)/116)),alpha:a.a}},c=function(a,n,i){var c,y=f(a),x=f(n);return function(t){var a=(t.l+16)/116,n=t.a/500+a,i=a-t.b/200;return e({x:(Math.pow(n,3)>p?Math.pow(n,3):(116*n-16)/h)*r,y:(t.l>8?Math.pow((t.l+16)/116,3):t.l/h)*o,z:(Math.pow(i,3)>p?Math.pow(i,3):(116*i-16)/h)*u,a:t.alpha})}({l:t((c={l:y.l*(1-i)+x.l*i,a:y.a*(1-i)+x.a*i,b:y.b*(1-i)+x.b*i,alpha:y.alpha*(1-i)+x.alpha*i}).l,0,400),a:c.a,b:c.b,alpha:t(c.alpha)})};export default function(t){function a(t,a,n){void 0===n&&(n=5);for(var r=[],o=1/(n-1),u=0;u<=n-1;u++)r.push(t.mix(a,o*u));return r}t.prototype.mix=function(a,n){void 0===n&&(n=.5);var r=a instanceof t?a:new t(a),o=c(this.toRgb(),r.toRgb(),n);return new t(o)},t.prototype.tints=function(t){return a(this,\"#fff\",t)},t.prototype.shades=function(t){return a(this,\"#000\",t)},t.prototype.tones=function(t){return a(this,\"#808080\",t)}}\n"], "mappings": ";;;AAAA,IAAI,IAAE,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,WAASD,OAAIA,KAAE,IAAG,WAASC,OAAIA,KAAE,IAAGF,KAAEE,KAAEA,KAAEF,KAAEC,KAAED,KAAEC;AAAC;AAA9E,IAAgF,IAAE,SAASD,IAAE;AAAC,MAAIC,KAAED,KAAE;AAAI,SAAOC,KAAE,UAAOA,KAAE,QAAM,KAAK,KAAKA,KAAE,SAAM,OAAM,GAAG;AAAC;AAA9J,IAAgK,IAAE,SAASD,IAAE;AAAC,SAAO,OAAKA,KAAE,WAAS,QAAM,KAAK,IAAIA,IAAE,IAAE,GAAG,IAAE,QAAK,QAAMA;AAAE;AAA1O,IAA4O,IAAE;AAA9O,IAAqP,IAAE;AAAvP,IAA2P,IAAE;AAA7P,IAAoQ,IAAE,SAASC,IAAE;AAAC,MAAIE,IAAEC,IAAEC,KAAE,EAAC,GAAE,aAAUF,KAAEF,IAAG,IAAE,aAAUE,GAAE,IAAE,YAASA,GAAE,GAAE,GAAE,aAAUA,GAAE,IAAE,YAAUA,GAAE,IAAE,YAASA,GAAE,GAAE,GAAE,YAASA,GAAE,IAAE,YAASA,GAAE,IAAE,YAAUA,GAAE,EAAC;AAAE,SAAOC,KAAE,EAAC,GAAE,EAAE,cAAWC,GAAE,IAAE,cAAWA,GAAE,IAAE,aAAWA,GAAE,CAAC,GAAE,GAAE,EAAE,aAAWA,GAAE,IAAE,cAAWA,GAAE,IAAE,WAASA,GAAE,CAAC,GAAE,GAAE,EAAE,YAAUA,GAAE,IAAE,aAAWA,GAAE,IAAE,cAAWA,GAAE,CAAC,GAAE,GAAEJ,GAAE,EAAC,GAAE,EAAC,GAAE,EAAEG,GAAE,GAAE,GAAE,GAAG,GAAE,GAAE,EAAEA,GAAE,GAAE,GAAE,GAAG,GAAE,GAAE,EAAEA,GAAE,GAAE,GAAE,GAAG,GAAE,GAAE,EAAEA,GAAE,CAAC,EAAC;AAAC;AAA5nB,IAA8nB,IAAE,SAASF,IAAE;AAAC,MAAII,KAAE,EAAEJ,GAAE,CAAC,GAAEK,KAAE,EAAEL,GAAE,CAAC,GAAEM,KAAE,EAAEN,GAAE,CAAC;AAAE,SAAO,SAASD,IAAE;AAAC,WAAM,EAAC,GAAE,EAAEA,GAAE,GAAE,GAAE,CAAC,GAAE,GAAE,EAAEA,GAAE,GAAE,GAAE,CAAC,GAAE,GAAE,EAAEA,GAAE,GAAE,GAAE,CAAC,GAAE,GAAE,EAAEA,GAAE,CAAC,EAAC;AAAA,EAAC,EAAE,SAASD,IAAE;AAAC,WAAM,EAAC,GAAE,YAAUA,GAAE,IAAE,YAASA,GAAE,IAAE,YAASA,GAAE,GAAE,GAAE,YAASA,GAAE,IAAE,YAASA,GAAE,IAAE,aAAUA,GAAE,GAAE,GAAE,YAAUA,GAAE,IAAE,YAASA,GAAE,IAAE,YAASA,GAAE,GAAE,GAAEA,GAAE,EAAC;AAAA,EAAC,EAAE,EAAC,GAAE,OAAK,YAASM,KAAE,YAASC,KAAE,YAASC,KAAG,GAAE,OAAK,YAASF,KAAE,YAASC,KAAE,WAAQC,KAAG,GAAE,OAAK,YAASF,KAAE,WAAQC,KAAE,YAASC,KAAG,GAAEN,GAAE,EAAC,CAAC,CAAC;AAAC;AAAlhC,IAAohC,IAAE,MAAI;AAA1hC,IAAgiC,IAAE,QAAM;AAAxiC,IAA2iC,IAAE,SAASF,IAAE;AAAC,MAAIC,KAAE,EAAED,EAAC,GAAEE,KAAED,GAAE,IAAE,GAAEK,KAAEL,GAAE,IAAE,GAAEQ,KAAER,GAAE,IAAE;AAAE,SAAOC,KAAEA,KAAE,IAAE,KAAK,KAAKA,EAAC,KAAG,IAAEA,KAAE,MAAI,KAAI,EAAC,GAAE,OAAKI,KAAEA,KAAE,IAAE,KAAK,KAAKA,EAAC,KAAG,IAAEA,KAAE,MAAI,OAAK,IAAG,GAAE,OAAKJ,KAAEI,KAAG,GAAE,OAAKA,MAAGG,KAAEA,KAAE,IAAE,KAAK,KAAKA,EAAC,KAAG,IAAEA,KAAE,MAAI,OAAM,OAAMR,GAAE,EAAC;AAAC;AAAjvC,IAAmvC,IAAE,SAASA,IAAEC,IAAEK,IAAE;AAAC,MAAIG,IAAE,IAAE,EAAET,EAAC,GAAE,IAAE,EAAEC,EAAC;AAAE,SAAO,SAASF,IAAE;AAAC,QAAIC,MAAGD,GAAE,IAAE,MAAI,KAAIE,KAAEF,GAAE,IAAE,MAAIC,IAAEM,KAAEN,KAAED,GAAE,IAAE;AAAI,WAAO,EAAE,EAAC,IAAG,KAAK,IAAIE,IAAE,CAAC,IAAE,IAAE,KAAK,IAAIA,IAAE,CAAC,KAAG,MAAIA,KAAE,MAAI,KAAG,GAAE,IAAGF,GAAE,IAAE,IAAE,KAAK,KAAKA,GAAE,IAAE,MAAI,KAAI,CAAC,IAAEA,GAAE,IAAE,KAAG,GAAE,IAAG,KAAK,IAAIO,IAAE,CAAC,IAAE,IAAE,KAAK,IAAIA,IAAE,CAAC,KAAG,MAAIA,KAAE,MAAI,KAAG,GAAE,GAAEP,GAAE,MAAK,CAAC;AAAA,EAAC,EAAE,EAAC,GAAE,GAAGU,KAAE,EAAC,GAAE,EAAE,KAAG,IAAEH,MAAG,EAAE,IAAEA,IAAE,GAAE,EAAE,KAAG,IAAEA,MAAG,EAAE,IAAEA,IAAE,GAAE,EAAE,KAAG,IAAEA,MAAG,EAAE,IAAEA,IAAE,OAAM,EAAE,SAAO,IAAEA,MAAG,EAAE,QAAMA,GAAC,GAAG,GAAE,GAAE,GAAG,GAAE,GAAEG,GAAE,GAAE,GAAEA,GAAE,GAAE,OAAM,EAAEA,GAAE,KAAK,EAAC,CAAC;AAAC;AAAiB,SAAR,YAAiBV,IAAE;AAAC,WAASC,GAAED,IAAEC,IAAEC,IAAE;AAAC,eAASA,OAAIA,KAAE;AAAG,aAAQC,KAAE,CAAC,GAAEC,KAAE,KAAGF,KAAE,IAAGG,KAAE,GAAEA,MAAGH,KAAE,GAAEG;AAAI,MAAAF,GAAE,KAAKH,GAAE,IAAIC,IAAEG,KAAEC,EAAC,CAAC;AAAE,WAAOF;AAAA,EAAC;AAAC,EAAAH,GAAE,UAAU,MAAI,SAASC,IAAEC,IAAE;AAAC,eAASA,OAAIA,KAAE;AAAI,QAAIC,KAAEF,cAAaD,KAAEC,KAAE,IAAID,GAAEC,EAAC,GAAEG,KAAE,EAAE,KAAK,MAAM,GAAED,GAAE,MAAM,GAAED,EAAC;AAAE,WAAO,IAAIF,GAAEI,EAAC;AAAA,EAAC,GAAEJ,GAAE,UAAU,QAAM,SAASA,IAAE;AAAC,WAAOC,GAAE,MAAK,QAAOD,EAAC;AAAA,EAAC,GAAEA,GAAE,UAAU,SAAO,SAASA,IAAE;AAAC,WAAOC,GAAE,MAAK,QAAOD,EAAC;AAAA,EAAC,GAAEA,GAAE,UAAU,QAAM,SAASA,IAAE;AAAC,WAAOC,GAAE,MAAK,WAAUD,EAAC;AAAA,EAAC;AAAC;", "names": ["t", "a", "n", "r", "o", "u", "e", "i", "p", "f", "c"]}