import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.745 10.751a8.294 8.294 0 0 1 1.492 2.07a.75.75 0 1 1-1.336.682a6.795 6.795 0 0 0-1.217-1.691A6.562 6.562 0 0 0 6.19 13.485a.75.75 0 1 1-1.338-.677a8.062 8.062 0 0 1 12.893-2.057zm-2.102 3.069c.448.447.816.997 1.072 1.582a.75.75 0 1 1-1.374.602a3.72 3.72 0 0 0-.759-1.124a3.592 3.592 0 0 0-5.08 0c-.31.311-.562.69-.747 1.111a.75.75 0 1 1-1.374-.601a5.11 5.11 0 0 1 1.06-1.57a5.092 5.092 0 0 1 7.202 0zm-2.582 2.62a1.5 1.5 0 1 1-2.122 2.121a1.5 1.5 0 0 1 2.122-2.122z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wifi224Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
