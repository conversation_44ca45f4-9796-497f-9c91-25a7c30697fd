import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M39 7.25a1.25 1.25 0 1 0-2.5 0V9h-1.75a1.25 1.25 0 0 0 0 2.5h1.75v1.75a1.25 1.25 0 0 0 2.5 0V11.5h1.75a1.25 1.25 0 1 0 0-2.5H39V7.25zm-24 4a1.25 1.25 0 1 0-2.5 0V13h-1.75a1.25 1.25 0 0 0 0 2.5h1.75v1.75a1.25 1.25 0 0 0 2.5 0V15.5h1.75a1.25 1.25 0 1 0 0-2.5H15v-1.75zM33.75 30c.69 0 1.25.56 1.25 1.25V33h1.75a1.25 1.25 0 1 1 0 2.5H35v1.75a1.25 1.25 0 1 1-2.5 0V35.5h-1.75a1.25 1.25 0 0 1 0-2.5h1.75v-1.75c0-.69.56-1.25 1.25-1.25zm-1.143-14.596a4.75 4.75 0 0 0-6.717 0l-1.36 1.359l6.718 6.717l1.359-1.358a4.75 4.75 0 0 0 0-6.718zm-3.126 9.844l-6.718-6.717L5.396 35.898a4.75 4.75 0 0 0 6.717 6.718l17.368-17.368z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wand48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
