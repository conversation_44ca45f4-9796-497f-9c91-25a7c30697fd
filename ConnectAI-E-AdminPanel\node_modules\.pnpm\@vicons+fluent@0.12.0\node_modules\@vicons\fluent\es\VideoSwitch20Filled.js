import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.5 2A2.5 2.5 0 0 0 4 4.5v5A2.5 2.5 0 0 0 6.5 12h4A2.5 2.5 0 0 0 13 9.5v-5A2.5 2.5 0 0 0 10.5 2h-4zm9.391 7.918L14 8.887V5.113l1.891-1.03A.75.75 0 0 1 17 4.74v4.518a.75.75 0 0 1-1.109.659zM3.67 11.888a4.82 4.82 0 0 0-.846.41C2.408 12.56 2 12.955 2 13.5c0 .546.408.94.823 1.201c.44.278 1.043.51 1.745.696c1.41.376 3.33.603 5.432.603c.098 0 .197 0 .294-.002l-1.148 1.148a.5.5 0 0 0 .708.708l2-2a.5.5 0 0 0 0-.708l-2-2a.5.5 0 0 0-.708.708l1.145 1.145A28.16 28.16 0 0 1 10 15c-2.04 0-3.87-.221-5.174-.569c-.656-.175-1.151-.374-1.47-.575c-.344-.217-.356-.35-.356-.356c0-.006.012-.139.356-.355c.277-.175.686-.348 1.217-.505a3.013 3.013 0 0 1-.904-.751zm9.395.29c.274-.26.5-.572.66-.92c.626.093 1.2.21 1.708.345c.702.187 1.305.418 1.745.696c.415.261.823.655.823 1.201s-.408.94-.823 1.201c-.44.278-1.043.51-1.745.696c-.266.071-.55.137-.85.197a.49.49 0 0 1-.582-.486c0-.245.176-.453.415-.5c.27-.055.523-.114.76-.177c.655-.175 1.15-.374 1.469-.575c.344-.217.356-.35.356-.356c0-.006-.012-.139-.356-.355c-.319-.202-.814-.401-1.47-.576a15.734 15.734 0 0 0-2.11-.391z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoSwitch20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
