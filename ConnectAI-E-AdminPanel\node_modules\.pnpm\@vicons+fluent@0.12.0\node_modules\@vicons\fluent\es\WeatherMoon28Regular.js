import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M13.823 2.302a.75.75 0 0 0-.155.668c.652 2.6 1.105 6.518-.608 9.945c-.859 1.716-2.396 3.02-4.17 4.003c-1.77.98-3.72 1.61-5.32 2.004a.75.75 0 0 0-.468 1.106A11.995 11.995 0 0 0 13.48 26c6.628 0 12-5.372 12-12c0-6.299-4.853-11.464-11.024-11.96a.75.75 0 0 0-.633.262zm1.54 1.366c4.9.887 8.617 5.176 8.617 10.332c0 5.8-4.7 10.5-10.5 10.5c-3.518 0-6.634-1.73-8.54-4.39c1.462-.416 3.122-1.018 4.677-1.88c1.924-1.066 3.742-2.56 4.784-4.644c1.717-3.433 1.501-7.207.961-9.918z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherMoon28Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
