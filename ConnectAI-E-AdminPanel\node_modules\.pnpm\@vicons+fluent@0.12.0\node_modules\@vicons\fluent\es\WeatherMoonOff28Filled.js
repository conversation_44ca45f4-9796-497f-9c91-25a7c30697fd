import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.28 2.22a.75.75 0 1 0-1.06 1.06l10.397 10.398c-.894 1.346-2.226 2.408-3.727 3.24c-1.77.98-3.72 1.61-5.32 2.004a.75.75 0 0 0-.468 1.105A11.995 11.995 0 0 0 13.48 26c3.177 0 6.064-1.234 8.21-3.248l3.03 3.029a.75.75 0 0 0 1.06-1.061L3.28 2.22zm10.594 8.473l9.74 9.739A11.945 11.945 0 0 0 25.48 14c0-6.3-4.853-11.464-11.024-11.96a.75.75 0 0 0-.788.93c.514 2.05.905 4.92.206 7.723z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherMoonOff28Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
