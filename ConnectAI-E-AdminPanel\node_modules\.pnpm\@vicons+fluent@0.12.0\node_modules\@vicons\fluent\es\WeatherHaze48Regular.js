import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10 37.763l-.156.084a1.25 1.25 0 0 1-1.197-2.195c2.982-1.627 5.664-2.006 8.319-1.516c2.585.478 5.062 1.769 7.657 3.283c4.752 2.771 10.291 2.031 13.395-.186a1.25 1.25 0 1 1 1.453 2.034c-3.895 2.783-10.496 3.584-16.107.311c-2.587-1.509-4.734-2.592-6.852-2.983c-2.007-.37-4.064-.131-6.512 1.168zm26-2.78c.199-.063.394-.131.587-.203a12.426 12.426 0 0 0 2.887-1.513a1.25 1.25 0 1 0-1.453-2.034c-3.104 2.217-8.643 2.957-13.395.186c-2.595-1.514-5.072-2.805-7.658-3.283c-2.655-.49-5.336-.11-8.32 1.516a1.25 1.25 0 0 0 1.198 2.195c2.517-1.373 4.62-1.63 6.668-1.252c2.118.391 4.266 1.474 6.852 2.983c4.17 2.433 8.887 2.615 12.634 1.406zm-22.903-10.37a8.713 8.713 0 0 1 2.496-.144c.31.027.617.07.924.126c2.118.391 4.265 1.475 6.852 2.983c5.611 3.273 12.212 2.472 16.108-.311a1.25 1.25 0 1 0-1.453-2.034c-3.105 2.217-8.644 2.957-13.396.186c-2.595-1.514-5.072-2.805-7.657-3.283c-2.655-.49-5.337-.11-8.32 1.516a1.25 1.25 0 0 0 1.198 2.195c1.159-.632 2.23-1.028 3.248-1.234zM32.42 24c0 .22-.008.44-.025.656a9.952 9.952 0 0 0 2.525-.589V24c0-6.03-4.888-10.92-10.92-10.92c-4.41 0-8.209 2.615-9.932 6.379c.925-.053 1.845-.01 2.765.122A8.42 8.42 0 0 1 32.419 24zm-8.345 20a1.315 1.315 0 0 1-.164 0h.164zM11.607 9.895l-.101-.09a1.25 1.25 0 0 0-1.667 1.858l2.148 2.148l.102.09a1.25 1.25 0 0 0 1.666-1.858l-2.148-2.148zm26.628 1.666a1.25 1.25 0 0 0-1.859-1.666l-2.148 2.148l-.09.102a1.25 1.25 0 0 0 1.858 1.666l2.148-2.148l.091-.102zM25.244 5.12a1.25 1.25 0 0 0-2.494.128v3.038l.007.127a1.25 1.25 0 0 0 2.493-.127V5.247l-.006-.128z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherHaze48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
