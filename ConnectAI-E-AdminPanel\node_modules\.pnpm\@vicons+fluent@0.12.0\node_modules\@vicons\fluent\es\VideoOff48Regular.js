import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M33.48 35.248l8.386 8.386a1.25 1.25 0 0 0 1.768-1.768l-37.5-37.5a1.25 1.25 0 1 0-1.768 1.768l3.386 3.385A6.251 6.251 0 0 0 4 15.25v17.5A6.25 6.25 0 0 0 10.25 39h17.5a6.251 6.251 0 0 0 5.73-3.752zm-2.011-2.011A3.75 3.75 0 0 1 27.75 36.5h-17.5a3.75 3.75 0 0 1-3.75-3.75v-17.5a3.75 3.75 0 0 1 3.264-3.719l21.705 21.705zM31.5 15.25v10.947l9.16 9.16c1.546.56 3.34-.558 3.34-2.36V15.003c0-2.082-2.397-3.252-4.039-1.97L34 17.688V15.25A6.25 6.25 0 0 0 27.75 9H14.303l2.5 2.5H27.75a3.75 3.75 0 0 1 3.75 3.75zm2.5 5.61l7.5-5.858v17.995L34 27.14v-6.28z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoOff48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
