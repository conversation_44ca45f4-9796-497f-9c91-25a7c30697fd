import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2.854 2.146l15 15a.5.5 0 0 1-.708.708l-1.269-1.27c-.395.263-.87.416-1.38.416h-9a2.5 2.5 0 0 1-2.5-2.5v-9c0-.51.153-.985.415-1.38L2.146 2.854a.5.5 0 1 1 .708-.708zm12.292 13.707L10 10.707V12.5a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-4a.5.5 0 0 1 .5-.5h1.793l-1-1H3.997v7.5a1.5 1.5 0 0 0 1.5 1.5h9c.232 0 .452-.053.649-.147zM5.293 6L4.144 4.851a1.494 1.494 0 0 0-.147.649V6h1.296zm3 3H6v3h3V9.707L8.293 9zm7.704-3H8.121l1 1h6.876v6.876l.976.975c.016-.115.024-.232.024-.351V7H17V6h-.003v-.5a2.5 2.5 0 0 0-2.5-2.5h-9c-.12 0-.236.008-.351.024L6.12 4h8.376a1.5 1.5 0 0 1 1.5 1.5V6z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowAdOff20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
