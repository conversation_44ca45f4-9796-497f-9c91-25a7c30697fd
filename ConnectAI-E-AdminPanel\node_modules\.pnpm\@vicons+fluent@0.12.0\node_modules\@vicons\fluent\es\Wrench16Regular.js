import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M7 5a4 4 0 0 1 5.445-3.73a.5.5 0 0 1 .173.819L10.708 4L12 5.293l1.911-1.91a.5.5 0 0 1 .82.172a4 4 0 0 1-4.829 5.292L4.896 13.92a1.986 1.986 0 0 1-2.843-2.774l5.051-5.234A4.01 4.01 0 0 1 7 5zm4-3a3 3 0 0 0-2.862 3.903a.5.5 0 0 1-.117.498L2.773 11.84a.986.986 0 0 0 1.411 1.377l5.224-5.293a.5.5 0 0 1 .533-.116a3 3 0 0 0 4.046-3.088l-1.633 1.634a.5.5 0 0 1-.707 0l-2-2a.5.5 0 0 1 0-.707l1.633-1.634A3.042 3.042 0 0 0 11 2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wrench16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
