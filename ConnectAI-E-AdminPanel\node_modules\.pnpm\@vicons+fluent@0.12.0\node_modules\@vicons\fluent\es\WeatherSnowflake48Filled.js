import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10.078 32.091l-.11.115a1.75 1.75 0 0 1-2.62-2.3l.1-.125L11.21 25.5H4.75a1.75 1.75 0 0 1-.144-3.494L4.75 22h6.455l-3.757-4.282l-.1-.124a1.75 1.75 0 0 1 2.62-2.3l.11.116L15.863 22H22v-6.128l-6.591-5.796a1.75 1.75 0 0 1 2.186-2.728l.125.1L22 11.21V4.75a1.75 1.75 0 0 1 3.494-.144l.006.144v6.462l4.28-3.76l.125-.1a1.75 1.75 0 0 1 2.3 2.619l-.114.11l-6.591 5.79V22h6.642l5.797-6.591a1.75 1.75 0 0 1 2.728 2.186l-.1.125L36.803 22h6.447a1.75 1.75 0 0 1 .144 3.494l-.144.006h-6.448l3.761 4.28a1.75 1.75 0 0 1-2.519 2.426l-.11-.115l-5.791-6.591H25.5v6.635l6.591 5.791a1.75 1.75 0 0 1-2.186 2.73l-.124-.1l-4.281-3.761v6.455a1.75 1.75 0 0 1-3.494.144L22 43.25v-6.459l-4.28 3.764l-.125.1a1.75 1.75 0 0 1-2.301-2.618l.115-.11L22 32.13V25.5h-6.132l-5.79 6.591z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSnowflake48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
