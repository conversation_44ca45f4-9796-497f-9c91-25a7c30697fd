import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11.75 2a.75.75 0 0 1 .743.648l.007.102l-.001 3.346l2.172-1.908a.75.75 0 0 1 .99 1.127L12.5 8.093l-.001 2.906h2.909l2.78-3.16a.75.75 0 0 1 .97-.136l.088.067a.75.75 0 0 1 .135.97l-.067.089l-1.908 2.169h3.345a.75.75 0 0 1 .743.649l.007.102a.75.75 0 0 1-.648.743l-.102.007h-3.348l1.909 2.172a.75.75 0 0 1 .01.98l-.078.079a.75.75 0 0 1-.98.01l-.079-.078l-2.779-3.163l-2.908-.001l.001 2.908l3.164 2.78a.75.75 0 0 1 .135.97l-.067.088a.75.75 0 0 1-.97.136l-.088-.067l-2.174-1.91l-.001 3.347a.75.75 0 0 1-.649.743l-.101.007a.75.75 0 0 1-.743-.648l-.007-.102l-.001-3.344l-2.167 1.907a.75.75 0 0 1-.99-1.127l3.158-2.778v-2.909H8.312l-2.997 3.412a.75.75 0 0 1-1.127-.99L6.316 12.5L2.75 12.5a.75.75 0 0 1-.743-.648L2 11.75a.75.75 0 0 1 .648-.743L2.75 11h3.124L4.188 9.078a.75.75 0 0 1 1.127-.99L7.87 11h3.128V8.091l-3.16-2.778a.75.75 0 0 1-.135-.97l.067-.089a.75.75 0 0 1 .97-.135l.089.067l2.17 1.908L11 2.75a.75.75 0 0 1 .55-.723l.098-.02L11.75 2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSnowflake24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
