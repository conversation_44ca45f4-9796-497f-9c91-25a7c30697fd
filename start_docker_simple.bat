@echo off
chcp 65001 >nul
echo 🐳 ConnectAI Docker开发环境快速启动
echo =====================================

echo.
echo 🔍 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未安装或不可用
    echo 请先安装Docker Desktop: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)
echo ✅ Docker环境正常

echo.
echo 📁 准备数据目录...
if not exist "manager-server\data\mysql\data" mkdir "manager-server\data\mysql\data"
if not exist "manager-server\data\mysql\conf.d" mkdir "manager-server\data\mysql\conf.d"
if not exist "manager-server\data\redis" mkdir "manager-server\data\redis"
if not exist "manager-server\data\rabbitmq" mkdir "manager-server\data\rabbitmq"
if not exist "data\files" mkdir "data\files"
echo ✅ 数据目录准备完成

echo.
echo 🔨 构建Docker镜像...
cd manager-server
docker images | findstr connectai-manager >nul
if errorlevel 1 (
    echo 正在构建镜像，这可能需要几分钟...
    make build
    if errorlevel 1 (
        echo ❌ 镜像构建失败
        pause
        exit /b 1
    )
    echo ✅ 镜像构建成功
) else (
    echo ✅ 镜像已存在
)

echo.
echo 🚀 启动Docker服务...
docker-compose -f docker-compose-local.yml down >nul 2>&1
docker-compose -f docker-compose-local.yml up -d
if errorlevel 1 (
    echo ❌ 服务启动失败
    pause
    exit /b 1
)

echo ✅ Docker服务启动成功
cd ..

echo.
echo ⏳ 等待服务启动完成...
timeout /t 10 /nobreak >nul

echo.
echo =====================================
echo 🎉 ConnectAI Docker开发环境启动成功！
echo =====================================
echo.
echo 📊 服务地址:
echo   • Manager Server:   http://localhost:3000
echo   • MySQL数据库:      localhost:3306
echo   • Redis缓存:        localhost:6379
echo   • RabbitMQ管理:     http://localhost:15672
echo     (用户名: rabbitmq, 密码: rabbitmq)
echo.
echo 👤 预置账号:
echo   • 邮箱: <EMAIL>
echo   • 密码: admin123
echo.
echo 📝 下一步:
echo   1. 启动前端: cd ConnectAI-E-AdminPanel ^&^& pnpm run dev
echo   2. 访问前端: http://localhost:3200
echo   3. 开始开发和调试
echo.
echo 🔧 管理命令:
echo   • 查看日志: cd manager-server ^&^& docker-compose -f docker-compose-local.yml logs -f
echo   • 停止服务: cd manager-server ^&^& docker-compose -f docker-compose-local.yml down
echo   • 重启服务: cd manager-server ^&^& docker-compose -f docker-compose-local.yml restart
echo.
echo =====================================

pause
