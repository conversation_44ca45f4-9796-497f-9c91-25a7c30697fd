import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2.564 12.257l5.001-9a.5.5 0 0 1 .82-.078l.054.078l5.002 9a.5.5 0 0 1-.352.736l-.085.007H3a.5.5 0 0 1-.473-.665l.036-.078l5.001-9l-5.001 9zM8.002 4.53L3.851 12h8.303L8.002 4.53zm0 5.348a.623.623 0 1 1 0 1.246a.623.623 0 0 1 0-1.246zm0-2.874a.5.5 0 0 1 .492.41l.008.09L8.5 8.5a.5.5 0 0 1-.992.09L7.5 8.5v-.998a.5.5 0 0 1 .5-.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Warning16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
