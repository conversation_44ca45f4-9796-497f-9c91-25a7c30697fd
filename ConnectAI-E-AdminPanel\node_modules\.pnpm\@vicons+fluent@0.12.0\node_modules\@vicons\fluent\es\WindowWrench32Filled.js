import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M7.5 3A4.5 4.5 0 0 0 3 7.5v17A4.5 4.5 0 0 0 7.5 29h7.164a4.013 4.013 0 0 1 .26-2H7.5A2.5 2.5 0 0 1 5 24.5V11h22v3.685l.065.025C27.75 14.838 29 15.45 29 17V7.5A4.5 4.5 0 0 0 24.5 3h-17zm12.9 27.268a2.5 2.5 0 0 1-3.535-3.536l3.435-3.435a5.5 5.5 0 0 1 6.105-7.223c.368.061.48.506.215.77l-1.888 1.888a2.5 2.5 0 0 0 3.536 3.536l1.888-1.888c.264-.264.709-.153.77.215a5.5 5.5 0 0 1-7.022 6.17L20.4 30.268z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowWrench32Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
