import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M37.903 33.009c-4.971 8.61-15.98 11.559-24.589 6.588a17.934 17.934 0 0 1-5.821-5.367a1.35 1.35 0 0 1 .656-2.037c6.78-2.427 10.412-5.239 12.52-9.261c2.218-4.235 2.791-8.874 1.24-15.232a1.35 1.35 0 0 1 1.383-1.668c2.802.15 5.54.955 8.022 2.388c8.61 4.97 11.56 15.98 6.589 24.589z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherMoon48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
