# ConnectAI 本地部署配置文件
# 这是一个可以直接使用的本地开发配置

# ==================== 基础配置 ====================
COMPOSE_PROJECT_NAME=connectai-local
COMPOSE_FILE=production-replica-docker-compose.yml

# ==================== 数据库配置 ====================
# MySQL 配置
MYSQL_ROOT_PASSWORD=connectai2023
MYSQL_DATABASE_MANAGER=connectai-manager
MYSQL_DATABASE_PROXY=connectai-proxy
MYSQL_DATABASE_SERVER=connectai-server
MYSQL_DATABASE_PROXYALL=connectai-proxyall

# Redis 配置
REDIS_PASSWORD=

# ==================== AI API 配置 (本地测试用) ====================
# OpenAI 配置 - 如果您有真实的API密钥，请替换
OPENAI_API_KEY=sk-test-key-for-local-development
OPENAI_API_BASE=https://api.openai.com
OPENAI_API_VERSION=2023-03-15-preview

# Claude 配置 - 测试用
CLAUDE_API_KEY=test-claude-key
CLAUDE_API_BASE=https://api.anthropic.com

# 星火配置 - 测试用
XINGHUO_APP_ID=test-app-id
XINGHUO_API_SECRET=test-api-secret
XINGHUO_API_KEY=test-api-key

# 文心一言配置 - 测试用
WENXIN_API_KEY=test-wenxin-key
WENXIN_SECRET_KEY=test-wenxin-secret

# 智谱AI配置 - 测试用
ZHIPUAI_API_KEY=test-zhipuai-key

# 通义千问配置 - 测试用
QWEN_API_KEY=test-qwen-key

# 阿里云配置 - 测试用
ALIYUN_ACCESS_KEY_ID=test-access-key
ALIYUN_ACCESS_KEY_SECRET=test-access-secret

# 腾讯云混元配置 - 测试用
HUNYUAN_SECRET_ID=test-secret-id
HUNYUAN_SECRET_KEY=test-secret-key

# ==================== 企业应用配置 (本地测试用) ====================
# 飞书配置 - 测试用
FEISHU_APP_ID=test-feishu-app-id
FEISHU_APP_SECRET=test-feishu-secret
FEISHU_VERIFICATION_TOKEN=test-verification-token
FEISHU_ENCRYPT_KEY=test-encrypt-key

# 钉钉配置 - 测试用
DINGDING_APP_KEY=test-dingding-key
DINGDING_APP_SECRET=test-dingding-secret
DINGDING_AGENT_ID=test-agent-id

# 企业微信配置 - 测试用
WEWORK_CORP_ID=test-corp-id
WEWORK_CORP_SECRET=test-corp-secret
WEWORK_AGENT_ID=test-agent-id

# ==================== 系统配置 ====================
# 系统域名配置
SYSTEM_DOMAIN=http://localhost:8081
SYSTEM_LOGIN_URL=http://localhost:8081/login
SYSTEM_API_URL=http://manager:3000/api/code2session

# 文件上传配置
UPLOAD_PATH=/data/files
MAX_CONTENT_LENGTH=104867600

# Elasticsearch 配置 (降低内存使用)
ES_HOST=elasticsearch
ES_PORT=9200
ES_JAVA_OPTS=-Xms512m -Xmx512m

# RabbitMQ 配置
RABBITMQ_DEFAULT_USER=rabbitmq
RABBITMQ_DEFAULT_PASS=rabbitmq
RABBITMQ_DEFAULT_VHOST=/
RABBITMQ_ERLANG_COOKIE=SWQOKODSQALRPCLNMEQG

# ==================== 其他配置 ====================
# 时区配置
TZ=Asia/Shanghai

# 语言配置
LANG=zh_CN.UTF-8

# 调试模式
DEBUG=true

# JWT 密钥
JWT_SECRET_KEY=local-development-jwt-secret-key

# 加密密钥
ENCRYPT_KEY=local-development-encrypt-key
