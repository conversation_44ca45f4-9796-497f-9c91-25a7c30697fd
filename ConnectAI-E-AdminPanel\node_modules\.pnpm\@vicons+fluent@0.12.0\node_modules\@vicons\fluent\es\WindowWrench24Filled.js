import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.25 3A3.25 3.25 0 0 0 3 6.25v11.5A3.25 3.25 0 0 0 6.25 21h4.794c.092-.482.323-.942.696-1.314l.186-.186H6.25a1.75 1.75 0 0 1-1.75-1.75V8.5h15v2.532c.172.02.343.048.512.085c.47.102.814.412.988.791V6.25A3.25 3.25 0 0 0 17.75 3H6.25zm13.88 9.51c0-.19-.115-.37-.331-.416a4.067 4.067 0 0 0-3.752 1.099a4.074 4.074 0 0 0-.87 4.47l-2.73 2.73a1.527 1.527 0 1 0 2.16 2.16l2.73-2.73a4.075 4.075 0 0 0 5.57-4.622c-.078-.353-.508-.44-.763-.185l-1.905 1.904a1.535 1.535 0 0 1-.739.41a1.526 1.526 0 0 1-1.42-2.57l1.904-1.904a.49.49 0 0 0 .146-.346zM17.72 21a5.378 5.378 0 0 1-.116-.03l-.03.03h.146z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowWrench24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
