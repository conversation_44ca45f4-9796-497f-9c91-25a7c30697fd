import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M21.282 31.95l4.517-5.489a1.25 1.25 0 0 1 2.011 1.479l-.08.11l-2.835 3.444h4.858c1.01 0 1.586 1.124 1.044 1.937l-.073.1l-6.486 8.005a1.25 1.25 0 0 1-2.022-1.463l.08-.11l4.835-5.969h-4.884c-1.014 0-1.59-1.132-1.04-1.944l.075-.1l4.517-5.489l-4.517 5.489zM26 10.018c6.337 0 9.932 4.194 10.455 9.26h.16c4.078 0 7.384 3.298 7.384 7.365c0 4.068-3.306 7.365-7.384 7.365l-4.123.001A2.49 2.49 0 0 0 33 32.5c0-.35-.072-.684-.203-.987h4.007c2.594 0 4.697-2.114 4.697-4.721c0-2.608-2.103-4.722-4.697-4.722h-1.42c-.752 0-1.408-.592-1.408-1.346c0-4.824-3.714-8.211-7.975-8.211c-4.261 0-7.975 3.448-7.975 8.21c0 .755-.656 1.347-1.407 1.347h-1.421c-2.594 0-4.697 2.114-4.697 4.722c0 2.607 2.103 4.721 4.697 4.721h4.004c-.13.302-.202.636-.202.987c0 .567.189 1.09.506 1.51l-4.12-.002c-4.078 0-7.384-3.297-7.384-7.365c0-3.986 3.175-7.233 7.14-7.361l.404-.004c.526-5.099 4.118-9.26 10.455-9.26zM19.998 4a9.432 9.432 0 0 1 7.787 4.104a15.29 15.29 0 0 0-3.437-.017a6.649 6.649 0 0 0-4.35-1.625c-3.285 0-6.083 2.422-6.644 5.696l-.313 1.83a1 1 0 0 1-.986.831h-1.947c-2.063 0-3.735 1.71-3.735 3.821c0 1.106.459 2.102 1.193 2.8a9.272 9.272 0 0 0-1.044 2.092a6.564 6.564 0 0 1 4.04-11.737h.142A9.44 9.44 0 0 1 19.998 4z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherThunderstorm48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
