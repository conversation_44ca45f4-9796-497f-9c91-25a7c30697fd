# ConnectAI 远程服务器手动部署指南

## 前提条件

### 本地环境
- Docker
- Node.js 和 npm/pnpm
- SSH客户端

### 远程服务器 (ubuntu@************)
- Ubuntu 系统
- Docker 和 Docker Compose
- 至少 4GB 内存
- 至少 20GB 磁盘空间

## 部署步骤

### 1. 在本地构建项目

```bash
# 1.1 构建前端项目
cd ConnectAI-E-AdminPanel
pnpm install
pnpm run build
cd ..

cd Lark-Messenger-Web
pnpm install  
pnpm run build
cd ..

# 1.2 构建Docker镜像
docker build -t know-server:2.0.1-privatization -f ./DataChat-API/docker/Dockerfile ./DataChat-API
docker build -t connectai-manager:2.0.1-privatization -f ./manager-server/docker/Dockerfile ./manager-server

# 1.3 准备部署文件
rm -rf build
cp -r ./deploy build
mkdir -p ./build/dist
cp -r ./ConnectAI-E-AdminPanel/dist/* ./build/dist/
cp -r ./Lark-Messenger-Web/dist/* ./build/dist/
mkdir -p ./build/dist/upload

# 1.4 更新配置文件
sed -i 's/know-server:es/know-server:2.0.1-privatization/g' ./build/docker-compose.yml
sed -i 's/connectai-manager:1.0/connectai-manager:2.0.1-privatization/g' ./build/docker-compose.yml

# 1.5 打包部署文件
tar -zcf deploy-2.0.1-privatization.tar.gz build/

# 1.6 导出Docker镜像
docker save docker.elastic.co/elasticsearch/elasticsearch:8.9.0 lloydzhou/nchan jwilder/nginx-proxy:alpine mysql:5.7 rabbitmq:3.7-management-alpine redis:alpine | gzip > images-base.tar.gz

docker save know-server:2.0.1-privatization connectai-manager:2.0.1-privatization | gzip > images-2.0.1-privatization.tar.gz
```

### 2. 上传文件到服务器

```bash
# 2.1 创建远程目录
ssh ubuntu@************ "sudo mkdir -p /opt/connectai && sudo chown \$USER:\$USER /opt/connectai"

# 2.2 上传文件
scp deploy-2.0.1-privatization.tar.gz ubuntu@************:/opt/connectai/
scp images-base.tar.gz ubuntu@************:/opt/connectai/
scp images-2.0.1-privatization.tar.gz ubuntu@************:/opt/connectai/
```

### 3. 在远程服务器部署

```bash
# 3.1 连接到服务器
ssh ubuntu@************

# 3.2 安装Docker (如果未安装)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 3.3 安装Docker Compose (如果未安装)
sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 3.4 重新登录以应用Docker组权限
exit
ssh ubuntu@************

# 3.5 进入部署目录
cd /opt/connectai

# 3.6 解压部署包
tar -zxf deploy-2.0.1-privatization.tar.gz

# 3.7 加载Docker镜像
docker load < images-base.tar.gz
docker load < images-2.0.1-privatization.tar.gz

# 3.8 进入部署目录
cd build

# 3.9 创建数据目录
mkdir -p data/{mysql/data,mysql/conf.d,rabbitmq,elasticsearch,redis,files,search_index}

# 3.10 设置Elasticsearch目录权限
sudo chown -R 1000:1000 data/elasticsearch
sudo chmod -R 755 data

# 3.11 启动服务
docker-compose up -d

# 3.12 查看服务状态
docker-compose ps
docker-compose logs
```

## 服务访问地址

部署完成后，可以通过以下地址访问服务：

- **管理面板**: http://************:8080
- **API服务**: http://************:8081  
- **知识库服务**: http://************:8082

## 常用管理命令

```bash
# 查看服务状态
cd /opt/connectai/build
docker-compose ps

# 查看服务日志
docker-compose logs [service_name]

# 重启服务
docker-compose restart [service_name]

# 停止所有服务
docker-compose down

# 启动所有服务
docker-compose up -d

# 更新服务
docker-compose pull
docker-compose up -d
```

## 故障排除

### 1. 端口冲突
如果端口被占用，可以修改 `docker-compose.yml` 中的端口映射：

```yaml
ports:
  - "8080:80"  # 改为其他端口，如 "9080:80"
```

### 2. 内存不足
如果服务器内存不足，可以调整Elasticsearch的内存设置：

```yaml
environment:
  - "ES_JAVA_OPTS=-Xms256m -Xmx256m"  # 减少内存使用
```

### 3. 数据持久化
确保数据目录有正确的权限：

```bash
sudo chown -R 1000:1000 /opt/connectai/build/data/elasticsearch
sudo chmod -R 755 /opt/connectai/build/data
```

### 4. 防火墙设置
确保服务器防火墙允许相应端口：

```bash
sudo ufw allow 8080
sudo ufw allow 8081
sudo ufw allow 8082
```

## 备份和恢复

### 备份数据
```bash
cd /opt/connectai/build
tar -zcf backup-$(date +%Y%m%d).tar.gz data/
```

### 恢复数据
```bash
cd /opt/connectai/build
docker-compose down
tar -zxf backup-YYYYMMDD.tar.gz
docker-compose up -d
```

## 监控和维护

### 查看系统资源使用
```bash
# 查看Docker容器资源使用
docker stats

# 查看磁盘使用
df -h

# 查看内存使用
free -h
```

### 日志管理
```bash
# 清理Docker日志
docker system prune -f

# 查看特定服务日志
docker-compose logs -f manager
```
