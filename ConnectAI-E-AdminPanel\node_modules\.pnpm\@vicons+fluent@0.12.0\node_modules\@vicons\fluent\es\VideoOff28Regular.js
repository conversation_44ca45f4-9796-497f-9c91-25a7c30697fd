import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.28 2.22a.75.75 0 1 0-1.06 1.06L4.52 5.582A3.251 3.251 0 0 0 2 8.75v10.5a3.25 3.25 0 0 0 3.25 3.25h10.502a3.251 3.251 0 0 0 3.168-2.52l5.8 5.8a.75.75 0 0 0 1.06-1.06L3.28 2.22zm14.222 16.343v.687a1.75 1.75 0 0 1-1.75 1.75H5.25a1.75 1.75 0 0 1-1.75-1.75V8.75C3.5 7.784 4.284 7 5.25 7h.69l11.562 11.563zM8.682 5.5l1.5 1.5h5.57c.967 0 1.75.784 1.75 1.75v5.57l1.5 1.5v-3.675l5-3.419v10.55l-4.9-3.355l5.08 5.08c.674.036 1.32-.49 1.32-1.25V8.252a1.25 1.25 0 0 0-1.956-1.031l-4.544 3.107V8.75a3.25 3.25 0 0 0-3.25-3.25h-7.07z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoOff28Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
