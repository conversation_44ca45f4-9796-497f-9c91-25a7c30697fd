import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4 5.75A2.75 2.75 0 0 1 6.75 3h6.5A2.75 2.75 0 0 1 16 5.75v.541l1.99-1.522c.823-.63 2.01-.043 2.01.993v6.476c0 1.036-1.187 1.622-2.01.993L16 11.71v.541A2.75 2.75 0 0 1 13.25 15h-6.5A2.75 2.75 0 0 1 4 12.25v-6.5zM6.75 4.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h6.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25h-6.5zM16 9.82l2.5 1.912V6.268L16 8.179v1.642zm2.363 6.318c-.95-.232-2.13-.413-3.458-.522a3.767 3.767 0 0 0 1.508-1.35c.847.109 1.624.249 2.305.414c.868.211 1.623.474 2.179.794c.5.288 1.103.776 1.103 1.526c0 .47-.245.839-.517 1.099c-.271.259-.628.47-1.017.646c-.782.355-1.854.64-3.092.848a.75.75 0 0 1-.249-1.48c1.179-.198 2.107-.455 2.721-.734c.31-.14.5-.268.6-.365l.01-.008l.006-.006a1.43 1.43 0 0 0-.313-.226c-.38-.218-.98-.44-1.786-.636zm2.146.804l.002-.003l-.002.003zM3.103 15.474a6.369 6.369 0 0 1 1.091-.48c.58.541 1.333.9 2.166.986c-.253.05-.495.102-.723.158c-.807.196-1.406.418-1.786.636c-.176.101-.267.178-.313.226c.046.048.137.125.313.226c.38.218.98.44 1.786.636c1.588.387 3.815.634 6.304.638l-.719-.72a.75.75 0 1 1 1.062-1.06l1.997 2a.75.75 0 0 1 0 1.06l-1.998 1.997a.75.75 0 0 1-1.06-1.06l.716-.717c-2.572-.004-4.923-.259-6.657-.68c-.868-.211-1.623-.474-2.179-.794C2.603 18.238 2 17.75 2 17s.603-1.238 1.103-1.526z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoSwitch24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
