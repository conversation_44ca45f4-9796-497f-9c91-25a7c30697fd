import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10.5 7.751a5.75 5.75 0 0 1 8.38-5.115a.75.75 0 0 1 .186 1.197L16.301 6.6l1.06 1.06l2.779-2.778a.75.75 0 0 1 1.193.18a5.75 5.75 0 0 1-6.422 8.284l-7.365 7.618a3.05 3.05 0 0 1-4.387-4.24l7.475-7.734a5.766 5.766 0 0 1-.134-1.238zm5.75-4.25a4.25 4.25 0 0 0-4.067 5.488a.75.75 0 0 1-.178.74l-7.768 8.036a1.55 1.55 0 1 0 2.23 2.155l7.676-7.94a.75.75 0 0 1 .775-.192a4.25 4.25 0 0 0 5.466-5.03l-2.492 2.493a.75.75 0 0 1-1.061 0L14.71 7.129a.75.75 0 0 1 0-1.06l2.466-2.467a4.268 4.268 0 0 0-.926-.101z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wrench24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
