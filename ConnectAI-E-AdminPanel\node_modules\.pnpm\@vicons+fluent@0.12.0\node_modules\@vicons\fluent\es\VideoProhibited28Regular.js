import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5.25 6A3.25 3.25 0 0 0 2 9.25v10.5A3.25 3.25 0 0 0 5.25 23h8.177a7.451 7.451 0 0 1-.36-1.5H5.25a1.75 1.75 0 0 1-1.75-1.75V9.25c0-.966.784-1.75 1.75-1.75h10.502c.967 0 1.75.784 1.75 1.75v4.373a7.44 7.44 0 0 1 1.5-.473v-.505L24 9.223v4.642a7.518 7.518 0 0 1 1.5 1.045V8.748a1.25 1.25 0 0 0-1.956-1.03l-4.542 3.11V9.25A3.25 3.25 0 0 0 15.752 6H5.25zM20.5 27a6.5 6.5 0 1 0 0-13a6.5 6.5 0 0 0 0 13zm0-1.5a4.978 4.978 0 0 1-2.965-.974l6.991-6.991A5 5 0 0 1 20.5 25.5zm2.965-9.026l-6.991 6.991a5 5 0 0 1 6.991-6.991z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoProhibited28Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
