import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M25.996 15.998c6.337 0 9.932 4.194 10.454 9.26h.16c4.078 0 7.384 3.298 7.384 7.365c0 4.068-3.306 7.365-7.384 7.365H15.38c-4.077 0-7.383-3.297-7.383-7.365c0-4.067 3.306-7.365 7.384-7.365h.16c.526-5.099 4.117-9.26 10.455-9.26zM7.569 24.191a1.75 1.75 0 0 1-.499 2.299l-.142.091l-1.299.75a1.75 1.75 0 0 1-1.892-2.94l.142-.091l1.3-.75a1.75 1.75 0 0 1 2.39.64zm14.136-9.54c-3.801 1.22-6.509 4.09-7.62 7.921l-.094.34l-.116.476l-.412.077a9.278 9.278 0 0 0-3.342 1.431A7.883 7.883 0 0 1 21.705 14.65zm-16.2-.672l.132.055l1.36.634a1.75 1.75 0 0 1-1.347 3.228l-.132-.056l-1.36-.634a1.75 1.75 0 0 1 1.347-3.227zm19.11-5.762a1.75 1.75 0 0 1 .508 2.316l-.078.121l-.86 1.229a1.75 1.75 0 0 1-2.945-1.887l.078-.12l.86-1.23a1.75 1.75 0 0 1 2.438-.43zm-10.291-.42l.065.156l.513 1.41a1.75 1.75 0 0 1-3.224 1.353l-.065-.156l-.513-1.41a1.75 1.75 0 0 1 3.224-1.352z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherPartlyCloudyDay48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
