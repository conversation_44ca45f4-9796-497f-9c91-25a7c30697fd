import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.784 10.708a8.353 8.353 0 0 1 1.504 2.085a1 1 0 0 1-1.78.91a6.356 6.356 0 0 0-1.138-1.58a6.128 6.128 0 0 0-9.8 1.562a1 1 0 0 1-1.784-.903a8.128 8.128 0 0 1 12.998-2.074zM15.735 13.7c.46.46.838 1.024 1.102 1.624a1 1 0 0 1-1.832.803a3.359 3.359 0 0 0-.684-1.013a3.233 3.233 0 0 0-4.572 0a3.257 3.257 0 0 0-.672 1a1 1 0 1 1-1.832-.802a5.25 5.25 0 0 1 1.09-1.612a5.233 5.233 0 0 1 7.4 0zm4.684-5.401c.508.508.987 1.087 1.404 1.691a1 1 0 0 1-1.646 1.136c-.35-.506-.752-.993-1.172-1.413c-3.872-3.872-10.15-3.872-14.023 0c-.399.399-.797.886-1.16 1.41a1 1 0 0 1-1.644-1.14c.428-.618.901-1.195 1.39-1.684c4.653-4.654 12.198-4.654 16.851 0zm-7.357 8.142a1.501 1.501 0 1 1-2.123 2.123a1.501 1.501 0 0 1 2.123-2.123z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wifi124Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
