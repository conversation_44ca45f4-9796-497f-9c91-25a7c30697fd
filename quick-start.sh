#!/bin/bash

# ConnectAI 生产环境复刻快速启动脚本
# 用于验证环境配置和快速部署核心服务

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
COMPOSE_FILE="$SCRIPT_DIR/production-replica-docker-compose.yml"
ENV_FILE="$SCRIPT_DIR/.env.production"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
echo_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }
echo_debug() { echo -e "${BLUE}[DEBUG]${NC} $1"; }

echo "🚀 ConnectAI 生产环境复刻 - 快速启动"
echo "========================================"

# 检查系统要求
check_system_requirements() {
    echo_info "检查系统要求..."
    
    # 检查内存
    local mem_gb=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$mem_gb" -lt 4 ]; then
        echo_warn "系统内存 ${mem_gb}GB，推荐至少 8GB"
    else
        echo_info "系统内存: ${mem_gb}GB ✓"
    fi
    
    # 检查磁盘空间
    local disk_gb=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "$disk_gb" -lt 20 ]; then
        echo_warn "可用磁盘空间 ${disk_gb}GB，推荐至少 50GB"
    else
        echo_info "可用磁盘空间: ${disk_gb}GB ✓"
    fi
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo_error "Docker 未安装"
        exit 1
    fi
    echo_info "Docker: $(docker --version | cut -d' ' -f3 | cut -d',' -f1) ✓"
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo_error "Docker Compose 未安装"
        exit 1
    fi
    echo_info "Docker Compose: $(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1) ✓"
}

# 检查端口占用
check_port_conflicts() {
    echo_info "检查端口占用..."
    
    local ports=(8081 8086 10001 49490 49642 50094 50344 53306)
    local conflicts=()
    
    for port in "${ports[@]}"; do
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            conflicts+=($port)
        fi
    done
    
    if [ ${#conflicts[@]} -gt 0 ]; then
        echo_warn "以下端口被占用: ${conflicts[*]}"
        echo_warn "请停止相关服务或修改配置文件中的端口映射"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        echo_info "端口检查通过 ✓"
    fi
}

# 验证环境配置
validate_environment() {
    echo_info "验证环境配置..."
    
    if [ ! -f "$ENV_FILE" ]; then
        echo_warn "环境变量文件不存在，创建默认配置..."
        cp "$SCRIPT_DIR/.env.production" "$ENV_FILE"
        echo_warn "请编辑 $ENV_FILE 配置您的API密钥"
        return 1
    fi
    
    # 检查关键配置
    local missing_configs=()
    
    if ! grep -q "OPENAI_API_KEY=sk-" "$ENV_FILE" 2>/dev/null; then
        missing_configs+=("OPENAI_API_KEY")
    fi
    
    if ! grep -q "MYSQL_ROOT_PASSWORD=" "$ENV_FILE" | grep -v "your_" 2>/dev/null; then
        missing_configs+=("MYSQL_ROOT_PASSWORD")
    fi
    
    if [ ${#missing_configs[@]} -gt 0 ]; then
        echo_warn "以下配置需要设置: ${missing_configs[*]}"
        echo_warn "请编辑 $ENV_FILE 文件"
        return 1
    fi
    
    echo_info "环境配置验证通过 ✓"
    return 0
}

# 启动核心服务
start_core_services() {
    echo_info "启动核心服务..."
    
    # 创建网络
    docker network create connectai-network 2>/dev/null || true
    
    # 启动基础设施服务
    echo_info "启动基础设施服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d \
        mysql-manager redis-manager elasticsearch rabbitmq
    
    echo_info "等待基础设施服务启动..."
    sleep 30
    
    # 启动代理服务
    echo_info "启动代理服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d \
        proxy-manager nchan
    
    # 启动知识服务器
    echo_info "启动知识服务器..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d \
        know-server
    
    # 启动管理服务
    echo_info "启动管理服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d \
        manager-1 admin
    
    echo_info "等待服务启动..."
    sleep 20
}

# 健康检查
health_check() {
    echo_info "执行健康检查..."
    
    local services=(
        "mysql-manager:3306"
        "redis-manager:6379"
        "elasticsearch:9200"
        "rabbitmq:5672"
        "know-server:80"
    )
    
    local failed_services=()
    
    for service in "${services[@]}"; do
        local name=$(echo $service | cut -d: -f1)
        local port=$(echo $service | cut -d: -f2)
        
        if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T $name timeout 5 bash -c "echo > /dev/tcp/localhost/$port" 2>/dev/null; then
            echo_info "$name 健康 ✓"
        else
            echo_warn "$name 可能未就绪"
            failed_services+=($name)
        fi
    done
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        echo_warn "以下服务可能存在问题: ${failed_services[*]}"
        echo_warn "请检查日志: docker-compose logs [service_name]"
    else
        echo_info "所有核心服务健康 ✓"
    fi
}

# 显示访问信息
show_quick_access() {
    echo_info "快速访问信息："
    echo "=================================="
    echo "🌐 主要服务："
    echo "   管理后台: http://localhost:50344"
    echo "   知识服务: http://localhost:8086"
    echo "   Nginx代理: http://localhost:8081"
    echo ""
    echo "🔧 管理工具："
    echo "   RabbitMQ: http://localhost:49490 (rabbitmq/rabbitmq)"
    echo "   Elasticsearch: http://localhost:50094"
    echo ""
    echo "💾 数据库："
    echo "   MySQL: localhost:53306 (root/connectai2023)"
    echo "   Redis: localhost:49642"
    echo ""
    echo "📊 监控命令："
    echo "   查看状态: docker-compose ps"
    echo "   查看日志: docker-compose logs [service]"
    echo "   完整部署: ./deploy-production-replica.sh start"
    echo "=================================="
}

# 主函数
main() {
    echo_info "开始快速启动流程..."
    
    # 系统检查
    check_system_requirements
    check_port_conflicts
    
    # 环境验证
    if ! validate_environment; then
        echo_error "环境配置不完整，请先配置环境变量"
        echo_info "编辑文件: $ENV_FILE"
        exit 1
    fi
    
    # 启动核心服务
    start_core_services
    
    # 健康检查
    health_check
    
    # 显示访问信息
    show_quick_access
    
    echo_info "快速启动完成！"
    echo_info "如需启动完整环境，请运行: ./deploy-production-replica.sh start"
}

# 清理函数
cleanup() {
    echo_info "清理快速启动的服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down
    echo_info "清理完成"
}

# 处理中断信号
trap cleanup EXIT

# 检查参数
case "${1:-start}" in
    start)
        main
        ;;
    stop)
        cleanup
        ;;
    check)
        check_system_requirements
        check_port_conflicts
        validate_environment
        ;;
    *)
        echo "用法: $0 [start|stop|check]"
        echo "  start - 快速启动核心服务 (默认)"
        echo "  stop  - 停止快速启动的服务"
        echo "  check - 仅检查环境配置"
        exit 1
        ;;
esac
