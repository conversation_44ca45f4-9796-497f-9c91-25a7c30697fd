version: '3.8'

networks:
  connectai-network:
    driver: bridge

volumes:
  mysql_data_manager:
  mysql_data_proxy:
  mysql_data_server:
  mysql_data_proxyall:
  redis_data_manager:
  redis_data_server:
  redis_data_datachat:
  elasticsearch_data:
  rabbitmq_data:

services:
  # ==================== 基础设施服务 ====================

  # MySQL 数据库服务 (多个实例)
  mysql-manager:
    image: mysql:5.7
    container_name: manager-server_mysql_1
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: connectai2023
      MYSQL_DATABASE: connectai-manager
      TZ: Asia/Shanghai
    ports:
      - "53306:3306"
    volumes:
      - mysql_data_manager:/var/lib/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - connectai-network

  mysql-proxy:
    image: mysql:5.7
    container_name: field-base-proxy_mysql_1
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: connectai2023
      MYSQL_DATABASE: connectai-proxy
      TZ: Asia/Shanghai
    ports:
      - "49979:3306"
    volumes:
      - mysql_data_proxy:/var/lib/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - connectai-network

  mysql-server:
    image: mysql:5.7
    container_name: field-base-server_mysql_1
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: connectai2023
      MYSQL_DATABASE: connectai-server
      TZ: Asia/Shanghai
    ports:
      - "49982:3306"
    volumes:
      - mysql_data_server:/var/lib/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - connectai-network

  mysql-proxyall:
    image: mysql:5.7
    container_name: proxyall_mysql_1
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: connectai2023
      MYSQL_DATABASE: connectai-proxyall
      TZ: Asia/Shanghai
    ports:
      - "50294:3306"
    volumes:
      - mysql_data_proxyall:/var/lib/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - connectai-network

  # Redis 服务 (多个实例)
  redis-manager:
    image: redis:alpine
    container_name: manager-server_redis_1
    restart: always
    ports:
      - "49642:6379"
    volumes:
      - redis_data_manager:/data
    networks:
      - connectai-network

  redis-server:
    image: redis:alpine
    container_name: field-base-server_redis_1
    restart: always
    ports:
      - "49978:6379"
    volumes:
      - redis_data_server:/data
    networks:
      - connectai-network

  redis-datachat:
    image: redis:alpine
    container_name: datachat-api-es-1_redis_1
    restart: always
    volumes:
      - redis_data_datachat:/data
    networks:
      - connectai-network

  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
    container_name: datachat-api-es-1_elasticsearch_1
    restart: always
    environment:
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "50094:9200"
      - "50093:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - connectai-network

  # RabbitMQ
  rabbitmq:
    image: rabbitmq:3.7-management-alpine
    container_name: manager-server_rabbitmq_1
    restart: always
    environment:
      RABBITMQ_ERLANG_COOKIE: "SWQOKODSQALRPCLNMEQG"
      RABBITMQ_DEFAULT_USER: "rabbitmq"
      RABBITMQ_DEFAULT_PASS: "rabbitmq"
      RABBITMQ_DEFAULT_VHOST: "/"
    ports:
      - "49491:5672"
      - "49490:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - connectai-network

  # Nchan (消息推送)
  nchan:
    image: lloydzhou/nchan
    container_name: manager-server_nchan_1
    restart: always
    ports:
      - "49186:80"
    networks:
      - connectai-network

  # ==================== 代理服务 ====================

  # Nginx 代理 (多个实例)
  proxy-manager:
    image: jwilder/nginx-proxy:alpine
    container_name: manager-server_proxy_1
    restart: always
    ports:
      - "8081:80"
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
    networks:
      - connectai-network

  proxy-proxyall:
    image: jwilder/nginx-proxy:alpine
    container_name: proxyall_proxy_1
    restart: always
    ports:
      - "10001:80"
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
    networks:
      - connectai-network

  proxy-field-server:
    image: jwilder/nginx-proxy:alpine
    container_name: field-base-server_proxy_1
    restart: always
    ports:
      - "10011:80"
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
    networks:
      - connectai-network

  proxy-field-proxy:
    image: jwilder/nginx-proxy:alpine
    container_name: field-base-proxy_proxy_1
    restart: always
    ports:
      - "10012:80"
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
    networks:
      - connectai-network

  # ==================== ConnectAI 核心服务 ====================

  # 知识服务器
  know-server:
    image: know-server:es
    container_name: datachat-api-es-1_know-server_1
    restart: always
    ports:
      - "8086:80"
    environment:
      - FLASK_OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - FLASK_OPENAI_API_BASE=https://api.openai.com
      - FLASK_OPENAI_API_VERSION=2023-03-15-preview
      - FLASK_SYSTEM_DOMAIN=http://localhost:8081
      - FLASK_SYSTEM_LOGIN_URL=http://localhost:8081/login
      - FLASK_SYSTEM_URL=http://manager:3000/api/code2session
      - FLASK_UPLOAD_PATH=/data/files
      - FLASK_DOMAIN=http://know-server
      - FLASK_ES_HOST=elasticsearch
      - FLASK_ES_PORT=9200
      - FLASK_MAX_CONTENT_LENGTH=104867600
    volumes:
      - ./data/files:/data/files
    depends_on:
      - elasticsearch
      - redis-datachat
    networks:
      - connectai-network

  # ConnectAI 管理服务 (多个实例)
  manager-1:
    image: connectai-manager
    container_name: manager-server_manager_1
    restart: always
    ports:
      - "50345:3000"
    environment:
      - VIRTUAL_HOST=manager1.connect.ai
    depends_on:
      - mysql-manager
      - redis-manager
      - rabbitmq
    networks:
      - connectai-network

  manager-2:
    image: connectai-manager
    container_name: manager-server_manager_2
    restart: always
    ports:
      - "50346:3000"
    environment:
      - VIRTUAL_HOST=manager2.connect.ai
    depends_on:
      - mysql-manager
      - redis-manager
      - rabbitmq
    networks:
      - connectai-network

  # 管理后台
  admin:
    image: connectai-manager
    container_name: manager-server_admin_1
    restart: always
    ports:
      - "50344:3000"
    environment:
      - VIRTUAL_HOST=admin.connect.ai
    command: bash -c 'pip3 install gradio && python3 /server/admin.py'
    depends_on:
      - mysql-manager
      - redis-manager
    networks:
      - connectai-network

  # ==================== Consumer 服务 ====================

  # 飞书消费者 (多个实例)
  feishu-consumer-1:
    image: connectai-manager
    container_name: manager-server_feishuconsumer_1
    restart: always
    command: python3 /server/scripts/feishu_consumer.py
    depends_on:
      - mysql-manager
      - redis-manager
      - rabbitmq
    networks:
      - connectai-network

  feishu-consumer-2:
    image: connectai-manager
    container_name: manager-server_feishuconsumer_2
    restart: always
    command: python3 /server/scripts/feishu_consumer.py
    depends_on:
      - mysql-manager
      - redis-manager
      - rabbitmq
    networks:
      - connectai-network

  # 钉钉消费者
  dingding-consumer-1:
    image: connectai-manager
    container_name: manager-server_dingdingconsumer_1
    restart: always
    command: python3 /server/scripts/dingding_consumer.py
    depends_on:
      - mysql-manager
      - redis-manager
      - rabbitmq
    networks:
      - connectai-network

  dingding-consumer-2:
    image: connectai-manager
    container_name: manager-server_dingdingconsumer_2
    restart: always
    command: python3 /server/scripts/dingding_consumer.py
    depends_on:
      - mysql-manager
      - redis-manager
      - rabbitmq
    networks:
      - connectai-network

  # 企业微信消费者
  wework-consumer:
    image: connectai-manager
    container_name: manager-server_weworkconsumer_1
    restart: always
    command: python3 /server/scripts/wework_consumer.py
    depends_on:
      - mysql-manager
      - redis-manager
      - rabbitmq
    networks:
      - connectai-network

  # Messenger消费者
  messenger-consumer:
    image: connectai-manager
    container_name: manager-server_messengerconsumer_1
    restart: always
    command: python3 /server/scripts/messenger_consumer.py
    depends_on:
      - mysql-manager
      - redis-manager
      - rabbitmq
    networks:
      - connectai-network

  # 应用消费者 (多个实例)
  app-consumer-1:
    image: connectai-manager
    container_name: manager-server_appconsumer_1
    restart: always
    command: python3 /server/scripts/application_consumer.py
    depends_on:
      - mysql-manager
      - redis-manager
      - rabbitmq
    networks:
      - connectai-network

  app-consumer-2:
    image: connectai-manager
    container_name: manager-server_appconsumer_2
    restart: always
    command: python3 /server/scripts/application_consumer.py
    depends_on:
      - mysql-manager
      - redis-manager
      - rabbitmq
    networks:
      - connectai-network

  # ==================== AI 代理服务 ====================

  # OpenAI 代理
  proxy-openai:
    image: connectai-proxy-openai
    container_name: proxyall_connectai-proxy-openai_1
    restart: always
    ports:
      - "50314:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=openai.proxy.connect.ai
    networks:
      - connectai-network

  # Claude 代理
  proxy-claude:
    image: connectai-proxy-claude
    container_name: proxyall_connectai-proxy-claude_1
    restart: always
    ports:
      - "50320:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=claude.proxy.connect.ai
    networks:
      - connectai-network

  # GPT 代理
  proxy-gpts:
    image: connectai-proxy-gpts
    container_name: proxyall_connectai-proxy-gpts_1
    restart: always
    ports:
      - "50301:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=gpts.proxy.connect.ai
    networks:
      - connectai-network

  # 星火代理
  proxy-xinghuo:
    image: connectai-proxy-xinghuo
    container_name: proxyall_connectai-proxy-xinghuo_1
    restart: always
    ports:
      - "50295:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=xinghuo.proxy.connect.ai
    networks:
      - connectai-network

  # Stability AI 代理
  proxy-stability:
    image: connectai-proxy-stability
    container_name: proxyall_connectai-proxy-stability_1
    restart: always
    ports:
      - "50298:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=stability.proxy.connect.ai
    networks:
      - connectai-network

  # BibiGPT 代理
  proxy-bibigpt:
    image: connectai-proxy-bibigpt
    container_name: proxyall_connectai-proxy-bibigpt_1
    restart: always
    ports:
      - "50326:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=bibigpt.proxy.connect.ai
    networks:
      - connectai-network

  # SMS 代理
  proxy-sms:
    image: connectai-proxy-sms
    container_name: proxyall_connectai-proxy-sms_1
    restart: always
    ports:
      - "50308:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=sms.proxy.connect.ai
    networks:
      - connectai-network

  # 阿里云代理
  proxy-aliyun:
    image: connectai-proxy-aliyun
    container_name: proxyall_connectai-proxy-aliyun_1
    restart: always
    ports:
      - "50304:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=aliyun.proxy.connect.ai
    networks:
      - connectai-network

  # 通义千问代理
  proxy-qwen:
    image: connectai-proxy-qwen
    container_name: proxyall_connectai-proxy-qwen_1
    restart: always
    ports:
      - "50306:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=qwen.proxy.connect.ai
    networks:
      - connectai-network

  # 文心一言代理
  proxy-wenxin:
    image: connectai-proxy-wenxin
    container_name: proxyall_connectai-proxy-wenxin_1
    restart: always
    ports:
      - "50302:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=wenxin.proxy.connect.ai
    networks:
      - connectai-network

  # 智谱AI代理
  proxy-zhipuai:
    image: connectai-proxy-zhipuai
    container_name: proxyall_connectai-proxy-zhipuai_2
    restart: always
    ports:
      - "50323:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=zhipuai.proxy.connect.ai
    networks:
      - connectai-network

  # Gemini 代理
  proxy-gemini:
    image: connectai-proxy-gemini
    container_name: proxyall_connectai-proxy-gemini_1
    restart: always
    ports:
      - "50303:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=gemini.proxy.connect.ai
    networks:
      - connectai-network

  # 翻译代理
  proxy-translate:
    image: connectai-proxy-translate
    container_name: proxyall_connectai-proxy-translate_1
    restart: always
    ports:
      - "50296:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=translate.proxy.connect.ai
    networks:
      - connectai-network

  # 混元代理
  proxy-hunyuan:
    image: connectai-proxy-hunyuan
    container_name: proxyall_connectai-proxy-hunyuan_1
    restart: always
    ports:
      - "50307:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=hunyuan.proxy.connect.ai
    networks:
      - connectai-network

  # ElevenLabs 代理
  proxy-elevenlabs:
    image: connectai-proxy-elevenlabs
    container_name: proxyall_connectai-proxy-elevenlabs_1
    restart: always
    ports:
      - "50309:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=elevenlabs.proxy.connect.ai
    networks:
      - connectai-network

  # 百川代理
  proxy-baichuan:
    image: connectai-proxy-baichuan
    container_name: proxyall_connectai-proxy-baichuan_1
    restart: always
    ports:
      - "50318:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=baichuan.proxy.connect.ai
    networks:
      - connectai-network

  # 商汤代理
  proxy-sensenova:
    image: connectai-proxy-sensenova
    container_name: proxyall_connectai-proxy-sensenova_1
    restart: always
    ports:
      - "50305:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=sensenova.proxy.connect.ai
    networks:
      - connectai-network

  # RWKV 代理
  proxy-rwkv:
    image: connectai-proxy-rwkv
    container_name: proxyall_connectai-proxy-rwkv_1
    restart: always
    ports:
      - "50300:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=rwkv.proxy.connect.ai
    networks:
      - connectai-network

  # MiniMax 代理
  proxy-minimax:
    image: connectai-proxy-minimax
    container_name: proxyall_connectai-proxy-minimax_1
    restart: always
    ports:
      - "50297:10086"
    command: python3 /server/server.py
    environment:
      - VIRTUAL_HOST=minimax.proxy.connect.ai
    networks:
      - connectai-network

  # ==================== 其他服务 ====================

  # 证书管理服务
  certd:
    image: registry.cn-shenzhen.aliyuncs.com/handsfree/certd:latest
    container_name: certd
    restart: always
    ports:
      - "7001-7002:7001-7002"
    networks:
      - connectai-network

  # GeoIP API 服务
  geoip-api:
    image: rehiy/geoip-api
    container_name: geoip-api_geoip_1
    restart: always
    ports:
      - "20086:80"
    networks:
      - connectai-network

  # Lark 部署服务器
  lark-deploy-server:
    image: d.ai2e.cn:5000/lark-deploy-server:latest
    container_name: lark-deploy-server-git_server_1
    restart: always
    ports:
      - "7000:7000"
    networks:
      - connectai-network

  # 快捷服务器
  shortcut-server:
    image: shortcut-server
    container_name: field-base-server_server_1
    restart: always
    ports:
      - "50232:9999"
    command: python3 /server/server.py
    networks:
      - connectai-network

  # ProxyAll 管理服务
  proxyall-server:
    image: proxyall-server
    container_name: proxyall_server_1
    restart: always
    ports:
      - "50299:9999"
    command: python3 /server/server.py
    networks:
      - connectai-network

  # ProxyAll 管理后台
  proxyall-admin:
    image: proxyall-admin
    container_name: proxyall_admin_1
    restart: always
    ports:
      - "50312:9999"
    command: python /server/admin.py
    networks:
      - connectai-network
