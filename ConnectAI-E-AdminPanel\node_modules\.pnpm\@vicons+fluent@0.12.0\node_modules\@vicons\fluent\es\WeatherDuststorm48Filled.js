import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M16.5 35a4.5 4.5 0 1 1 0 9a4.5 4.5 0 0 1 0-9zm0 3.5a1 1 0 1 0 0 2a1 1 0 0 0 0-2zm-10.752-8a1.75 1.75 0 0 1-.143-3.494L5.748 27H37c.06 0 .12.003.178.01l.175-.01c1.73 0 3.147-1.447 3.147-3.25s-1.417-3.25-3.147-3.25c-1.338 0-2.447.777-2.817 1.98l-.048.175a1.75 1.75 0 1 1-3.403-.818C31.787 18.92 34.375 17 37.353 17C41.033 17 44 20.03 44 23.75s-2.968 6.75-6.647 6.75c-.06 0-.12-.003-.178-.009l-.175.01h-1.563c.363.762.566 1.612.566 2.509c0 3.346-2.605 6.01-6.003 6.01c-3.152 0-4.918-1.345-5.848-3.559a1.75 1.75 0 1 1 3.227-1.356c.415.988.975 1.415 2.621 1.415c1.447 0 2.503-1.08 2.503-2.51c0-1.31-1.11-2.421-2.54-2.505l-.173-.005H5.748zM22.5 5a8.5 8.5 0 0 1 .256 16.995l-.255.004L5.75 22a1.75 1.75 0 0 1-.144-3.494l.144-.006L22.5 18.5a5 5 0 0 0 0-9.999c-2.748 0-4.882 1.966-4.995 4.56l-.005.217a1.75 1.75 0 1 1-3.5 0C14 8.607 17.78 5 22.5 5zM38.5 4a4.5 4.5 0 1 1 0 9a4.5 4.5 0 0 1 0-9zM7.494 2.996a4.498 4.498 0 1 1 0 8.996a4.498 4.498 0 0 1 0-8.996zM38.5 7.5a1 1 0 1 0 0 2a1 1 0 0 0 0-2zM7.494 6.496a.998.998 0 1 0 0 1.996a.998.998 0 0 0 0-1.996z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherDuststorm48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
