import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5.25 6A3.25 3.25 0 0 0 2 9.25v10.5A3.25 3.25 0 0 0 5.25 23h8.177A7.503 7.503 0 0 1 18 13.427V9.25A3.25 3.25 0 0 0 14.75 6h-9.5zm19.998 2.753v5.941A7.469 7.469 0 0 0 20.5 13c-.34 0-.673.023-1 .066V11.5l3.612-3.628c.787-.79 2.136-.233 2.136.882zM20.5 27a6.5 6.5 0 1 0 0-13a6.5 6.5 0 0 0 0 13zm0-1.5a4.978 4.978 0 0 1-2.965-.974l6.991-6.991A5 5 0 0 1 20.5 25.5zm2.965-9.026l-6.991 6.991a5 5 0 0 1 6.991-6.991z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoProhibited28Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
