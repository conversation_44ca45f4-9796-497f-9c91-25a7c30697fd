import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M37.75 6c.69 0 1.25.56 1.25 1.25V9h1.75a1.25 1.25 0 0 1 0 2.5H39v1.75a1.25 1.25 0 0 1-2.5 0V11.5h-1.75a1.25 1.25 0 1 1 0-2.5h1.75V7.25c0-.69.56-1.25 1.25-1.25zM15 11.25a1.25 1.25 0 0 0-2.5 0V13h-1.75a1.25 1.25 0 1 0 0 2.5h1.75v1.75a1.25 1.25 0 1 0 2.5 0V15.5h1.75a1.25 1.25 0 0 0 0-2.5H15v-1.75zM33.75 30c.69 0 1.25.56 1.25 1.25V33h1.75a1.25 1.25 0 0 1 0 2.5H35v1.75a1.25 1.25 0 0 1-2.5 0V35.5h-1.75a1.25 1.25 0 1 1 0-2.5h1.75v-1.75c0-.69.56-1.25 1.25-1.25zm-7.86-14.596a4.75 4.75 0 1 1 6.717 6.718L12.113 42.616a4.75 4.75 0 0 1-6.717-6.718L25.89 15.404zM24.53 20.3L7.164 37.666a2.25 2.25 0 0 0 3.182 3.182l17.368-17.367l-3.182-3.182zm4.95 1.414l1.36-1.359a2.25 2.25 0 1 0-3.183-3.182L26.3 18.531l3.182 3.182z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wand48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
