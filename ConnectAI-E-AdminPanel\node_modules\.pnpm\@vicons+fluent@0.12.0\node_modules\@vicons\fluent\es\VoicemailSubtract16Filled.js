import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4 5.75a.75.75 0 1 1 1.5 0a.75.75 0 0 1-1.5 0zM9.25 4a1.75 1.75 0 0 1 1.586 1.01A5.468 5.468 0 0 1 13 5.6V2.5A1.5 1.5 0 0 0 11.5 1h-9A1.5 1.5 0 0 0 1 2.5V9a1.5 1.5 0 0 0 1.5 1.5H5c0-1.933.997-3.633 2.505-4.614A1.776 1.776 0 0 1 7.668 5H6.332A1.75 1.75 0 1 1 4.75 4h4.5zm-.618 1.325c.304-.11.621-.193.948-.248a.747.747 0 0 0-.948.248zM15 10.5a4.5 4.5 0 1 1-9 0a4.5 4.5 0 0 1 9 0zm-2 0a.5.5 0 0 0-.5-.5h-4a.5.5 0 0 0 0 1h4a.5.5 0 0 0 .5-.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VoicemailSubtract16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
