import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.502 36a2.5 2.5 0 1 1 0 5a2.5 2.5 0 0 1 0-5zm19.853-19c3.679 0 6.647 3.03 6.647 6.75s-2.968 6.75-6.647 6.75c-.06 0-.12-.003-.178-.009l-.175.009h-1.564c.364.763.567 1.612.567 2.51c0 3.346-2.605 6.01-6.003 6.01c-3.153 0-4.918-1.345-5.848-3.559a1.75 1.75 0 0 1 3.227-1.356c.415.988.975 1.415 2.62 1.415c1.448 0 2.504-1.08 2.504-2.51c0-1.31-1.11-2.421-2.54-2.505l-.173-.005H5.75a1.75 1.75 0 0 1-.144-3.494L5.75 27h31.252c.06 0 .12.003.178.009l.031-.003l.144-.006c1.73 0 3.147-1.447 3.147-3.25s-1.418-3.25-3.147-3.25c-1.338 0-2.447.777-2.817 1.98l-.048.175a1.75 1.75 0 0 1-3.403-.818c.702-2.918 3.29-4.837 6.268-4.837zM22.5 5a8.5 8.5 0 0 1 .255 16.995l-.255.004L5.751 22a1.75 1.75 0 0 1-.143-3.494l.144-.006l16.749-.001a5 5 0 1 0 0-9.999c-2.823 0-5 2.077-5 4.777a1.75 1.75 0 1 1-3.5 0c0-4.67 3.78-8.277 8.5-8.277zm15 3a2.5 2.5 0 1 1 0 5a2.5 2.5 0 0 1 0-5zm-30-2a2.5 2.5 0 1 1 0 5a2.5 2.5 0 0 1 0-5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherBlowingSnow48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
