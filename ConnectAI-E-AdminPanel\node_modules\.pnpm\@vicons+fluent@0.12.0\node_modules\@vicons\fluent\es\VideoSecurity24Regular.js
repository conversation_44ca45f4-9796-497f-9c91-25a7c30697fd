import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5.25 3A3.25 3.25 0 0 0 2 6.25v6.5A3.25 3.25 0 0 0 5.25 16h2.243a1.656 1.656 0 0 1-1.65 1.5H3.25a.75.75 0 0 0-.75.75v3c0 .414.336.75.75.75h3A5.75 5.75 0 0 0 12 16.25V16h1.75a3.25 3.25 0 0 0 3.235-2.934l3.88 2.327A.75.75 0 0 0 22 14.75V4.25a.75.75 0 0 0-1.136-.643l-3.88 2.327A3.25 3.25 0 0 0 13.75 3h-8.5zM17 7.675l3.5-2.1v7.85l-3.5-2.1v-3.65zM6.25 20.5H4V19h1.844c1.69 0 3.07-1.33 3.152-3H10.5v.25a4.25 4.25 0 0 1-4.25 4.25zM3.5 6.25c0-.966.784-1.75 1.75-1.75h8.5c.966 0 1.75.784 1.75 1.75v6.5a1.75 1.75 0 0 1-1.75 1.75h-8.5a1.75 1.75 0 0 1-1.75-1.75v-6.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoSecurity24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
