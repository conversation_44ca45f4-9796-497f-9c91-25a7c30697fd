import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3 6.25A3.25 3.25 0 0 1 6.25 3h11.5A3.25 3.25 0 0 1 21 6.25v2.76a3.3 3.3 0 0 0-.25-.01H19.5v-.5h-15v9.25c0 .966.784 1.75 1.75 1.75H9v1.25c0 .084.003.168.01.25H6.25A3.25 3.25 0 0 1 3 17.75V6.25zm9.25 9.25h3.25v-3.25A2.25 2.25 0 0 1 17.75 10h3A2.25 2.25 0 0 1 23 12.25v7.5A3.25 3.25 0 0 1 19.75 23h-7.5A2.25 2.25 0 0 1 10 20.75v-3a2.25 2.25 0 0 1 2.25-2.25zM17 12.25v3.25h4.5v-3.25a.75.75 0 0 0-.75-.75h-3a.75.75 0 0 0-.75.75zm-1.5 9.25V17h-3.25a.75.75 0 0 0-.75.75v3c0 .414.336.75.75.75h3.25zM17 17v4.5h2.75a1.75 1.75 0 0 0 1.75-1.75V17H17z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowApps24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
