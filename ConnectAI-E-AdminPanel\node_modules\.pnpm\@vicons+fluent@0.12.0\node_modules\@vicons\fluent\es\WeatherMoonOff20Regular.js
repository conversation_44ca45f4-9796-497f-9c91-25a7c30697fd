import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2.854 2.146a.5.5 0 1 0-.708.708L8.753 9.46a9.092 9.092 0 0 1-.035.069c-.935 1.783-2.545 3.03-5.551 4.106a.598.598 0 0 0-.292.903a7.985 7.985 0 0 0 12.142 1.187l2.13 2.129a.5.5 0 0 0 .707-.708l-2.165-2.164l-.712-.712l-5.022-5.022l-.772-.772l-6.33-6.33zM14.31 15.018a6.984 6.984 0 0 1-10.3-.63c2.732-1.048 4.436-2.326 5.482-4.19l4.818 4.82zM12.938 3.96a6.981 6.981 0 0 1 2.6 9.457l.732.731l.09-.151A7.981 7.981 0 0 0 9.88 2.035a.599.599 0 0 0-.613.74c.437 1.79.494 3.272.226 4.597l.828.828c.425-1.511.451-3.166.042-5.117c.898.119 1.77.413 2.575.877z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherMoonOff20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
