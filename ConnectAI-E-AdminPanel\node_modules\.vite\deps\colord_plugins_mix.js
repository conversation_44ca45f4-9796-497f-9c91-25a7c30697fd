import "./chunk-TFWDKVI3.js";

// node_modules/.pnpm/colord@2.9.3/node_modules/colord/plugins/mix.mjs
var t = function(t2, a2, n2) {
  return void 0 === a2 && (a2 = 0), void 0 === n2 && (n2 = 1), t2 > n2 ? n2 : t2 > a2 ? t2 : a2;
};
var a = function(t2) {
  var a2 = t2 / 255;
  return a2 < 0.04045 ? a2 / 12.92 : Math.pow((a2 + 0.055) / 1.055, 2.4);
};
var n = function(t2) {
  return 255 * (t2 > 31308e-7 ? 1.055 * Math.pow(t2, 1 / 2.4) - 0.055 : 12.92 * t2);
};
var r = 96.422;
var o = 100;
var u = 82.521;
var e = function(a2) {
  var r2, o2, u2 = { x: 0.9555766 * (r2 = a2).x + -0.0230393 * r2.y + 0.0631636 * r2.z, y: -0.0282895 * r2.x + 1.0099416 * r2.y + 0.0210077 * r2.z, z: 0.0122982 * r2.x + -0.020483 * r2.y + 1.3299098 * r2.z };
  return o2 = { r: n(0.032404542 * u2.x - 0.015371385 * u2.y - 4985314e-9 * u2.z), g: n(-969266e-8 * u2.x + 0.018760108 * u2.y + 41556e-8 * u2.z), b: n(556434e-9 * u2.x - 2040259e-9 * u2.y + 0.010572252 * u2.z), a: a2.a }, { r: t(o2.r, 0, 255), g: t(o2.g, 0, 255), b: t(o2.b, 0, 255), a: t(o2.a) };
};
var i = function(n2) {
  var e2 = a(n2.r), i2 = a(n2.g), p2 = a(n2.b);
  return function(a2) {
    return { x: t(a2.x, 0, r), y: t(a2.y, 0, o), z: t(a2.z, 0, u), a: t(a2.a) };
  }(function(t2) {
    return { x: 1.0478112 * t2.x + 0.0228866 * t2.y + -0.050127 * t2.z, y: 0.0295424 * t2.x + 0.9904844 * t2.y + -0.0170491 * t2.z, z: -92345e-7 * t2.x + 0.0150436 * t2.y + 0.7521316 * t2.z, a: t2.a };
  }({ x: 100 * (0.4124564 * e2 + 0.3575761 * i2 + 0.1804375 * p2), y: 100 * (0.2126729 * e2 + 0.7151522 * i2 + 0.072175 * p2), z: 100 * (0.0193339 * e2 + 0.119192 * i2 + 0.9503041 * p2), a: n2.a }));
};
var p = 216 / 24389;
var h = 24389 / 27;
var f = function(t2) {
  var a2 = i(t2), n2 = a2.x / r, e2 = a2.y / o, f2 = a2.z / u;
  return n2 = n2 > p ? Math.cbrt(n2) : (h * n2 + 16) / 116, { l: 116 * (e2 = e2 > p ? Math.cbrt(e2) : (h * e2 + 16) / 116) - 16, a: 500 * (n2 - e2), b: 200 * (e2 - (f2 = f2 > p ? Math.cbrt(f2) : (h * f2 + 16) / 116)), alpha: a2.a };
};
var c = function(a2, n2, i2) {
  var c2, y = f(a2), x = f(n2);
  return function(t2) {
    var a3 = (t2.l + 16) / 116, n3 = t2.a / 500 + a3, i3 = a3 - t2.b / 200;
    return e({ x: (Math.pow(n3, 3) > p ? Math.pow(n3, 3) : (116 * n3 - 16) / h) * r, y: (t2.l > 8 ? Math.pow((t2.l + 16) / 116, 3) : t2.l / h) * o, z: (Math.pow(i3, 3) > p ? Math.pow(i3, 3) : (116 * i3 - 16) / h) * u, a: t2.alpha });
  }({ l: t((c2 = { l: y.l * (1 - i2) + x.l * i2, a: y.a * (1 - i2) + x.a * i2, b: y.b * (1 - i2) + x.b * i2, alpha: y.alpha * (1 - i2) + x.alpha * i2 }).l, 0, 400), a: c2.a, b: c2.b, alpha: t(c2.alpha) });
};
function mix_default(t2) {
  function a2(t3, a3, n2) {
    void 0 === n2 && (n2 = 5);
    for (var r2 = [], o2 = 1 / (n2 - 1), u2 = 0; u2 <= n2 - 1; u2++)
      r2.push(t3.mix(a3, o2 * u2));
    return r2;
  }
  t2.prototype.mix = function(a3, n2) {
    void 0 === n2 && (n2 = 0.5);
    var r2 = a3 instanceof t2 ? a3 : new t2(a3), o2 = c(this.toRgb(), r2.toRgb(), n2);
    return new t2(o2);
  }, t2.prototype.tints = function(t3) {
    return a2(this, "#fff", t3);
  }, t2.prototype.shades = function(t3) {
    return a2(this, "#000", t3);
  }, t2.prototype.tones = function(t3) {
    return a2(this, "#808080", t3);
  };
}
export {
  mix_default as default
};
//# sourceMappingURL=colord_plugins_mix.js.map
