@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM ConnectAI 生产环境复刻部署脚本 (Windows版本)
REM 使用方法: deploy-production-replica.bat [action]
REM 支持的操作: start, stop, restart, status, logs, backup, help

set SCRIPT_DIR=%~dp0
set COMPOSE_FILE=%SCRIPT_DIR%production-replica-docker-compose.yml
set ENV_FILE=%SCRIPT_DIR%.env.production
set DATA_DIR=%SCRIPT_DIR%data
set BACKUP_DIR=%SCRIPT_DIR%backups

echo 🚀 ConnectAI 生产环境复刻部署脚本

REM 检查参数
set ACTION=%1
if "%ACTION%"=="" set ACTION=help

REM 检查依赖
:check_dependencies
echo 🔍 检查依赖...

where docker >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

where docker-compose >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose 未安装，请先安装 Docker Compose
    pause
    exit /b 1
)

echo ✅ 依赖检查完成
goto :action_%ACTION%

REM 初始化环境
:init_environment
echo 📁 初始化环境...

REM 创建数据目录
if not exist "%DATA_DIR%" mkdir "%DATA_DIR%"
if not exist "%DATA_DIR%\mysql" mkdir "%DATA_DIR%\mysql"
if not exist "%DATA_DIR%\mysql\manager" mkdir "%DATA_DIR%\mysql\manager"
if not exist "%DATA_DIR%\mysql\proxy" mkdir "%DATA_DIR%\mysql\proxy"
if not exist "%DATA_DIR%\mysql\server" mkdir "%DATA_DIR%\mysql\server"
if not exist "%DATA_DIR%\mysql\proxyall" mkdir "%DATA_DIR%\mysql\proxyall"
if not exist "%DATA_DIR%\redis" mkdir "%DATA_DIR%\redis"
if not exist "%DATA_DIR%\redis\manager" mkdir "%DATA_DIR%\redis\manager"
if not exist "%DATA_DIR%\redis\server" mkdir "%DATA_DIR%\redis\server"
if not exist "%DATA_DIR%\redis\datachat" mkdir "%DATA_DIR%\redis\datachat"
if not exist "%DATA_DIR%\elasticsearch" mkdir "%DATA_DIR%\elasticsearch"
if not exist "%DATA_DIR%\rabbitmq" mkdir "%DATA_DIR%\rabbitmq"
if not exist "%DATA_DIR%\files" mkdir "%DATA_DIR%\files"

REM 创建备份目录
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

REM 检查环境变量文件
if not exist "%ENV_FILE%" (
    echo ⚠️  环境变量文件不存在，请先配置 .env.production 文件
    echo 请编辑 %ENV_FILE% 配置您的API密钥和其他设置
    pause
    exit /b 1
)

echo ✅ 环境初始化完成
goto :eof

REM 启动服务
:action_start
call :check_dependencies
call :init_environment

echo 🚀 启动 ConnectAI 生产环境复刻...

echo 📥 拉取 Docker 镜像...
docker-compose -f "%COMPOSE_FILE%" --env-file "%ENV_FILE%" pull --ignore-pull-failures

echo 🔄 启动服务...
docker-compose -f "%COMPOSE_FILE%" --env-file "%ENV_FILE%" up -d

echo ⏳ 等待服务启动...
timeout /t 30 /nobreak >nul

echo 📊 检查服务状态...
docker-compose -f "%COMPOSE_FILE%" --env-file "%ENV_FILE%" ps

echo ✅ 服务启动完成！
call :show_access_info
goto :end

REM 停止服务
:action_stop
echo 🛑 停止 ConnectAI 服务...
docker-compose -f "%COMPOSE_FILE%" --env-file "%ENV_FILE%" down
echo ✅ 服务已停止
goto :end

REM 重启服务
:action_restart
echo 🔄 重启 ConnectAI 服务...
call :action_stop
timeout /t 5 /nobreak >nul
call :action_start
goto :end

REM 显示服务状态
:action_status
echo 📊 ConnectAI 服务状态：
docker-compose -f "%COMPOSE_FILE%" --env-file "%ENV_FILE%" ps
goto :end

REM 显示日志
:action_logs
set SERVICE=%2
if "%SERVICE%"=="" (
    echo 📋 显示所有服务日志...
    docker-compose -f "%COMPOSE_FILE%" --env-file "%ENV_FILE%" logs --tail=100
) else (
    echo 📋 显示 %SERVICE% 服务日志...
    docker-compose -f "%COMPOSE_FILE%" --env-file "%ENV_FILE%" logs --tail=100 %SERVICE%
)
goto :end

REM 备份数据
:action_backup
set BACKUP_NAME=connectai-backup-%date:~0,4%%date:~5,2%%date:~8,2%-%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_NAME=%BACKUP_NAME: =0%
set BACKUP_PATH=%BACKUP_DIR%\%BACKUP_NAME%

echo 💾 开始备份数据到 %BACKUP_PATH%...

if not exist "%BACKUP_PATH%" mkdir "%BACKUP_PATH%"

echo 📁 备份数据文件...
tar -czf "%BACKUP_PATH%\data.tar.gz" -C "%SCRIPT_DIR%" data\

echo 📄 备份配置文件...
copy "%ENV_FILE%" "%BACKUP_PATH%\" >nul
copy "%COMPOSE_FILE%" "%BACKUP_PATH%\" >nul

echo ✅ 备份完成: %BACKUP_PATH%
goto :end

REM 显示访问信息
:show_access_info
echo.
echo 🌐 服务访问信息：
echo ==================================
echo 🌐 Web 服务：
echo    管理后台: http://localhost:50344
echo    知识服务: http://localhost:8086
echo    代理管理: http://localhost:10001
echo.
echo 🔧 管理服务：
echo    Nginx 代理: http://localhost:8081
echo    RabbitMQ 管理: http://localhost:49490 (rabbitmq/rabbitmq)
echo    Elasticsearch: http://localhost:50094
echo.
echo 🤖 AI 代理服务：
echo    OpenAI 代理: http://localhost:50314
echo    Claude 代理: http://localhost:50320
echo    星火代理: http://localhost:50295
echo    文心代理: http://localhost:50302
echo    智谱代理: http://localhost:50323
echo.
echo 💾 数据库服务：
echo    MySQL (Manager): localhost:53306
echo    MySQL (Proxy): localhost:49979
echo    MySQL (Server): localhost:49982
echo    MySQL (ProxyAll): localhost:50294
echo    Redis (Manager): localhost:49642
echo    Redis (Server): localhost:49978
echo.
echo 📊 监控命令：
echo    查看状态: %~nx0 status
echo    查看日志: %~nx0 logs [service_name]
echo    备份数据: %~nx0 backup
echo ==================================
goto :eof

REM 显示帮助信息
:action_help
echo.
echo ConnectAI 生产环境复刻部署脚本 (Windows版本)
echo.
echo 用法: %~nx0 [action] [options]
echo.
echo 支持的操作：
echo   start          启动所有服务
echo   stop           停止所有服务
echo   restart        重启所有服务
echo   status         显示服务状态
echo   logs [service] 显示日志 (可选指定服务名)
echo   backup         备份数据
echo   help           显示此帮助信息
echo.
echo 示例：
echo   %~nx0 start                    # 启动所有服务
echo   %~nx0 logs mysql-manager       # 查看管理数据库日志
echo   %~nx0 backup                   # 备份数据
echo.
echo 📝 注意事项：
echo   1. 请先配置 .env.production 文件中的API密钥
echo   2. 确保 Docker Desktop 正在运行
echo   3. 首次启动可能需要较长时间下载镜像
echo.
goto :end

REM 未知操作
:action_
echo ❌ 未知操作: %ACTION%
call :action_help
goto :end

REM 结束
:end
if "%ACTION%"=="help" (
    pause
)
exit /b 0
