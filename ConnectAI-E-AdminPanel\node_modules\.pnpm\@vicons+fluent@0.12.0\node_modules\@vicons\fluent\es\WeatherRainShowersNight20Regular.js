import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11.005 6.009c2.464 0 3.863 1.573 4.066 3.474h.062c1.586 0 2.872 1.237 2.872 2.763c0 1.526-1.286 2.763-2.872 2.763l-.703-.001l-.013.034l-1.488 2.704a.5.5 0 0 1-.914-.396l.036-.083l1.244-2.26h-1.853l-.014.035l-1.488 2.704a.5.5 0 0 1-.914-.396l.036-.083l1.244-2.26h-1.87l-1.491 2.739a.5.5 0 0 1-.596.235l-.082-.036a.5.5 0 0 1-.236-.595l.036-.083l1.23-2.26l-.42.002c-1.586 0-2.872-1.237-2.872-2.763c0-1.47 1.192-2.671 2.697-2.758l.237-.005c.204-1.913 1.601-3.474 4.066-3.474zm0 1c-1.65 0-3.087 1.27-3.087 3.025c0 .278-.254.496-.545.496h-.55c-1.004 0-1.818.779-1.818 1.74c0 .96.814 1.739 1.818 1.739h8.364c1.004 0 1.818-.779 1.818-1.74c0-.96-.814-1.74-1.818-1.74h-.55c-.291 0-.545-.217-.545-.495c0-1.777-1.438-3.025-3.087-3.025zm-5.157-5a4.236 4.236 0 0 1 1.895.565a4.238 4.238 0 0 1 1.979 2.573a4.931 4.931 0 0 0-1.073.363a3.048 3.048 0 0 0-1.644-2.126a6.013 6.013 0 0 0-.535-.249c.086 1.227-.037 2.483-.626 3.445c-.58.853-1.328 1.423-2.558 1.898c.14.124.29.236.45.338c.31.199.618.352.92.461a3.701 3.701 0 0 0-.878.81A4.238 4.238 0 0 1 2.116 8.67a.629.629 0 0 1 .306-.947c1.375-.493 2.117-1.047 2.54-1.856c.463-.883.549-1.82.241-3.08c-.1-.409.224-.799.645-.776z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherRainShowersNight20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
