import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M18.01 12.245l.504-1.187c.236-.556.801-.86 1.356-.744l.118.031l.63.202c.626.2 1.104.735 1.259 1.407c.367 1.598-.074 3.543-1.322 5.836c-1.247 2.289-2.614 3.665-4.1 4.129a1.76 1.76 0 0 1-1.663-.342l-.124-.114l-.478-.48a1.36 1.36 0 0 1-.223-1.59l.071-.117l.722-1.06c.283-.417.77-.614 1.237-.515l.127.035l1.332.444a5.08 5.08 0 0 0 1.33-1.519a4.799 4.799 0 0 0 .596-1.59l.038-.27l-1.109-1.052a1.354 1.354 0 0 1-.348-1.373l.047-.131l.504-1.187l-.503 1.187zM19.745 4a2.25 2.25 0 0 1 2.25 2.25l.001 3.979a2.821 2.821 0 0 0-.874-.562l-.2-.073l-.427-.137V6.25a.75.75 0 0 0-.75-.75H4.25a.75.75 0 0 0-.75.75v11.505c0 .414.336.75.75.75l2.749-.001L7 15.75a1.75 1.75 0 0 1 1.606-1.744L8.75 14h6.495a1.75 1.75 0 0 1 1.744 1.607l.006.143l-.001 1.222l-.554-.185l-.166-.048a2.151 2.151 0 0 0-.78-.047v-.942a.25.25 0 0 0-.192-.243l-.057-.007H8.75a.25.25 0 0 0-.243.193l-.007.057l-.001 2.754h4.854l-.142.21l-.098.156c-.2.352-.302.744-.307 1.134H4.25A2.25 2.25 0 0 1 2 17.755V6.25A2.25 2.25 0 0 1 4.25 4h15.495zM12 7a3 3 0 1 1 0 6a3 3 0 0 1 0-6zm0 1.5a1.5 1.5 0 1 0 0 3a1.5 1.5 0 0 0 0-3z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoPersonCall24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
