import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M21.945 14.472l.021.062l.023.1l.01.1v6.516a.75.75 0 0 1-.65.743l-.1.007H2.75a.75.75 0 0 1-.743-.648L2 21.25v-6.5l.002-.052l.01-.086a.748.748 0 0 1 .047-.153l2.76-6.019a.75.75 0 0 1 .573-.43l.108-.007l2.54-.001l-.79 1.37l-.067.13H5.98L3.918 14H20.07l-2.027-4.346l.862-1.497c.067.05.125.113.172.184l.053.095l2.815 6.036zm-8.58-12.416l.092.045l5.188 3.003c.328.19.458.591.319.933l-.045.092L16.112 11h1.138a.75.75 0 0 1 .102 1.494l-.102.007l-2.002-.001v.003h-4.079l-.003-.003H6.75a.75.75 0 0 1-.102-1.492l.102-.007L8.573 11l-.182-.105a.752.752 0 0 1-.318-.933l.044-.092l4.317-7.496c.19-.329.59-.46.931-.32zm-.01 1.72L9.789 9.97l1.778 1.03h2.817l2.865-4.973l-3.892-2.253z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Vote24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
