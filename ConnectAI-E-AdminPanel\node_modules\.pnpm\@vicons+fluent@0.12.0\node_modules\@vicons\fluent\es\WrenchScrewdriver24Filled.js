import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M16.08 2.415A.75.75 0 0 1 16.75 2h1.5a.75.75 0 0 1 .67.415l1 2a.75.75 0 0 1-.019.707L19 6.7V12h1.25a.75.75 0 0 1 .75.75v1.75h-7v-1.75a.75.75 0 0 1 .75-.75H16V6.7l-.901-1.578a.75.75 0 0 1-.02-.707l1-2zM14 16v2.5a3.5 3.5 0 1 0 7 0V16h-7zM8.828 2.212a.75.75 0 0 1 .698-.076a5.502 5.502 0 0 1 .51 9.996v7.332a2.536 2.536 0 0 1-5.072 0v-7.332a5.502 5.502 0 0 1 .51-9.996a.75.75 0 0 1 1.026.697V6.5a1 1 0 1 0 2 0V2.833a.75.75 0 0 1 .328-.62z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WrenchScrewdriver24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
