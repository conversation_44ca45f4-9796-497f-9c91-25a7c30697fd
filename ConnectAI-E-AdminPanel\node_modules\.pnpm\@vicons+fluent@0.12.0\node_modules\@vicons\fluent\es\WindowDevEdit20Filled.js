import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2 4.75A2.75 2.75 0 0 1 4.75 2h8.497a2.75 2.75 0 0 1 2.75 2.75v4.483c-.325.14-.63.342-.895.608l-.605.605V6H3.5v7.248c0 .69.56 1.25 1.25 1.25h5.694l-.171.172A3.197 3.197 0 0 0 9.475 16H4.75A2.75 2.75 0 0 1 2 13.248V4.75zm6.354 3.104a.5.5 0 1 0-.707-.708l-2.5 2.5a.5.5 0 0 0 0 .708l2.5 2.5a.5.5 0 0 0 .707-.708L6.208 10l2.146-2.146zm1.792 5a.5.5 0 0 1 0-.708L12.293 10l-2.147-2.146a.5.5 0 0 1 .708-.708l2.5 2.5a.5.5 0 0 1 0 .707l-2.5 2.5a.5.5 0 0 1-.708 0zm5.663-2.306l-4.83 4.83a2.197 2.197 0 0 0-.577 1.02l-.375 1.498a.89.89 0 0 0 1.079 1.078l1.498-.374c.386-.097.739-.296 1.02-.578l4.83-4.83a1.87 1.87 0 0 0-2.645-2.644z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowDevEdit20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
