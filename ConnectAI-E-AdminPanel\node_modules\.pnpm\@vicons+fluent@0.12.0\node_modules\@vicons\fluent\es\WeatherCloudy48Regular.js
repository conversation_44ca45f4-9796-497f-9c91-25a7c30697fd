import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M26.003 14.018c6.337 0 9.932 4.194 10.455 9.26h.16c4.078 0 7.384 3.298 7.384 7.365c0 4.068-3.306 7.365-7.384 7.365h-21.23c-4.078 0-7.384-3.297-7.384-7.365c0-3.986 3.176-7.233 7.14-7.361l.405-.004c.526-5.099 4.117-9.26 10.454-9.26zm0 2.495c-4.26 0-7.975 3.448-7.975 8.21c0 .755-.656 1.347-1.407 1.347H15.2c-2.595 0-4.698 2.114-4.698 4.722c0 2.607 2.103 4.721 4.697 4.721h21.606c2.595 0 4.697-2.114 4.697-4.721c0-2.608-2.102-4.722-4.697-4.722h-1.42c-.752 0-1.408-.592-1.408-1.346c0-4.824-3.714-8.211-7.975-8.211zM20 8a9.431 9.431 0 0 1 7.787 4.104a15.277 15.277 0 0 0-3.437-.017A6.649 6.649 0 0 0 20 10.462c-3.284 0-6.083 2.423-6.643 5.696l-.314 1.83a1 1 0 0 1-.986.832H10.11c-2.063 0-3.735 1.71-3.735 3.821c0 1.106.46 2.102 1.193 2.8a9.273 9.273 0 0 0-1.044 2.092a6.564 6.564 0 0 1 4.04-11.737h.142A9.44 9.44 0 0 1 20 8z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherCloudy48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
