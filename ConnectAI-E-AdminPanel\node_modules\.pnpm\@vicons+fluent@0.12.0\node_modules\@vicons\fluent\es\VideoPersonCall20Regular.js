import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2 5.5A1.5 1.5 0 0 1 3.5 4h13A1.5 1.5 0 0 1 18 5.5v3.406a2.29 2.29 0 0 0-1-.186V5.5a.5.5 0 0 0-.5-.5h-13a.5.5 0 0 0-.5.5v9a.5.5 0 0 0 .5.5H6v-1.5A1.5 1.5 0 0 1 7.5 12h5a1.5 1.5 0 0 1 1.5 1.5v1.074c-.411.013-.738.14-1 .317V13.5a.5.5 0 0 0-.5-.5h-5a.5.5 0 0 0-.5.5V15h5.853c-.315.256-.525.576-.68.81a4.24 4.24 0 0 1-.128.19H3.5A1.5 1.5 0 0 1 2 14.5v-9zm8 5.5a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5zm0-1a1.5 1.5 0 1 1 0-3a1.5 1.5 0 0 1 0 3zm5.835 1.265l.399-.941c.187-.442.635-.682 1.075-.59l.094.024l.5.16c.496.159.875.583.998 1.116c.291 1.267-.058 2.81-1.049 4.628c-.989 1.816-2.072 2.907-3.251 3.275c-.458.143-.95.04-1.32-.271l-.097-.09l-.38-.381a1.078 1.078 0 0 1-.176-1.262l.056-.092l.572-.84a.948.948 0 0 1 .982-.409l.1.027l1.057.353c.421-.32.773-.72 1.054-1.205c.241-.415.4-.835.473-1.261l.03-.214l-.879-.835a1.074 1.074 0 0 1-.276-1.088l.037-.104z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoPersonCall20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
