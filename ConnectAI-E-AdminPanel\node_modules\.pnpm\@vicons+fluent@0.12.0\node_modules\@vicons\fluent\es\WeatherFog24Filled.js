import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M7.745 19.5h8.501a.75.75 0 0 1 .102 1.493l-.102.007H7.745a.75.75 0 0 1-.102-1.493l.102-.007h8.501h-8.501zM4.75 16.52h14.5a.75.75 0 0 1 .102 1.492l-.102.007H4.75a.75.75 0 0 1-.102-1.493l.102-.007zM12 3.004c3.169 0 4.966 2.097 5.227 4.63h.08A3.687 3.687 0 0 1 21 11.318A3.687 3.687 0 0 1 17.306 15H6.693A3.687 3.687 0 0 1 3 11.318a3.687 3.687 0 0 1 3.692-3.683h.08c.263-2.55 2.059-4.63 5.227-4.63z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherFog24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
