import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M26.001 16.01c6.337 0 9.932 4.194 10.455 9.26h.16c4.078 0 7.384 3.297 7.384 7.365S40.694 40 36.616 40h-21.23c-4.078 0-7.384-3.297-7.384-7.365s3.306-7.365 7.384-7.365h.16c.526-5.1 4.118-9.26 10.455-9.26zm0 2.495c-4.261 0-7.975 3.448-7.975 8.21c0 .755-.656 1.347-1.407 1.347h-1.421c-2.594 0-4.697 2.114-4.697 4.721c0 2.608 2.103 4.722 4.697 4.722h21.606c2.594 0 4.697-2.114 4.697-4.722c0-2.607-2.103-4.72-4.697-4.72h-1.42c-.752 0-1.408-.593-1.408-1.348c0-4.823-3.714-8.21-7.975-8.21zM13.181 8.002c1.59.086 3.133.544 4.525 1.348a10.113 10.113 0 0 1 4.418 5.193a12.1 12.1 0 0 0-2.189.855c-.64-1.741-2.06-3.156-3.991-4.262a6.378 6.378 0 0 0-1.426-.59c.364 3.04.247 5.108-1.057 7.578l-.268.479c-1.295 2.197-3.207 3.497-6.125 4.84c.322.318.813.692 1.212.974c.453.32.923.593 1.404.818a9.42 9.42 0 0 0-1.823 1.874l-.31-.172a10.122 10.122 0 0 1-3.283-3.028a1.502 1.502 0 0 1 .73-2.265c3.285-1.176 5.055-2.5 6.067-4.432c1.106-2.11 1.31-4.348.576-7.354a1.502 1.502 0 0 1 1.54-1.856z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherPartlyCloudyNight48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
