import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.759 4H16.49a1.498 1.498 0 0 1 1.511 1.5v9a1.5 1.5 0 0 1-1.5 1.5H3.51a1.499 1.499 0 0 1-1.5-1.5V9.887c.158.07.328.11.5.113c.172-.002.342-.037.5-.105V14.5a.5.5 0 0 0 .5.5h2.498v-1.5a1.5 1.5 0 0 1 1.5-1.5h4.997a1.499 1.499 0 0 1 1.5 1.5V15h2.498a.5.5 0 0 0 .5-.5v-9a.5.5 0 0 0-.5-.5H4.309a.569.569 0 0 1-.2-.124a.565.565 0 0 1-.138-.225l-.212-.65zm3.237 11h5.997v-1.5a.5.5 0 0 0-.5-.5H7.496a.5.5 0 0 0-.5.5V15zm4.765-8.268A2.5 2.5 0 0 1 9.995 11a2.498 2.498 0 0 1-2.5-2.5a2.5 2.5 0 0 1 2.5-2.5c.662 0 1.298.263 1.766.732zm-2.827.707a1.5 1.5 0 1 0 2.123 2.122a1.5 1.5 0 0 0-2.123-2.122zM4.12 9.33a.185.185 0 0 0-.121.165a.18.18 0 0 0 .034.105c.021.03.052.054.087.066l.46.15a.957.957 0 0 1 .598.6l.15.46a.183.183 0 0 0 .166.12a.178.178 0 0 0 .166-.12l.15-.46a.942.942 0 0 1 .599-.599l.458-.149a.175.175 0 0 0 .088-.066a.177.177 0 0 0-.088-.27h-.009L6.4 9.18a.95.95 0 0 1-.6-.599l-.15-.459a.19.19 0 0 0-.035-.06a.18.18 0 0 0-.13-.06a.19.19 0 0 0-.105.033a.179.179 0 0 0-.066.087l-.149.46a.987.987 0 0 1-.225.368a.944.944 0 0 1-.364.23l-.456.147zM2.499 9a.3.3 0 0 1-.283-.199l-.25-.765a1.568 1.568 0 0 0-.998-1.002l-.764-.249A.3.3 0 0 1 .002 6.5a.3.3 0 0 1 .202-.284l.764-.249a1.57 1.57 0 0 0 .978-.979l.006-.019l.249-.765a.3.3 0 0 1 .285-.202a.3.3 0 0 1 .284.202l.25.765c.027.075.06.148.1.218c.07.147.165.282.278.4c.174.173.385.304.618.381l.764.25h.015a.3.3 0 0 1 .202.285a.3.3 0 0 1-.202.284l-.764.25a1.568 1.568 0 0 0-.999.997l-.249.765l-.007.02a.32.32 0 0 1-.102.126a.3.3 0 0 1-.175.055z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoPersonSparkle20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
