import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M14.517 10.016a4.481 4.481 0 0 0-8.908-.707c.34-.028.68-.028 1.017-.004a3.483 3.483 0 1 1 6.704 1.84c.39-.056.764-.152 1.105-.27c.054-.278.082-.566.082-.86zm-9.44-5.634l-.07-.058a.5.5 0 0 0-.638.765l.858.858l.07.058a.5.5 0 0 0 .638-.765l-.859-.858zm10.662.637a.5.5 0 0 0-.765-.637l-.859.858l-.058.07a.5.5 0 0 0 .765.637l.859-.858l.058-.07zM10.52 2.435a.5.5 0 0 0-.992.09v1.213l.008.09a.5.5 0 0 0 .992-.09V2.524l-.008-.09zm-6.78 9.504c1.056-.576 2.036-.734 2.997-.614c.974.123 1.96.535 3.01 1.148c1.203.701 2.584.829 3.834.656c1.248-.173 2.41-.651 3.21-1.222a.5.5 0 0 0-.582-.814c-.658.47-1.666.893-2.765 1.045c-1.097.152-2.236.03-3.193-.529c-1.108-.646-2.227-1.13-3.39-1.276c-1.175-.147-2.365.054-3.6.728a.5.5 0 0 0 .479.878zm0 2c1.056-.576 2.036-.734 2.997-.614c.974.123 1.96.535 3.01 1.148c1.203.701 2.584.829 3.834.656c1.248-.173 2.41-.651 3.21-1.222a.5.5 0 0 0-.582-.814c-.658.47-1.666.893-2.765 1.045c-1.097.152-2.236.03-3.193-.529c-1.108-.646-2.227-1.13-3.39-1.276c-1.175-.147-2.365.054-3.6.728a.5.5 0 0 0 .479.878zm2.997 1.386c-.961-.12-1.941.038-2.997.614a.5.5 0 0 1-.48-.878c1.236-.674 2.426-.875 3.602-.728c1.162.146 2.281.63 3.39 1.276c.956.558 2.095.681 3.192.53c1.098-.153 2.107-.575 2.765-1.046a.5.5 0 0 1 .582.814c-.8.57-1.963 1.049-3.21 1.222c-1.25.173-2.631.045-3.834-.656c-1.05-.613-2.036-1.025-3.01-1.147z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherHaze20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
