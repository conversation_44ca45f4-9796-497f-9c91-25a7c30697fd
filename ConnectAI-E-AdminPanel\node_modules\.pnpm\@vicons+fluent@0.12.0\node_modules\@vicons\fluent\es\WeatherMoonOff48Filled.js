import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.384 4.616a1.25 1.25 0 1 0-1.768 1.768l16.22 16.22c-.054.11-.11.219-.167.328c-2.108 4.022-5.739 6.835-12.52 9.261a1.35 1.35 0 0 0-.656 2.037a17.933 17.933 0 0 0 5.821 5.367c7.198 4.156 16.073 2.776 21.695-2.82l6.607 6.607a1.25 1.25 0 0 0 1.768-1.768l-37-37zm24.93 3.805c8.523 4.92 11.5 15.757 6.737 24.327l-15.49-15.49c.432-2.78.245-5.882-.652-9.558a1.35 1.35 0 0 1 1.383-1.668c2.802.15 5.54.955 8.022 2.389z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherMoonOff48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
