import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M16.25 2a.75.75 0 0 0-.67.415l-1 2a.75.75 0 0 0 .019.707L15.5 6.7V12h-.75a.75.75 0 0 0-.75.75v5.75a3.5 3.5 0 1 0 7 0v-5.75a.75.75 0 0 0-.75-.75h-.75V6.7l.901-1.578a.75.75 0 0 0 .02-.707l-1-2A.75.75 0 0 0 18.75 2h-2.5zM18 12h-1V6.5a.75.75 0 0 0-.099-.372l-.8-1.402l.613-1.226h1.573l.613 1.226l-.801 1.402A.75.75 0 0 0 18 6.5V12zm-2.5 6.5V16h4v2.5a2 2 0 1 1-4 0zm4-4h-4v-1h4v1zM9.526 2.136a.75.75 0 0 0-1.026.697V6.5a1 1 0 0 1-2 0V2.833a.75.75 0 0 0-1.027-.697a5.502 5.502 0 0 0-.509 9.996v7.332a2.536 2.536 0 0 0 5.072 0v-7.332a5.502 5.502 0 0 0-.51-9.996zM10 6.5V4.127a4.002 4.002 0 0 1-.996 6.831a.75.75 0 0 0-.468.695v7.811a1.036 1.036 0 0 1-2.072 0v-7.811a.75.75 0 0 0-.468-.695A4.002 4.002 0 0 1 5 4.128V6.5a2.5 2.5 0 1 0 5 0z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WrenchScrewdriver24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
