import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M18.998 10a3.001 3.001 0 1 1 0 6h-.03l-.102.006H17.7c.19.374.298.796.298 1.243c0 1.53-1.152 2.756-2.675 2.756c-1.505 0-2.415-.881-2.66-2.103a.75.75 0 1 1 1.47-.294c.114.565.457.897 1.19.897c.672 0 1.175-.534 1.175-1.256c0-.688-.568-1.25-1.273-1.25l-12.488.007a.75.75 0 0 1-.102-1.493l.102-.007h12.387l.101-.006c.065 0 .128.002.191.006l3.472.002l.054-.006l.2-.01a1.501 1.501 0 1 0-1.629-1.722l-.027.239A.75.75 0 0 1 16 12.816A3.001 3.001 0 0 1 18.998 10zM9.75 17.5a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zM12 3.999a4 4 0 0 1 .022 8H2.756a.75.75 0 0 1-.102-1.492l.102-.007H12l.165-.006A2.5 2.5 0 1 0 9.5 8A.75.75 0 0 1 8 8a4 4 0 0 1 4-4zm7.25 2.5a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zM5.75 5a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherBlowingSnow24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
