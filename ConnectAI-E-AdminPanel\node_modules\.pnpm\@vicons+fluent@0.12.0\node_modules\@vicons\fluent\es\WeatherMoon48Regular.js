import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M37.903 33.009c-4.971 8.61-15.98 11.559-24.589 6.588a17.934 17.934 0 0 1-5.821-5.367a1.35 1.35 0 0 1 .656-2.037c6.78-2.427 10.412-5.239 12.52-9.261c2.218-4.235 2.791-8.874 1.24-15.232a1.35 1.35 0 0 1 1.383-1.668c2.802.15 5.54.955 8.022 2.388c8.61 4.97 11.56 15.98 6.589 24.589zm-15.02-8.917c-2.303 4.396-6.112 7.43-12.426 9.906a15.473 15.473 0 0 0 4.107 3.434c7.414 4.28 16.893 1.74 21.173-5.673c4.28-7.414 1.74-16.893-5.673-21.174a15.477 15.477 0 0 0-4.907-1.818l-.468-.08c1.193 5.968.591 10.83-1.806 15.405z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherMoon48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
