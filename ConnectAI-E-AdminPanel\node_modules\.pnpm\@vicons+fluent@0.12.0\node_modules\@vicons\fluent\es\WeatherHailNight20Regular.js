import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11 16.667A.667.667 0 1 1 11 18a.667.667 0 0 1 0-1.333zM8.667 16a.667.667 0 1 1 0 1.333a.667.667 0 0 1 0-1.333zm4.666 0a.667.667 0 1 1 0 1.333a.667.667 0 0 1 0-1.333zM11 6c2.465 0 3.863 1.574 4.066 3.474h.062c1.586 0 2.872 1.237 2.872 2.763C18 13.763 16.714 15 15.128 15H6.872C5.286 15 4 13.763 4 12.237c0-1.47 1.192-2.671 2.697-2.758l.237-.005C7.139 7.561 8.535 6 11 6zm0 1c-1.65 0-3.087 1.27-3.087 3.025c0 .278-.254.496-.545.496h-.55C5.814 10.521 5 11.3 5 12.261C5 13.22 5.814 14 6.818 14h8.364C16.186 14 17 13.221 17 12.26c0-.96-.814-1.739-1.818-1.739h-.55c-.29 0-.545-.218-.545-.496C14.087 8.248 12.65 7 11 7zM5.843 2a4.236 4.236 0 0 1 1.895.565a4.238 4.238 0 0 1 1.979 2.573a4.931 4.931 0 0 0-1.073.363A3.048 3.048 0 0 0 7 3.375a6.016 6.016 0 0 0-.535-.249c.086 1.228-.036 2.483-.626 3.445c-.58.853-1.328 1.423-2.558 1.898c.14.124.29.236.45.339c.31.198.618.351.92.46a3.702 3.702 0 0 0-.878.811a4.238 4.238 0 0 1-1.662-1.418a.629.629 0 0 1 .306-.948c1.376-.492 2.117-1.046 2.54-1.856c.463-.883.549-1.82.242-3.08c-.1-.409.223-.799.644-.776z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherHailNight20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
