version: '3.8'

networks:
  connectai-local:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
  elasticsearch_data:
  rabbitmq_data:
  files_data:
  portainer_data:

services:
  # ==================== 基础设施服务 ====================

  # MySQL 数据库 (单实例用于本地开发)
  mysql:
    image: mysql:5.7
    container_name: connectai_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE_MANAGER}
      TZ: ${TZ}
    ports:
      - "53306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - connectai-local

  # Redis 缓存
  redis:
    image: redis:alpine
    container_name: connectai_redis
    restart: always
    ports:
      - "49642:6379"
    volumes:
      - redis_data:/data
    networks:
      - connectai-local

  # Elasticsearch (降低内存配置)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
    container_name: connectai_elasticsearch
    restart: always
    environment:
      - "ES_JAVA_OPTS=${ES_JAVA_OPTS}"
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "50094:9200"
      - "50093:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - connectai-local

  # RabbitMQ
  rabbitmq:
    image: rabbitmq:3.7-management-alpine
    container_name: connectai_rabbitmq
    restart: always
    environment:
      RABBITMQ_ERLANG_COOKIE: ${RABBITMQ_ERLANG_COOKIE}
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_DEFAULT_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_DEFAULT_PASS}
      RABBITMQ_DEFAULT_VHOST: ${RABBITMQ_DEFAULT_VHOST}
    ports:
      - "49491:5672"
      - "49490:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - connectai-local

  # Nginx 代理
  nginx-proxy:
    image: jwilder/nginx-proxy:alpine
    container_name: connectai_nginx
    restart: always
    ports:
      - "8081:80"
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
    networks:
      - connectai-local

  # Nchan (消息推送)
  nchan:
    image: lloydzhou/nchan
    container_name: connectai_nchan
    restart: always
    ports:
      - "49186:80"
    networks:
      - connectai-local

  # ==================== ConnectAI 核心服务 ====================

  # 知识服务器 (模拟)
  know-server:
    image: nginx:alpine
    container_name: connectai_know_server
    restart: always
    ports:
      - "8086:80"
    environment:
      - VIRTUAL_HOST=know.local.connectai.com
    volumes:
      - files_data:/usr/share/nginx/html
    networks:
      - connectai-local

  # ConnectAI 管理服务 (模拟)
  manager:
    image: nginx:alpine
    container_name: connectai_manager
    restart: always
    ports:
      - "50344:80"
    environment:
      - VIRTUAL_HOST=manager.local.connectai.com
    depends_on:
      - mysql
      - redis
      - rabbitmq
    networks:
      - connectai-local

  # ==================== AI 代理服务 (核心几个) ====================

  # OpenAI 代理 (模拟)
  proxy-openai:
    image: nginx:alpine
    container_name: connectai_proxy_openai
    restart: always
    ports:
      - "50314:80"
    environment:
      - VIRTUAL_HOST=openai.proxy.local.connectai.com
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    networks:
      - connectai-local

  # Claude 代理 (模拟)
  proxy-claude:
    image: nginx:alpine
    container_name: connectai_proxy_claude
    restart: always
    ports:
      - "50320:80"
    environment:
      - VIRTUAL_HOST=claude.proxy.local.connectai.com
      - CLAUDE_API_KEY=${CLAUDE_API_KEY}
    networks:
      - connectai-local

  # 星火代理 (模拟)
  proxy-xinghuo:
    image: nginx:alpine
    container_name: connectai_proxy_xinghuo
    restart: always
    ports:
      - "50295:80"
    environment:
      - VIRTUAL_HOST=xinghuo.proxy.local.connectai.com
      - XINGHUO_API_KEY=${XINGHUO_API_KEY}
    networks:
      - connectai-local

  # 文心代理 (模拟)
  proxy-wenxin:
    image: nginx:alpine
    container_name: connectai_proxy_wenxin
    restart: always
    ports:
      - "50302:80"
    environment:
      - VIRTUAL_HOST=wenxin.proxy.local.connectai.com
      - WENXIN_API_KEY=${WENXIN_API_KEY}
    networks:
      - connectai-local

  # ==================== 监控和管理工具 ====================

  # Portainer (Docker 管理界面)
  portainer:
    image: portainer/portainer-ce:latest
    container_name: connectai_portainer
    restart: always
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    networks:
      - connectai-local
