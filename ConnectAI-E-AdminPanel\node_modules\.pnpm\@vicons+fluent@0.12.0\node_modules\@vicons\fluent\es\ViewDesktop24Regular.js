import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M19.75 3a2.25 2.25 0 0 1 2.245 2.096L22 5.25v10.502a2.25 2.25 0 0 1-2.096 2.245l-.154.005H15.5V20.5h1.751a.75.75 0 0 1 .102 1.494L17.25 22h-10.5a.75.75 0 0 1-.102-1.493l.102-.007H8.5v-2.498H4.251a2.25 2.25 0 0 1-2.245-2.096l-.005-.154V5.25a2.25 2.25 0 0 1 2.096-2.245L4.25 3H19.75zm-5.751 15.002h-4L10 20.5h4l-.001-2.498zM19.749 4.5H4.252a.75.75 0 0 0-.743.648L3.5 5.25v10.502c0 .38.282.694.648.743l.102.007H19.75a.75.75 0 0 0 .743-.648l.007-.102V5.25a.75.75 0 0 0-.648-.743L19.75 4.5zM18.505 6a.5.5 0 0 1 .492.41l.008.09v8.002a.5.5 0 0 1-.41.492l-.09.008H9.5a.5.5 0 0 1-.492-.41L9 14.502V6.5a.5.5 0 0 1 .41-.492L9.5 6h9.004zM7.5 6a.5.5 0 0 1 .492.41L8 6.5v8.002a.5.5 0 0 1-.41.492l-.09.008h-2a.5.5 0 0 1-.492-.41L5 14.502V6.5a.5.5 0 0 1 .41-.492L5.5 6h2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ViewDesktop24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
