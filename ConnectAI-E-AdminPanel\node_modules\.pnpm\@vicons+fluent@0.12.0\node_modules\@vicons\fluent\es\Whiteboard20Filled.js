import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.331 3.461l.11.102l.102.11a1.928 1.928 0 0 1-.103 2.606l-3.603 3.617a1.892 1.892 0 0 1-.794.477l-1.96.591a.841.841 0 0 1-1.047-.567a.851.851 0 0 1 .005-.503l.621-1.942c.093-.289.252-.55.465-.765l3.612-3.625a1.904 1.904 0 0 1 2.592-.1zM12.891 4H4.5A2.5 2.5 0 0 0 2 6.5v2.264a17.73 17.73 0 0 1 1.72-1.411c.647-.458 1.342-.86 1.979-1.026c.322-.085.662-.118.987-.042c.34.08.633.272.846.582c.463.674.126 1.404-.194 1.924c-.167.272-.374.556-.576.834l-.023.03c-.211.292-.421.582-.609.881c-.158.285-.2.622-.13.865c.035.116.092.204.166.265c.073.061.19.119.379.136c.33.03.759-.083 1.286-.272a.5.5 0 1 1 .338.94c-.52.188-1.14.38-1.714.328a1.658 1.658 0 0 1-.928-.363a1.524 1.524 0 0 1-.486-.753c-.16-.546-.05-1.165.224-1.648l.005-.009l.006-.01c.21-.337.443-.657.655-.948l.01-.014c.213-.291.399-.547.545-.785c.326-.53.298-.724.222-.835a.394.394 0 0 0-.25-.175c-.113-.026-.278-.024-.505.036c-.46.12-1.038.438-1.654.875c-.853.604-1.701 1.38-2.299 1.985V13.5A2.5 2.5 0 0 0 4.5 16h11a2.5 2.5 0 0 0 2.5-2.5V7.134l-3.455 3.468c-.338.34-.755.59-1.213.728l-1.96.591a1.841 1.841 0 0 1-2.295-1.238a1.85 1.85 0 0 1 .011-1.094l.622-1.942c.14-.44.383-.839.709-1.165L12.89 4z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Whiteboard20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
