import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.509 16.082a1 1 0 0 1-1.504-1.318L6.549 13H3a1 1 0 0 1-.993-.883L2 12a1 1 0 0 1 .883-.993L3 11h2.785L4.24 9.25a1 1 0 1 1 1.5-1.324L8.451 11h2.548V8.45L7.927 5.739a1 1 0 0 1-.165-1.313l.077-.099a1 1 0 0 1 1.312-.165l.1.077l1.748 1.544L11 3a1 1 0 0 1 .77-.974l.113-.02L12 2a1 1 0 0 1 .993.883L13 3l-.001 2.798l1.77-1.544a1 1 0 0 1 1.316 1.506l-3.086 2.694V11h2.548l2.702-3.076a1 1 0 0 1 1.312-.169l.099.077a1 1 0 0 1 .168 1.312l-.077.1L18.208 11H21a1 1 0 0 1 .993.883L22 12a1 1 0 0 1-.883.993L21 13h-2.793l1.545 1.764a1 1 0 0 1-.004 1.322l-.089.089a1 1 0 0 1-1.322-.004l-.089-.089L15.546 13h-2.547v2.548l3.087 2.693a1 1 0 0 1 .173 1.312l-.077.1a1 1 0 0 1-1.311.172l-.1-.076L13 18.201L13 21a1 1 0 0 1-.883.993L12 22a1 1 0 0 1-.993-.883L11 21l-.001-2.783l-1.736 1.533a1 1 0 1 1-1.323-1.5l3.059-2.7V13H9.21l-2.701 3.082z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSnowflake24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
