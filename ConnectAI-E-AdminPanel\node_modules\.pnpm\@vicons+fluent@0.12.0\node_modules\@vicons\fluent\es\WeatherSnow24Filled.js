import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M9.75 18.151a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zm4.5 0a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zm-6.75-1a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zm4.5 0a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zm4.5 0a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zM12 4.001c3.168 0 4.966 2.097 5.227 4.63h.08A3.687 3.687 0 0 1 21 12.314a3.687 3.687 0 0 1-3.692 3.682H6.693A3.687 3.687 0 0 1 3 12.314A3.687 3.687 0 0 1 6.693 8.63h.08c.262-2.55 2.058-4.63 5.227-4.63z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSnow24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
