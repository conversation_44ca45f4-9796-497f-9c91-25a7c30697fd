import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10 1.998a.5.5 0 0 1 .492.41l.008.09v3.065l2.018-1.772a.5.5 0 1 1 .66.751l-2.679 2.353v2.603h2.605l2.355-2.676a.5.5 0 0 1 .633-.099l.072.053a.5.5 0 0 1 .099.633l-.053.073l-1.774 2.016h3.065a.5.5 0 0 1 .492.41l.009.09a.5.5 0 0 1-.41.493l-.09.008h-3.067l1.773 2.018a.5.5 0 0 1 .017.64l-.063.066a.5.5 0 0 1-.64.016l-.065-.062l-2.354-2.678l-2.605-.001v2.605l2.68 2.355a.5.5 0 0 1 .099.632l-.054.073a.5.5 0 0 1-.633.1l-.072-.054l-2.02-1.775v3.067a.5.5 0 0 1-.41.492l-.09.008a.5.5 0 0 1-.493-.41l-.008-.09v-3.064l-2.015 1.772a.5.5 0 1 1-.66-.751l2.676-2.353v-2.606l-2.427.002l-.026.037l-2.502 2.847a.5.5 0 0 1-.752-.66L5.747 10.5H2.499a.5.5 0 0 1-.492-.41L1.999 10a.5.5 0 0 1 .41-.491l.09-.008h2.88L3.79 7.689a.5.5 0 0 1 .752-.66L6.71 9.5h2.789V6.893L6.822 4.54a.5.5 0 0 1-.098-.633l.053-.072a.5.5 0 0 1 .633-.1l.072.054L9.5 5.562V2.498a.5.5 0 0 1 .326-.469l.084-.023l.09-.008z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSnowflake20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
