import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10.464 15.748L12.7 13.26a.75.75 0 0 1 1.184.915l-.068.088l-1.111 1.236h2.276a.75.75 0 0 1 .645 1.134l-.058.084l-3.212 4.031a.75.75 0 0 1-1.236-.843l.063-.091l2.242-2.815h-2.403a.75.75 0 0 1-.623-1.168l.065-.083L12.7 13.26l-2.236 2.488zm2.538-10.74c3.168 0 4.966 2.098 5.227 4.631h.08a3.687 3.687 0 0 1 3.692 3.683a3.687 3.687 0 0 1-3.692 3.682l-1.788.001a1.333 1.333 0 0 0-.002-1.5l1.734.002c1.261 0 2.283-1.002 2.283-2.237s-1.022-2.236-2.283-2.236h-.69c-.366 0-.685-.28-.685-.638c0-2.285-1.805-3.89-3.876-3.89c-2.072 0-3.877 1.634-3.877 3.89c0 .357-.319.638-.684.638h-.69c-1.262 0-2.284 1-2.284 2.236c0 1.235 1.022 2.237 2.283 2.237l1.762-.001a1.332 1.332 0 0 0-.002 1.5l-1.816-.002a3.687 3.687 0 0 1-3.692-3.682a3.687 3.687 0 0 1 3.692-3.683h.08c.263-2.55 2.06-4.63 5.228-4.63zM10 2c1.617 0 3.05.815 3.9 2.062a7.496 7.496 0 0 0-.898-.053c-.395 0-.775.029-1.139.085a3.22 3.22 0 0 0-5.032 2.062l-.073.414a1 1 0 0 1-.985.827h-.49a1.782 1.782 0 0 0-1.264 3.04c-.315.4-.565.855-.735 1.347a3.282 3.282 0 0 1 1.812-5.881l.257-.006A4.72 4.72 0 0 1 10 2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherThunderstorm24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
