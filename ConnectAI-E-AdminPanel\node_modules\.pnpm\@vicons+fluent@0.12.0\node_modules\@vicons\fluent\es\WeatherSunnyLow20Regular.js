import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5.554 10.016c0 .338.037.667.108.984H2.5a.5.5 0 0 0 0 1h15a.5.5 0 0 0 0-1h-3.092a4.481 4.481 0 1 0-8.854-.984zm7.822.984h-6.68a3.481 3.481 0 1 1 6.68 0zm-3.35 7.012a.504.504 0 0 0 .109-.012h-.22a.503.503 0 0 0 .11.012zM5.006 4.324l.07.058l.858.858a.5.5 0 0 1-.638.765l-.07-.058l-.858-.858a.5.5 0 0 1 .638-.765zm10.674.058a.5.5 0 0 1 .058.637l-.058.07l-.859.858a.5.5 0 0 1-.764-.638l.057-.07l.859-.857a.5.5 0 0 1 .707 0zm-5.653-2.358a.5.5 0 0 1 .492.41l.008.09v1.214a.5.5 0 0 1-.992.09l-.008-.09V2.524a.5.5 0 0 1 .5-.5zM8.5 16a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1h-3zM5 14a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9A.5.5 0 0 1 5 14z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSunnyLow20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
