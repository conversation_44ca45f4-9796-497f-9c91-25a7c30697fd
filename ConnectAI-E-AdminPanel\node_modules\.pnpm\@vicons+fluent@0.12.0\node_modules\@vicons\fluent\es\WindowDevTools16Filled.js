import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.75 2A2.75 2.75 0 0 0 2 4.75v6a2.75 2.75 0 0 0 2.75 2.75h4.027c-.049-.377.021-.773.226-1.128L9.218 12H4.75c-.69 0-1.25-.56-1.25-1.25V5H12v1.194c.472-.187.99-.25 1.5-.174V4.75A2.75 2.75 0 0 0 10.75 2h-6zm5.521 7.686a2.9 2.9 0 0 1 .228-2.144L9.104 6.146a.5.5 0 1 0-.707.708L10.043 8.5l-1.646 1.646a.5.5 0 0 0 .707.708l1.167-1.168zm-3.168-3.54a.5.5 0 0 1 0 .708L5.457 8.5l1.646 1.646a.5.5 0 0 1-.707.708l-2-2a.5.5 0 0 1 0-.708l2-2a.5.5 0 0 1 .707 0zm6.32.875l-.9 1.56a.637.637 0 1 0 1.103.636l.9-1.559a1.91 1.91 0 0 1-1.624 3.144l-1.654 2.865a.796.796 0 1 1-1.379-.795l1.651-2.86a1.91 1.91 0 0 1 1.903-2.99z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowDevTools16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
