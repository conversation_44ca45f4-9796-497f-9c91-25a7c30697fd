import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.75 3A3.75 3.75 0 0 0 3 6.75v14.5A3.75 3.75 0 0 0 6.75 25H13c0-.519.133-1.037.4-1.5H6.75a2.25 2.25 0 0 1-2.25-2.25V9.5h19v3.601l.046.015c.439.113 1.193.73 1.4 1.384H25V6.75A3.75 3.75 0 0 0 21.25 3H6.75zM23.5 8h-19V6.75A2.25 2.25 0 0 1 6.75 4.5h14.5a2.25 2.25 0 0 1 2.25 2.25V8zm-8.914 15.585c-.391.39-.586.903-.586 1.415s.195 1.023.586 1.413a2 2 0 0 0 2.828 0l3.649-3.647a4.5 4.5 0 0 0 5.89-4.92c-.054-.367-.495-.476-.758-.213l-1.78 1.781a2 2 0 1 1-2.83-2.828l1.782-1.781c.262-.263.154-.704-.213-.758a4.5 4.5 0 0 0-4.92 5.89l-3.648 3.648z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowWrench28Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
