import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M13.001 6.01c3.169 0 4.966 2.097 5.228 4.63h.08A3.687 3.687 0 0 1 22 14.322a3.687 3.687 0 0 1-3.692 3.683L17.676 18l-1.542 2.63a.75.75 0 0 1-1.344-.658l.045-.091L15.944 18h-2.136l-1.543 2.63a.75.75 0 0 1-1.344-.658l.045-.091L12.076 18H9.939l-1.542 2.63a.75.75 0 0 1-1.344-.658l.045-.091L8.207 18H7.6c-2.039 0-3.598-1.644-3.598-3.678a3.687 3.687 0 0 1 3.692-3.682h.08C8.037 8.09 9.833 6.01 13 6.01zM6.588 2.002a5.059 5.059 0 0 1 2.264.674A5.057 5.057 0 0 1 11.06 5.27c-1.985.563-3.405 2.002-3.994 3.946l-.07.246l-.057.238l-.206.038a4.67 4.67 0 0 0-2.804 1.815l-.155-.084a5.062 5.062 0 0 1-1.642-1.515a.75.75 0 0 1 .366-1.132c1.642-.588 2.527-1.25 3.033-2.216c.553-1.055.655-2.174.288-3.677a.75.75 0 0 1 .77-.928z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherRainShowersNight24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
