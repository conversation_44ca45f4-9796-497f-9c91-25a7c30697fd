#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ConnectAI Docker开发环境启动脚本
"""

import os
import sys
import time
import subprocess
import signal
from pathlib import Path

class DockerDevManager:
    def __init__(self):
        self.running = True
        self.processes = []
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """处理退出信号"""
        print(f"\n收到退出信号 {signum}，正在停止所有服务...")
        self.running = False
        self.stop_all_services()
        sys.exit(0)
    
    def run_command(self, command, cwd=None, check=True):
        """执行命令"""
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                cwd=cwd,
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            if check and result.returncode != 0:
                print(f"❌ 命令执行失败: {command}")
                print(f"错误输出: {result.stderr}")
                return False
            return result
        except Exception as e:
            print(f"❌ 命令执行异常: {e}")
            return False
    
    def check_docker(self):
        """检查Docker是否可用"""
        print("🔍 检查Docker环境...")
        
        # 检查Docker是否安装
        result = self.run_command("docker --version", check=False)
        if not result or result.returncode != 0:
            print("❌ Docker未安装或不可用")
            print("请先安装Docker Desktop: https://www.docker.com/products/docker-desktop")
            return False
        
        print(f"✅ Docker版本: {result.stdout.strip()}")
        
        # 检查Docker是否运行
        result = self.run_command("docker info", check=False)
        if not result or result.returncode != 0:
            print("❌ Docker服务未运行")
            print("请启动Docker Desktop")
            return False
        
        print("✅ Docker服务正常运行")
        return True
    
    def build_image(self):
        """构建Docker镜像"""
        print("🔨 构建ConnectAI Manager镜像...")
        
        manager_dir = Path("manager-server")
        if not manager_dir.exists():
            print("❌ manager-server目录不存在")
            return False
        
        # 检查是否已有镜像
        result = self.run_command("docker images connectai-manager", check=False)
        if result and "connectai-manager" in result.stdout:
            print("✅ ConnectAI Manager镜像已存在")
            return True
        
        # 构建镜像
        print("正在构建镜像，这可能需要几分钟...")
        result = self.run_command("make build", cwd=manager_dir)
        if not result:
            print("❌ 镜像构建失败")
            return False
        
        print("✅ 镜像构建成功")
        return True
    
    def prepare_data_dirs(self):
        """准备数据目录"""
        print("📁 准备数据目录...")
        
        dirs = [
            "manager-server/data/mysql/data",
            "manager-server/data/mysql/conf.d", 
            "manager-server/data/redis",
            "manager-server/data/rabbitmq",
            "data/files"
        ]
        
        for dir_path in dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        
        print("✅ 数据目录准备完成")
        return True
    
    def start_services(self):
        """启动Docker服务"""
        print("🚀 启动Docker服务...")
        
        manager_dir = Path("manager-server")
        
        # 停止可能存在的服务
        self.run_command("docker-compose -f docker-compose-local.yml down", cwd=manager_dir, check=False)
        
        # 启动服务
        result = self.run_command(
            "docker-compose -f docker-compose-local.yml up -d", 
            cwd=manager_dir
        )
        if not result:
            print("❌ 服务启动失败")
            return False
        
        print("✅ Docker服务启动成功")
        return True
    
    def wait_for_services(self):
        """等待服务启动完成"""
        print("⏳ 等待服务启动完成...")
        
        services = [
            ("MySQL", "localhost:3306"),
            ("Redis", "localhost:6379"), 
            ("RabbitMQ", "localhost:15672"),
            ("Manager Server", "localhost:3000")
        ]
        
        for service_name, endpoint in services:
            print(f"等待 {service_name} 启动...")
            
            max_attempts = 30
            for attempt in range(max_attempts):
                if not self.running:
                    return False
                
                # 简单的端口检查
                host, port = endpoint.split(":")
                result = self.run_command(f"netstat -an | findstr :{port}", check=False)
                if result and result.stdout:
                    print(f"✅ {service_name} 已启动")
                    break
                
                time.sleep(2)
            else:
                print(f"⚠️  {service_name} 启动超时，但继续等待其他服务...")
        
        print("✅ 服务启动检查完成")
        return True
    
    def show_service_info(self):
        """显示服务信息"""
        print("\n" + "="*60)
        print("🎉 ConnectAI Docker开发环境启动成功！")
        print("="*60)
        print()
        print("📊 服务地址:")
        print("  • Manager Server:   http://localhost:3000")
        print("  • MySQL数据库:      localhost:3306")
        print("  • Redis缓存:        localhost:6379") 
        print("  • RabbitMQ管理:     http://localhost:15672")
        print("    (用户名: rabbitmq, 密码: rabbitmq)")
        print()
        print("👤 预置账号:")
        print("  • 邮箱: <EMAIL>")
        print("  • 密码: admin123")
        print()
        print("🔧 开发命令:")
        print("  • 查看日志: docker-compose -f docker-compose-local.yml logs -f")
        print("  • 停止服务: docker-compose -f docker-compose-local.yml down")
        print("  • 重启服务: docker-compose -f docker-compose-local.yml restart")
        print()
        print("📝 下一步:")
        print("  1. 启动前端: cd ConnectAI-E-AdminPanel && pnpm run dev")
        print("  2. 访问前端: http://localhost:3200")
        print("  3. 开始开发和调试")
        print()
        print("⏹️  按 Ctrl+C 停止所有服务")
        print("="*60)
    
    def stop_all_services(self):
        """停止所有服务"""
        print("⏹️  正在停止Docker服务...")
        
        manager_dir = Path("manager-server")
        result = self.run_command(
            "docker-compose -f docker-compose-local.yml down", 
            cwd=manager_dir,
            check=False
        )
        
        if result:
            print("✅ Docker服务已停止")
        else:
            print("⚠️  停止服务时出现问题")
    
    def run(self):
        """主运行流程"""
        print("🐳 ConnectAI Docker开发环境启动器")
        print("="*50)
        
        # 检查Docker环境
        if not self.check_docker():
            return False
        
        # 构建镜像
        if not self.build_image():
            return False
        
        # 准备数据目录
        if not self.prepare_data_dirs():
            return False
        
        # 启动服务
        if not self.start_services():
            return False
        
        # 等待服务启动
        if not self.wait_for_services():
            return False
        
        # 显示服务信息
        self.show_service_info()
        
        # 保持运行
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.signal_handler(signal.SIGINT, None)
        
        return True

def main():
    """主函数"""
    manager = DockerDevManager()
    success = manager.run()
    
    if not success:
        print("❌ 启动失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
