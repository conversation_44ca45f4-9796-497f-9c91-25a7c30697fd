# ConnectAI 远程服务器部署指南

本指南将帮助您将ConnectAI项目部署到远程服务器 `ubuntu@124.223.68.3`。

## 📋 部署前准备

### 本地环境要求
- **Windows系统**: 使用提供的 `.bat` 批处理文件
- **Linux/macOS系统**: 使用提供的 `.sh` shell脚本
- **Docker Desktop** (必需)
- **Node.js 和 npm/pnpm** (必需)
- **SSH客户端** (Windows 10/11自带，或安装Git Bash)

### 远程服务器要求
- Ubuntu 系统
- 至少 4GB 内存
- 至少 20GB 可用磁盘空间
- SSH访问权限

## 🚀 快速部署 (推荐)

### Windows用户
```cmd
# 1. 检查服务器环境
check-server.bat

# 2. 执行部署
deploy-windows.bat
```

### Linux/macOS用户
```bash
# 1. 检查服务器环境
./check-server.sh

# 2. 快速部署
./quick-deploy.sh

# 或使用完整部署脚本
./deploy-to-server.sh
```

## 📝 部署步骤详解

### 1. 环境检查
首先运行环境检查脚本，确保本地和远程环境满足要求：

**Windows:**
```cmd
check-server.bat
```

**Linux/macOS:**
```bash
./check-server.sh
```

### 2. SSH配置
确保可以无密码登录到远程服务器：

```bash
# 生成SSH密钥对 (如果没有)
ssh-keygen -t rsa -b 4096

# 复制公钥到服务器
ssh-copy-id ubuntu@124.223.68.3

# 测试连接
ssh ubuntu@124.223.68.3
```

### 3. 执行部署
根据您的操作系统选择相应的部署脚本：

**Windows:**
```cmd
deploy-windows.bat
```

**Linux/macOS (快速部署):**
```bash
./quick-deploy.sh
```

**Linux/macOS (完整部署):**
```bash
./deploy-to-server.sh
```

## 🔧 手动部署

如果自动部署脚本遇到问题，请参考 `manual-deploy-guide.md` 进行手动部署。

## 📱 访问服务

部署完成后，可以通过以下地址访问服务：

- **管理面板**: http://124.223.68.3:8080
- **API服务**: http://124.223.68.3:8081
- **知识库服务**: http://124.223.68.3:8082

## 🛠️ 服务管理

### 连接到服务器
```bash
ssh ubuntu@124.223.68.3
cd /opt/connectai/build
```

### 常用命令
```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs

# 重启特定服务
docker-compose restart [service_name]

# 停止所有服务
docker-compose down

# 启动所有服务
docker-compose up -d

# 查看资源使用情况
docker stats
```

## 🔍 故障排除

### 1. SSH连接问题
```bash
# 检查SSH配置
ssh -v ubuntu@124.223.68.3

# 使用密码登录 (如果密钥认证失败)
ssh -o PreferredAuthentications=password ubuntu@124.223.68.3
```

### 2. Docker权限问题
```bash
# 在服务器上添加用户到docker组
sudo usermod -aG docker $USER
# 重新登录生效
```

### 3. 端口冲突
如果端口被占用，修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "9080:80"  # 将8080改为9080
```

### 4. 内存不足
调整Elasticsearch内存设置：
```yaml
environment:
  - "ES_JAVA_OPTS=-Xms256m -Xmx256m"
```

### 5. 磁盘空间不足
清理Docker资源：
```bash
docker system prune -f
docker volume prune -f
```

## 📊 监控和维护

### 查看系统资源
```bash
# 内存使用
free -h

# 磁盘使用
df -h

# Docker容器资源使用
docker stats

# 查看服务日志
docker-compose logs -f [service_name]
```

### 备份数据
```bash
cd /opt/connectai/build
tar -zcf backup-$(date +%Y%m%d).tar.gz data/
```

### 更新服务
```bash
# 拉取新镜像
docker-compose pull

# 重启服务
docker-compose up -d
```

## 🔐 安全建议

1. **防火墙配置**
```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 8080
sudo ufw allow 8081
sudo ufw allow 8082
```

2. **定期更新系统**
```bash
sudo apt update && sudo apt upgrade -y
```

3. **监控日志**
```bash
# 查看系统日志
sudo journalctl -f

# 查看Docker日志
docker-compose logs -f
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 检查 `manual-deploy-guide.md` 中的详细说明
2. 查看服务日志: `docker-compose logs`
3. 检查系统资源: `free -h` 和 `df -h`
4. 确认网络连接: `ping google.com`

## 📄 文件说明

- `deploy-windows.bat` - Windows自动部署脚本
- `quick-deploy.sh` - Linux/macOS快速部署脚本
- `deploy-to-server.sh` - Linux/macOS完整部署脚本
- `check-server.bat` - Windows服务器检查脚本
- `check-server.sh` - Linux/macOS服务器检查脚本
- `manual-deploy-guide.md` - 手动部署详细指南
- `DEPLOYMENT_README.md` - 本文件，部署总览
