import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2.227 8.78a2.25 2.25 0 0 0-.216-.171l-.008-.011a2.214 2.214 0 0 0-.65-.365l-1.07-.348A.425.425 0 0 1 0 7.486a.419.419 0 0 1 .283-.399l1.07-.348c.273-.098.524-.249.739-.445a.675.675 0 0 1 .05-.039a.5.5 0 0 0 .064-.053c.231-.235.407-.52.514-.832l.01-.027l.348-1.07a.422.422 0 0 1 .399-.279c.087 0 .173.027.244.078a.425.425 0 0 1 .119.156c.004.008.01.015.017.022c.007.008.015.016.019.026l.347 1.07c.04.104.085.204.138.3c.103.21.238.402.4.568c.243.243.539.426.865.533l1.07.348l.022.006a.422.422 0 0 1 .278.4a.43.43 0 0 1-.278.399l-1.071.347A2.2 2.2 0 0 0 4.25 9.644l-.35 1.07l-.01.026a.429.429 0 0 1-.143.178a.418.418 0 0 1-.485-.001a.418.418 0 0 1-.155-.2l-.348-1.07a2.218 2.218 0 0 0-.532-.867zM5.006 4h17.989A2.997 2.997 0 0 1 26 7v13.995a3 3 0 0 1-2.998 3H5.012a2.998 2.998 0 0 1-2.998-3V10.577l.146.449c.098.288.284.538.532.714c.24.168.525.258.818.258v8.997a1.5 1.5 0 0 0 1.499 1.5h3.244v-4a2 2 0 0 1 1.998-1.999h7.496a1.998 1.998 0 0 1 1.999 2v3.999h3.249a1.499 1.499 0 0 0 1.499-1.5V6.999a1.5 1.5 0 0 0-1.5-1.5H5.466a1.2 1.2 0 0 1-.288-.461l-.332-1.02a.773.773 0 0 0 .071-.01c.03-.004.059-.008.09-.008zm12.491 7.498a3.5 3.5 0 0 0-5.971-2.474a3.5 3.5 0 0 0 4.947 4.948a3.5 3.5 0 0 0 1.024-2.474zm-11.83.772l.611-.2c.181-.061.346-.162.483-.295a1.266 1.266 0 0 0 .3-.492l.2-.612a.244.244 0 0 1 .137-.144a.25.25 0 0 1 .27.063c.021.024.037.051.048.081l.2.612c.06.186.163.355.3.494c.139.138.308.243.494.305l.612.2h.012a.247.247 0 0 1 .117.088a.24.24 0 0 1 0 .279a.238.238 0 0 1-.117.088l-.612.2c-.186.06-.355.163-.494.3c-.137.139-.24.308-.3.493l-.2.612a.238.238 0 0 1-.228.161a.244.244 0 0 1-.228-.161l-.2-.612a1.267 1.267 0 0 0-.3-.5a1.266 1.266 0 0 0-.494-.306l-.612-.2a.241.241 0 0 1 0-.455z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoPersonSparkle28Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
