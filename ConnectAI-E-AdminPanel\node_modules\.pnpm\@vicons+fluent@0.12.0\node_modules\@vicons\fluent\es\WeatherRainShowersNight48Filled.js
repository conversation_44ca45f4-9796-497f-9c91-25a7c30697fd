import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M26.001 12c6.337 0 9.932 4.195 10.455 9.26h.16c4.078 0 7.384 3.298 7.384 7.365c0 4.068-3.306 7.365-7.384 7.365h-.069l-4.3 7.159a1.75 1.75 0 0 1-3.075-1.662l.075-.14l3.217-5.357h-3.917l-4.3 7.159a1.75 1.75 0 0 1-3.075-1.662l.075-.14l3.217-5.357h-3.917l-4.3 7.159a1.75 1.75 0 0 1-3.075-1.662l.075-.14l3.217-5.357h-1.078c-4.078 0-7.384-3.297-7.384-7.365c0-4.067 3.306-7.365 7.384-7.365h.16c.526-5.099 4.118-9.26 10.455-9.26zM13.178 4.002c1.59.086 3.134.544 4.526 1.348a10.112 10.112 0 0 1 4.418 5.193c-4.016 1.144-6.877 4.083-8.027 8.049l-.092.332l-.115.476l-.413.077a9.353 9.353 0 0 0-5.616 3.632l-.31-.172a10.122 10.122 0 0 1-3.284-3.028a1.501 1.501 0 0 1 .731-2.265c3.285-1.176 5.055-2.5 6.067-4.432c1.105-2.11 1.31-4.348.576-7.354a1.502 1.502 0 0 1 1.539-1.856z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherRainShowersNight48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
