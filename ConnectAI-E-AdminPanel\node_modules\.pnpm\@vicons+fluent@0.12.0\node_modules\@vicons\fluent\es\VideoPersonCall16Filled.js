import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M1 4.75C1 3.784 1.784 3 2.75 3h10.5c.966 0 1.75.784 1.75 1.75v2.187a2.159 2.159 0 0 0-.59-.295l-.41-.13V4.75a.75.75 0 0 0-.75-.75H2.75a.75.75 0 0 0-.75.75v6.5c0 .414.336.75.75.75H5v-1a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v.205a1.782 1.782 0 0 0-1.51.787l-.481.706l-.072.12l-.013.022a1.848 1.848 0 0 0-.076.16H2.75A1.75 1.75 0 0 1 1 11.25v-6.5zm13.92 3.755c.052.23.079.472.08.725v.028c-.003.874-.315 1.881-.936 3.02c-.13.24-.262.464-.397.672c-.697 1.081-1.448 1.747-2.254 1.999a1.139 1.139 0 0 1-1.076-.222l-.08-.073l-.309-.31a.88.88 0 0 1-.144-1.03l.046-.074l.467-.686a.785.785 0 0 1 .708-.346c.03.002.061.007.092.013l.082.023l.861.287c.344-.26.63-.588.86-.982c.197-.338.325-.681.386-1.029l.025-.174l-.717-.68a.875.875 0 0 1-.226-.888l.03-.085l.326-.767a.784.784 0 0 1 .877-.482l.077.02l.407.13c.405.13.714.476.814.91zM10 7a2 2 0 1 1-4 0a2 2 0 0 1 4 0z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoPersonCall16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
