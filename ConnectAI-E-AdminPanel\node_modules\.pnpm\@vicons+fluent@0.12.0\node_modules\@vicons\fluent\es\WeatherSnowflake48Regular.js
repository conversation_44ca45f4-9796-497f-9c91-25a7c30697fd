import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M24.26 4.05c.646 0 1.179.492 1.243 1.122l.006.128l-.001 7.775l5.121-4.499a1.25 1.25 0 1 1 1.65 1.878l-6.77 5.949L25.506 23h6.605l5.95-6.768a1.25 1.25 0 0 1 1.657-.198l.107.084a1.25 1.25 0 0 1 .198 1.657l-.084.108l-4.5 5.116h7.773c.648 0 1.18.493 1.244 1.123l.006.128a1.25 1.25 0 0 1-1.122 1.243l-.127.007h-7.778l4.5 5.122a1.25 1.25 0 0 1-.017 1.669l-.097.095a1.25 1.25 0 0 1-1.67-.016l-.095-.098l-5.95-6.771l-6.601-.001l-.001 6.6l6.775 5.953a1.25 1.25 0 0 1 .198 1.657l-.084.107a1.25 1.25 0 0 1-1.657.199l-.107-.085l-5.125-4.503l-.002 7.778a1.25 1.25 0 0 1-1.122 1.243l-.128.007a1.25 1.25 0 0 1-1.243-1.123l-.007-.128v-7.771l-5.112 4.497a1.25 1.25 0 0 1-1.651-1.878l6.765-5.948v-6.604h-6.142a1.249 1.249 0 0 1-.08.102l-6.322 7.195a1.25 1.25 0 0 1-1.878-1.65l4.96-5.647l-8.236.001a1.25 1.25 0 0 1-1.244-1.122l-.006-.128c0-.647.491-1.18 1.122-1.243l.128-.007h7.308l-4.032-4.595a1.25 1.25 0 1 1 1.879-1.649l5.479 6.243h7.066V16.4l-6.767-5.95a1.25 1.25 0 0 1-.198-1.657l.084-.108a1.25 1.25 0 0 1 1.657-.197l.108.084l5.118 4.5l.001-7.772c0-.604.429-1.108.998-1.225l.124-.019l.128-.006z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSnowflake48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
