import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.28 2.22a.75.75 0 0 0-1.06 1.06l2.889 2.89a9.492 9.492 0 0 0-1.77 1.393a9.842 9.842 0 0 0-1.104 1.338a.75.75 0 1 0 1.233.855c.292-.42.611-.811.932-1.132a7.971 7.971 0 0 1 1.807-1.357l.965.966A6.436 6.436 0 0 0 5.48 9.45a6.403 6.403 0 0 0-1.192 1.644a.75.75 0 0 0 1.338.677c.235-.464.54-.885.914-1.26c.52-.52 1.126-.9 1.773-1.14l1.281 1.282a4.113 4.113 0 0 0-2.48 1.186a4.145 4.145 0 0 0-.86 1.274a.75.75 0 1 0 1.374.6c.136-.31.32-.586.547-.813a2.631 2.631 0 0 1 3.376-.29l5.17 5.17a.75.75 0 1 0 1.06-1.061L3.28 2.22zm8.088 7.026a4.92 4.92 0 0 1 2.161 1.266c.359.358.676.803.918 1.275a.75.75 0 1 0 1.335-.683a6.621 6.621 0 0 0-1.192-1.653a6.426 6.426 0 0 0-4.896-1.878l1.674 1.673zM8.55 6.43a7.96 7.96 0 0 1 8.06 3.33a.75.75 0 1 0 1.235-.852A9.466 9.466 0 0 0 7.31 5.188l1.24 1.24zm2.371 9.21a1.242 1.242 0 1 0-1.757-1.757a1.242 1.242 0 0 0 1.757 1.757z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WifiOff20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
