import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M18.748 9.494a3.251 3.251 0 0 1 .184 6.495l-.2.005l-.116.007h-.787c.11.31.17.644.17.993c0 1.664-1.262 3.006-2.926 3.006c-1.3 0-2.235-.628-2.688-1.627a1 1 0 0 1 1.767-.93l.098.189c.14.242.373.368.823.368c.53 0 .925-.42.925-1.006c0-.511-.393-.936-.904-.993H3a1 1 0 0 1-.117-1.994L3 14.002h11.859l.117-.007c.063 0 .126.002.189.006l3.462.001l.06-.005l.189-.008a1.25 1.25 0 1 0-1.254-1.793l-.097.226a1 1 0 0 1-1.83-.796a3.253 3.253 0 0 1 3.053-2.13zM9.75 17.5a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zm2-14.001a4.25 4.25 0 0 1 .023 8.5H3a1 1 0 0 1-.117-1.993L3 10h8.75l.154-.006A2.25 2.25 0 1 0 9.499 7.75a1 1 0 0 1-2 0a4.25 4.25 0 0 1 4.25-4.25zm7.5 3a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zM5.75 5a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherBlowingSnow24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
