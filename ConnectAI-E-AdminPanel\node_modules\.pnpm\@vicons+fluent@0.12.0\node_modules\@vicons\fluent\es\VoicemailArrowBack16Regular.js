import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.5 5.75c0-.268-.06-.523-.168-.75h1.336a1.743 1.743 0 0 0-.163.886a5.48 5.48 0 0 1 1.127-.56a.75.75 0 0 1 .948-.25a5.537 5.537 0 0 1 1.256-.066A1.75 1.75 0 0 0 9.25 4h-4.5A1.75 1.75 0 1 0 6.5 5.75zm-1.75.75a.75.75 0 1 1 0-1.5a.75.75 0 0 1 0 1.5zM12 5.207V2.5a.5.5 0 0 0-.5-.5h-9a.5.5 0 0 0-.5.5V9a.5.5 0 0 0 .5.5h2.59c-.059.324-.09.659-.09 1H2.5A1.5 1.5 0 0 1 1 9V2.5A1.5 1.5 0 0 1 2.5 1h9A1.5 1.5 0 0 1 13 2.5v3.1a5.465 5.465 0 0 0-1-.393zm3 5.293a4.5 4.5 0 1 1-9 0a4.5 4.5 0 0 1 9 0zM9.604 7.896a.5.5 0 0 0-.708 0l-1.75 1.75a.498.498 0 0 0-.002.705l1.752 1.753a.5.5 0 0 0 .708-.708l-.897-.896h1.543c.966 0 1.75.784 1.75 1.75v.25a.5.5 0 0 0 1 0v-.25a2.75 2.75 0 0 0-2.75-2.75H8.707l.897-.896a.5.5 0 0 0 0-.708z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VoicemailArrowBack16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
