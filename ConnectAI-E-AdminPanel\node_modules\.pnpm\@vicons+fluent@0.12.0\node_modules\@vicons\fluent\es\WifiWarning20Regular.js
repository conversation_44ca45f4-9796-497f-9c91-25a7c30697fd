import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M16.833 7.384c.41.409.796.877 1.133 1.365a.5.5 0 1 1-.823.568A8.612 8.612 0 0 0 3.947 8.091A9.02 9.02 0 0 0 2.94 9.313a.5.5 0 1 1-.821-.57c.346-.5.728-.966 1.121-1.36a9.612 9.612 0 0 1 13.593 0zM5.476 9.357a6.45 6.45 0 0 1 8.756-.339c-.374.05-.735.203-1.034.46a5.451 5.451 0 0 0-8.023 1.976a.5.5 0 1 1-.892-.452a6.41 6.41 0 0 1 1.193-1.645zm4.305 6.62l1.122-2.247a1.298 1.298 0 1 0-1.122 2.246zm1.906-3.817l.449-.899a4.035 4.035 0 0 0-4.951.593a4.05 4.05 0 0 0-.84 1.244a.5.5 0 0 0 .916.4c.155-.355.368-.674.63-.937a3.034 3.034 0 0 1 3.796-.4zm1.916-1.606l-3.496 6.998A1 1 0 0 0 11.002 19h6.996a1 1 0 0 0 .895-1.448l-3.5-6.999a1 1 0 0 0-1.79 0zm1.395 1.941v3.002a.5.5 0 1 1-1 0v-3.002a.5.5 0 1 1 1 0zm-.5 5.504a.5.5 0 1 1 0-1a.5.5 0 0 1 0 1z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WifiWarning20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
