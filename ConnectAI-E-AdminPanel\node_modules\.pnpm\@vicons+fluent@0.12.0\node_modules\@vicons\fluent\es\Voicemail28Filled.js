import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M7.5 14a2 2 0 1 1 4 0a2 2 0 0 1-4 0z',
      fill: 'currentColor'
    }),
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M16.5 14a2 2 0 1 1 4 0a2 2 0 0 1-4 0z',
      fill: 'currentColor'
    }),
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5.754 3.998a3.75 3.75 0 0 0-3.75 3.75v12.5a3.75 3.75 0 0 0 3.75 3.75H22.25a3.75 3.75 0 0 0 3.75-3.75v-12.5a3.75 3.75 0 0 0-3.75-3.75H5.755zM12.373 12A3.5 3.5 0 1 1 9.5 10.5h9a3.5 3.5 0 1 1-2.873 1.5h-3.254z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Voicemail28Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
