import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M14.5 13.444l.155.146l5.828 5.867l.01-.078l.007-.128V13.75a.75.75 0 0 1 1.493-.102l.007.102v5.502a2.75 2.75 0 0 1-2.582 2.745l-.168.005h-5.5a.75.75 0 0 1-.102-1.493l.102-.007h5.5c.05 0 .098-.003.146-.008l-5.802-5.842a2.25 2.25 0 0 0-3.053-.12l-.129.12l-5.866 5.834l.076.01l.128.006h5.5l.102.007a.75.75 0 0 1 0 1.486l-.102.007h-5.5l-.168-.005a2.75 2.75 0 0 1-2.577-2.57L2 19.25V13.75l.007-.102a.75.75 0 0 1 1.486 0l.007.102v5.502l.009.147l5.842-5.808a3.75 3.75 0 0 1 5.149-.146zm1.523-7.432a2.007 2.007 0 1 1 0 4.014a2.007 2.007 0 0 1 0-4.014zM10.25 1.999a.75.75 0 0 1 .102 1.493l-.102.007h-5.5a1.25 1.25 0 0 0-1.244 1.122L3.5 4.75v5.502a.75.75 0 0 1-1.493.102L2 10.251V4.75a2.75 2.75 0 0 1 2.582-2.745l.168-.005h5.5zm9 0l.168.005a2.75 2.75 0 0 1 2.577 2.57L22 4.75v5.502l-.007.102a.75.75 0 0 1-1.486 0l-.007-.102V4.75l-.006-.128a1.25 1.25 0 0 0-1.116-1.116L19.25 3.5h-5.5l-.102-.007a.75.75 0 0 1 0-1.486l.102-.007h5.5zm-3.227 5.513a.507.507 0 1 0 0 1.014a.507.507 0 0 0 0-1.014z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wallpaper24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
