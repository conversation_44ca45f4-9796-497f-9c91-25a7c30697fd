@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 ConnectAI Windows 部署脚本
echo 目标服务器: ubuntu@************
echo.

REM 检查必要工具
echo 🔍 检查本地环境...

where docker >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)
echo ✅ Docker已安装

where ssh >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ SSH客户端未找到，请安装OpenSSH或Git Bash
    pause
    exit /b 1
)
echo ✅ SSH客户端可用

where pnpm >nul 2>&1
if %errorlevel% neq 0 (
    where npm >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ npm和pnpm都未安装，请先安装Node.js
        pause
        exit /b 1
    )
    set NPM_CMD=npm
    echo ⚠️  使用npm替代pnpm
) else (
    set NPM_CMD=pnpm
    echo ✅ pnpm已安装
)

REM 检查SSH连接
echo.
echo 📡 检查SSH连接...
ssh -o ConnectTimeout=10 -o BatchMode=yes ubuntu@************ exit >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 无法连接到服务器，请检查SSH配置
    echo 提示：您可能需要先配置SSH密钥或使用密码登录
    pause
    exit /b 1
)
echo ✅ SSH连接成功

REM 构建前端项目
echo.
echo 🔨 构建前端项目...

echo 构建管理面板...
cd ConnectAI-E-AdminPanel
%NPM_CMD% install
if %errorlevel% neq 0 (
    echo ❌ 管理面板依赖安装失败
    pause
    exit /b 1
)

%NPM_CMD% run build
if %errorlevel% neq 0 (
    echo ❌ 管理面板构建失败
    pause
    exit /b 1
)
cd ..

echo 构建消息Web界面...
cd Lark-Messenger-Web
%NPM_CMD% install
if %errorlevel% neq 0 (
    echo ❌ 消息Web界面依赖安装失败
    pause
    exit /b 1
)

%NPM_CMD% run build
if %errorlevel% neq 0 (
    echo ❌ 消息Web界面构建失败
    pause
    exit /b 1
)
cd ..

REM 构建Docker镜像
echo.
echo 🐳 构建Docker镜像...

echo 构建知识服务器镜像...
docker build -t know-server:2.0.1-privatization -f ./DataChat-API/docker/Dockerfile ./DataChat-API
if %errorlevel% neq 0 (
    echo ❌ 知识服务器镜像构建失败
    pause
    exit /b 1
)

echo 构建管理服务器镜像...
docker build -t connectai-manager:2.0.1-privatization -f ./manager-server/docker/Dockerfile ./manager-server
if %errorlevel% neq 0 (
    echo ❌ 管理服务器镜像构建失败
    pause
    exit /b 1
)

REM 准备部署文件
echo.
echo 📦 准备部署文件...

if exist build rmdir /s /q build
xcopy /e /i deploy build >nul

if not exist build\dist mkdir build\dist
xcopy /e /y ConnectAI-E-AdminPanel\dist\* build\dist\ >nul
xcopy /e /y Lark-Messenger-Web\dist\* build\dist\ >nul
if not exist build\dist\upload mkdir build\dist\upload

REM 更新docker-compose.yml (Windows版本)
powershell -Command "(Get-Content build\docker-compose.yml) -replace 'know-server:es', 'know-server:2.0.1-privatization' | Set-Content build\docker-compose.yml"
powershell -Command "(Get-Content build\docker-compose.yml) -replace 'connectai-manager:1.0', 'connectai-manager:2.0.1-privatization' | Set-Content build\docker-compose.yml"

REM 创建部署包
echo 创建部署包...
tar -zcf deploy-2.0.1-privatization.tar.gz build/
if %errorlevel% neq 0 (
    echo ❌ 部署包创建失败，请确保tar命令可用
    pause
    exit /b 1
)

REM 导出Docker镜像
echo.
echo 📤 导出Docker镜像...

echo 导出基础镜像...
docker save docker.elastic.co/elasticsearch/elasticsearch:8.9.0 lloydzhou/nchan jwilder/nginx-proxy:alpine mysql:5.7 rabbitmq:3.7-management-alpine redis:alpine | gzip > images-base.tar.gz
if %errorlevel% neq 0 (
    echo ❌ 基础镜像导出失败
    pause
    exit /b 1
)

echo 导出应用镜像...
docker save know-server:2.0.1-privatization connectai-manager:2.0.1-privatization | gzip > images-2.0.1-privatization.tar.gz
if %errorlevel% neq 0 (
    echo ❌ 应用镜像导出失败
    pause
    exit /b 1
)

REM 上传文件
echo.
echo 📤 上传文件到服务器...

ssh ubuntu@************ "sudo mkdir -p /opt/connectai && sudo chown $USER:$USER /opt/connectai"

echo 上传部署包...
scp deploy-2.0.1-privatization.tar.gz ubuntu@************:/opt/connectai/
if %errorlevel% neq 0 (
    echo ❌ 部署包上传失败
    pause
    exit /b 1
)

echo 上传基础镜像...
scp images-base.tar.gz ubuntu@************:/opt/connectai/
if %errorlevel% neq 0 (
    echo ❌ 基础镜像上传失败
    pause
    exit /b 1
)

echo 上传应用镜像...
scp images-2.0.1-privatization.tar.gz ubuntu@************:/opt/connectai/
if %errorlevel% neq 0 (
    echo ❌ 应用镜像上传失败
    pause
    exit /b 1
)

REM 创建远程部署脚本
echo.
echo 📝 创建远程部署脚本...

echo #!/bin/bash > remote-deploy.sh
echo set -e >> remote-deploy.sh
echo. >> remote-deploy.sh
echo DEPLOY_PATH="/opt/connectai" >> remote-deploy.sh
echo cd $DEPLOY_PATH >> remote-deploy.sh
echo. >> remote-deploy.sh
echo echo "🔧 检查Docker环境..." >> remote-deploy.sh
echo if ! command -v docker ^&^> /dev/null; then >> remote-deploy.sh
echo     curl -fsSL https://get.docker.com -o get-docker.sh >> remote-deploy.sh
echo     sudo sh get-docker.sh >> remote-deploy.sh
echo     sudo usermod -aG docker $USER >> remote-deploy.sh
echo     echo "⚠️  Docker已安装，请重新登录后再次运行部署" >> remote-deploy.sh
echo     exit 1 >> remote-deploy.sh
echo fi >> remote-deploy.sh
echo. >> remote-deploy.sh
echo if ! command -v docker-compose ^&^> /dev/null; then >> remote-deploy.sh
echo     sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose >> remote-deploy.sh
echo     sudo chmod +x /usr/local/bin/docker-compose >> remote-deploy.sh
echo fi >> remote-deploy.sh
echo. >> remote-deploy.sh
echo echo "📦 解压部署包..." >> remote-deploy.sh
echo tar -zxf deploy-2.0.1-privatization.tar.gz >> remote-deploy.sh
echo. >> remote-deploy.sh
echo echo "🐳 加载Docker镜像..." >> remote-deploy.sh
echo docker load ^< images-base.tar.gz >> remote-deploy.sh
echo docker load ^< images-2.0.1-privatization.tar.gz >> remote-deploy.sh
echo. >> remote-deploy.sh
echo echo "🛑 停止现有服务..." >> remote-deploy.sh
echo cd build >> remote-deploy.sh
echo docker-compose down 2^>/dev/null ^|^| true >> remote-deploy.sh
echo. >> remote-deploy.sh
echo echo "📁 创建数据目录..." >> remote-deploy.sh
echo mkdir -p data/{mysql/data,mysql/conf.d,rabbitmq,elasticsearch,redis,files,search_index} >> remote-deploy.sh
echo sudo chown -R 1000:1000 data/elasticsearch 2^>/dev/null ^|^| true >> remote-deploy.sh
echo sudo chmod -R 755 data >> remote-deploy.sh
echo. >> remote-deploy.sh
echo echo "🚀 启动服务..." >> remote-deploy.sh
echo docker-compose up -d >> remote-deploy.sh
echo. >> remote-deploy.sh
echo echo "⏳ 等待服务启动..." >> remote-deploy.sh
echo sleep 30 >> remote-deploy.sh
echo. >> remote-deploy.sh
echo echo "📊 检查服务状态..." >> remote-deploy.sh
echo docker-compose ps >> remote-deploy.sh
echo. >> remote-deploy.sh
echo echo "🎉 部署完成！" >> remote-deploy.sh
echo echo "📱 访问地址：" >> remote-deploy.sh
echo echo "   管理面板: http://************:8080" >> remote-deploy.sh
echo echo "   API服务:  http://************:8081" >> remote-deploy.sh
echo echo "   知识库:   http://************:8082" >> remote-deploy.sh

scp remote-deploy.sh ubuntu@************:/opt/connectai/
ssh ubuntu@************ "chmod +x /opt/connectai/remote-deploy.sh"

REM 执行远程部署
echo.
echo 🎯 执行远程部署...
ssh ubuntu@************ "/opt/connectai/remote-deploy.sh"

REM 清理本地文件
echo.
echo 🧹 清理本地文件...
del deploy-2.0.1-privatization.tar.gz
del images-base.tar.gz
del images-2.0.1-privatization.tar.gz
del remote-deploy.sh
rmdir /s /q build

echo.
echo ✅ 部署完成！
echo 🌐 请访问: http://************:8080
echo.
pause
