@echo off
chcp 65001 >nul

echo 🔍 ConnectAI 服务器环境检查
echo 目标服务器: ubuntu@124.223.68.3
echo.

REM 检查SSH客户端
where ssh >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ SSH客户端未找到，请安装OpenSSH或Git Bash
    pause
    exit /b 1
)

echo 📡 检查SSH连接...
ssh -o ConnectTimeout=10 -o BatchMode=yes ubuntu@124.223.68.3 exit >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ SSH连接失败
    echo 请检查：
    echo   1. 服务器地址是否正确
    echo   2. SSH密钥是否配置
    echo   3. 服务器是否在线
    pause
    exit /b 1
)
echo ✅ SSH连接成功

echo.
echo 🖥️  正在检查远程服务器环境...
echo.

ssh ubuntu@124.223.68.3 "
echo '🖥️  系统信息:'
echo '操作系统:' \$(lsb_release -d | cut -f2)
echo '内核版本:' \$(uname -r)
echo '架构:' \$(uname -m)
echo ''

echo '💾 内存信息:'
free -h
echo ''

echo '💿 磁盘空间:'
df -h
echo ''

echo '🔧 检查Docker环境:'
if command -v docker &> /dev/null; then
    echo '✅ Docker已安装:' \$(docker --version)
    if docker ps &> /dev/null; then
        echo '✅ Docker服务运行正常'
    else
        echo '⚠️  Docker服务未运行或权限不足'
    fi
else
    echo '❌ Docker未安装'
fi

if command -v docker-compose &> /dev/null; then
    echo '✅ Docker Compose已安装:' \$(docker-compose --version)
else
    echo '❌ Docker Compose未安装'
fi
echo ''

echo '🌐 网络检查:'
if ping -c 1 google.com &> /dev/null; then
    echo '✅ 外网连接正常'
else
    echo '⚠️  外网连接异常'
fi
echo ''

echo '🔥 防火墙状态:'
if command -v ufw &> /dev/null; then
    sudo ufw status
else
    echo 'UFW防火墙未安装'
fi
echo ''

echo '📊 端口占用检查:'
for port in 8080 8081 8082 3306 6379 5672 9200; do
    if netstat -tuln | grep \":\$port \" &> /dev/null; then
        echo \"⚠️  端口 \$port 已被占用\"
    else
        echo \"✅ 端口 \$port 可用\"
    fi
done
echo ''

echo '📁 检查部署目录:'
if [ -d '/opt/connectai' ]; then
    echo '✅ 部署目录存在: /opt/connectai'
    ls -la /opt/connectai
else
    echo '📁 部署目录不存在，将在部署时创建'
fi
echo ''

echo '🔍 检查完成！'
"

echo.
echo 💡 建议：
echo 1. 确保服务器至少有4GB内存
echo 2. 确保至少有20GB可用磁盘空间  
echo 3. 如果Docker未安装，部署脚本会自动安装
echo 4. 如果端口被占用，请先停止相关服务
echo.
pause
