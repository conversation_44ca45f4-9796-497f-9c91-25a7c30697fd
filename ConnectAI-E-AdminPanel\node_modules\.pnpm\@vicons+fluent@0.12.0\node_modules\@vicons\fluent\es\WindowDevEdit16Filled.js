import { openBlock as _openBlock, createElementBlock as _createElementBlock, createStaticVNode as _createStaticVNode, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createStaticVNode('<g fill="none"><g clip-path="url(#clip0_118447_756)"><path d="M4.75 2A2.75 2.75 0 0 0 2 4.75v6a2.75 2.75 0 0 0 2.75 2.75h2.422l.177-.709c.069-.275.168-.54.295-.791H4.75c-.69 0-1.25-.56-1.25-1.25V5H12v2.379l.63-.631c.257-.256.553-.447.87-.571V4.75A2.75 2.75 0 0 0 10.75 2h-6zm6.238 6.39a.5.5 0 0 0-.134-.243l-2-2a.5.5 0 0 0-.707.708L9.793 8.5l-1.646 1.646a.5.5 0 0 0 .243.842l2.598-2.598zM6.853 6.856a.5.5 0 0 0-.707-.708l-2 2a.5.5 0 0 0 0 .708l2 2a.5.5 0 1 0 .707-.708L5.207 8.501l1.646-1.646zm8.692.6a1.56 1.56 0 0 0-2.207 0l-4.289 4.288a2.777 2.777 0 0 0-.73 1.29l-.303 1.212a.61.61 0 0 0 .739.739l1.211-.303a2.778 2.778 0 0 0 1.29-.73l4.289-4.289a1.56 1.56 0 0 0 0-2.207z" fill="currentColor"></path></g><defs><clipPath id="clip0_118447_756"><path fill="#fff" d="M0 0h16v16H0z"></path></clipPath></defs></g>', 1)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowDevEdit16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
