import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4 5.75A2.75 2.75 0 0 1 6.75 3h5.5A2.75 2.75 0 0 1 15 5.75v6.5A2.75 2.75 0 0 1 12.25 15h-5.5A2.75 2.75 0 0 1 4 12.25v-6.5zm.194 9.245a6.369 6.369 0 0 0-1.091.479C2.603 15.762 2 16.25 2 17s.603 1.238 1.103 1.526c.556.32 1.311.583 2.179.794c1.734.421 4.085.676 6.657.68l-.716.717a.75.75 0 0 0 1.06 1.06l1.997-1.997a.75.75 0 0 0 0-1.06l-1.996-2a.75.75 0 1 0-1.062 1.06l.72.72c-2.49-.004-4.717-.252-6.305-.638c-.807-.196-1.406-.418-1.786-.636A1.427 1.427 0 0 1 3.538 17c.046-.048.137-.125.313-.226c.38-.218.98-.44 1.786-.636c.228-.056.47-.109.723-.158a3.736 3.736 0 0 1-2.166-.985zm9.829.56c1.688.094 3.184.301 4.34.583c.807.196 1.406.418 1.786.636c.175.101.266.178.313.226a.718.718 0 0 1-.015.014c-.102.097-.291.225-.6.365c-.615.279-1.543.536-2.722.734a.75.75 0 1 0 .249 1.48c1.238-.209 2.31-.493 3.092-.848c.389-.176.746-.387 1.017-.646c.272-.26.517-.63.517-1.099c0-.75-.603-1.238-1.103-1.526c-.556-.32-1.311-.583-2.179-.794c-.925-.225-2.025-.402-3.241-.52a3.768 3.768 0 0 1-1.454 1.395zm6.486 1.387l.002-.003l-.002.003zM20 5.246a.75.75 0 0 0-1.218-.586l-2.783 2.226v4.225l2.783 2.221A.75.75 0 0 0 20 12.746v-7.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoSwitch24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
