import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.332 5A1.75 1.75 0 1 1 4.75 4h4.5a1.75 1.75 0 0 1 1.586 1.01a5.59 5.59 0 0 0-1.256.067a.747.747 0 0 0-.948.248a5.48 5.48 0 0 0-1.127.561A1.776 1.776 0 0 1 7.668 5H6.332zM4 5.75a.75.75 0 1 0 1.5 0a.75.75 0 0 0-1.5 0zm8-3.25v2.707c.349.099.683.23 1 .393V2.5A1.5 1.5 0 0 0 11.5 1h-9A1.5 1.5 0 0 0 1 2.5V9a1.5 1.5 0 0 0 1.5 1.5H5c0-.341.031-.676.09-1H2.5A.5.5 0 0 1 2 9V2.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 .5.5zm3 8a4.5 4.5 0 1 1-9 0a4.5 4.5 0 0 1 9 0zm-2 0a.5.5 0 0 0-.5-.5h-4a.5.5 0 0 0 0 1h4a.5.5 0 0 0 .5-.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VoicemailSubtract16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
