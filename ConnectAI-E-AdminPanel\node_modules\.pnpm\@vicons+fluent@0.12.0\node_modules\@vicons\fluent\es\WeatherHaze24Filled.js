import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.842 13.984a5.647 5.647 0 0 1 1.713.021c1.293.21 2.522.838 3.823 1.597c2.357 1.375 4.946 1.102 6.436.037a.75.75 0 1 1 .872 1.221c-2.01 1.436-5.24 1.685-8.064.038c-1.29-.753-2.31-1.25-3.307-1.412c-.957-.156-1.955-.01-3.206.672a.75.75 0 1 1-.718-1.317c.861-.47 1.668-.747 2.45-.857zm2.798-2.707c-.359-.12-.72-.213-1.086-.273a5.665 5.665 0 0 0-1.994.026c-.695.132-1.411.397-2.17.81a.75.75 0 0 0 .719 1.318c1.25-.683 2.248-.828 3.205-.673c.278.046.558.117.846.213c.742.247 1.531.657 2.46 1.199l.182.103c2.78 1.533 5.916 1.264 7.883-.14a.75.75 0 0 0-.871-1.221c-.41.292-.9.524-1.448.68c-1.08.307-2.378.311-3.678-.123a6.997 6.997 0 0 1-1.31-.595c-.934-.544-1.83-1.02-2.738-1.324zm7.885.723c0 .07-.002.14-.004.21c-1.209.488-2.964.505-4.64-.473c-1.31-.764-2.677-1.478-4.167-1.72a6.671 6.671 0 0 0-1.856-.044A5.527 5.527 0 0 1 17.525 12zm-5.403 10a.758.758 0 0 1-.252 0h.252zM5.974 4.94l-.084-.072a.75.75 0 0 0-.977 1.133l1.06 1.061l.085.073A.75.75 0 0 0 7.034 6l-1.06-1.06zm13.177.977a.75.75 0 0 0-1.133-.976L16.957 6l-.073.085a.75.75 0 0 0 1.134.976l1.06-1.06l.073-.085zm-6.408-3.28a.75.75 0 0 0-1.493.102v1.5l.007.102a.75.75 0 0 0 1.493-.102v-1.5l-.007-.102zM5.11 19.158c1.251-.682 2.249-.828 3.206-.672c.996.162 2.018.66 3.307 1.412c2.825 1.647 6.055 1.398 8.064-.038a.75.75 0 1 0-.872-1.22c-1.49 1.064-4.079 1.337-6.436-.038c-1.301-.76-2.53-1.387-3.823-1.597c-1.333-.216-2.665.019-4.164.837a.75.75 0 0 0 .718 1.316z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherHaze24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
