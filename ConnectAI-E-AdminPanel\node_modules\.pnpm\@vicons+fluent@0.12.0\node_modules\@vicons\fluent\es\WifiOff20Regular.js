import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2.854 2.146a.5.5 0 1 0-.708.708l3.031 3.03a9.628 9.628 0 0 0-1.937 1.5c-.393.393-.775.86-1.121 1.36a.5.5 0 0 0 .821.57A9.02 9.02 0 0 1 3.947 8.09a8.614 8.614 0 0 1 1.964-1.473l1.436 1.436a6.437 6.437 0 0 0-1.871 1.303a6.408 6.408 0 0 0-1.193 1.645a.5.5 0 1 0 .892.452a5.41 5.41 0 0 1 1.008-1.39A5.43 5.43 0 0 1 8.11 8.818l1.855 1.856a4.02 4.02 0 0 0-2.781 1.18a4.05 4.05 0 0 0-.84 1.244a.5.5 0 0 0 .916.4c.155-.355.368-.674.63-.937a3.034 3.034 0 0 1 3.304-.66l1.646 1.647a.497.497 0 0 0 .2.2l4.105 4.106a.5.5 0 0 0 .708-.708l-15-15zm6.75 5.336L10.62 8.5a5.427 5.427 0 0 1 3.27 1.565c.396.396.745.885 1.01 1.404a.5.5 0 0 0 .891-.455a6.635 6.635 0 0 0-1.194-1.656a6.435 6.435 0 0 0-4.994-1.875zM7.135 5.014l.81.81a8.607 8.607 0 0 1 9.197 3.493a.5.5 0 0 0 .823-.568a9.61 9.61 0 0 0-10.83-3.734zm3.828 8.773a1.298 1.298 0 1 1-1.836 1.836a1.298 1.298 0 0 1 1.836-1.836z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WifiOff20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
