import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M24.003 7c6.337 0 9.932 4.195 10.455 9.26h.16c4.078 0 7.384 3.298 7.384 7.365c0 4.068-3.306 7.365-7.385 7.365h-2.704a1.5 1.5 0 1 1-2.822 0H18.555l-4.306 7.17a1.75 1.75 0 0 1-3.077-1.66l.076-.141l3.224-5.369h-1.085c-4.078 0-7.384-3.297-7.384-7.365c0-4.067 3.306-7.365 7.384-7.365h.16C14.074 11.161 17.666 7 24.003 7zm-.416 27.27a1.75 1.75 0 0 1 .648 2.27l-.078.14l-.918 1.489a1.75 1.75 0 0 1-3.057-1.698l.078-.14l.918-1.49a1.75 1.75 0 0 1 2.409-.57zm4.915.73a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3zm5-1a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherRainSnow48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
