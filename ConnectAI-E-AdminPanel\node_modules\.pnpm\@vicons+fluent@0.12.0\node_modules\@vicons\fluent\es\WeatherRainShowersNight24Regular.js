import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M13.001 6.018c3.169 0 4.966 2.097 5.228 4.63h.08A3.687 3.687 0 0 1 22 14.33a3.687 3.687 0 0 1-3.692 3.683h-.637l-1.536 2.612a.75.75 0 0 1-1.344-.658l.045-.092l1.103-1.862h-2.136l-1.536 2.612a.75.75 0 0 1-1.344-.658l.045-.092l1.103-1.862H9.935L8.4 20.625a.75.75 0 0 1-1.344-.658l.045-.092l1.102-1.862h-.51a3.687 3.687 0 0 1-3.691-3.683a3.687 3.687 0 0 1 3.692-3.682h.08c.263-2.55 2.059-4.63 5.227-4.63zm0 1.497c-2.071 0-3.877 1.634-3.877 3.89c0 .357-.318.637-.684.637h-.69c-1.261 0-2.283 1.002-2.283 2.237s1.022 2.236 2.283 2.236h10.503c1.26 0 2.283-1.001 2.283-2.236c0-1.235-1.022-2.237-2.283-2.237h-.69c-.366 0-.685-.28-.685-.637c0-2.285-1.806-3.89-3.877-3.89zM6.588 2.01a5.058 5.058 0 0 1 2.264.674a5.057 5.057 0 0 1 2.208 2.595c-.5.14-.963.338-1.386.584A3.554 3.554 0 0 0 7.484 3.7c.16 1.352-.048 2.513-.623 3.61l-.118.214c-.57.976-1.432 1.708-2.718 2.304a3.614 3.614 0 0 0 1.07.614c-.451.301-.846.681-1.166 1.119l-.155-.085a5.062 5.062 0 0 1-1.642-1.514a.75.75 0 0 1 .366-1.132C4.14 8.243 5.025 7.58 5.53 6.615c.553-1.055.655-2.174.288-3.677a.75.75 0 0 1 .77-.928z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherRainShowersNight24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
