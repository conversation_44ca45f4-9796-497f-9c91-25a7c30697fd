import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.75 14.5a1.75 1.75 0 1 1 0 3.5a1.75 1.75 0 0 1 0-3.5zm0 1.5a.25.25 0 1 0 0 .5a.25.25 0 0 0 0-.5zm8.748-8a2.5 2.5 0 1 1 0 5l-.052-.002l-.114.009l-.742-.001c.112.271.174.568.174.88a2.35 2.35 0 0 1-2.375 2.378c-1.003 0-1.74-.444-2.13-1.176a.75.75 0 0 1 1.271-.79l.1.163c.136.194.36.303.76.303a.85.85 0 0 0 .874-.878c0-.44-.362-.815-.844-.866l-.215-.013H2.75a.75.75 0 0 1-.102-1.493l.102-.007h12.582l.048.002l.059-.007l.176-.009a1 1 0 1 0-1.009-1.449l-.09.207a.75.75 0 0 1-1.368-.607A2.502 2.502 0 0 1 15.498 8zm-6-5.005a3.5 3.5 0 0 1 .435 6.974l-.1.02l-.103.006H2.75a.75.75 0 0 1-.102-1.493l.102-.007h6.749a2 2 0 1 0-2-2a.75.75 0 0 1-1.5 0a3.5 3.5 0 0 1 3.5-3.5zM3.75 3a1.75 1.75 0 1 1 0 3.5a1.75 1.75 0 0 1 0-3.5zm12.5-1a1.75 1.75 0 1 1 0 3.5a1.75 1.75 0 0 1 0-3.5zM3.75 4.5a.25.25 0 1 0 0 .5a.25.25 0 0 0 0-.5zm12.5-1a.25.25 0 1 0 0 .5a.25.25 0 0 0 0-.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherDuststorm20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
