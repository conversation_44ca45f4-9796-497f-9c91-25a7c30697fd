import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M27.356 25.395a1.75 1.75 0 0 1 .342 2.335l-.095.128l-2.164 2.645h4.806c1.436 0 2.242 1.622 1.43 2.76l-.086.111l-7.501 8.997a1.75 1.75 0 0 1-2.785-2.115l.097-.127l5.106-6.126h-4.758c-1.428 0-2.236-1.606-1.44-2.745l.086-.112l4.5-5.504a1.75 1.75 0 0 1 2.462-.247zM26 10.018c6.338 0 9.932 4.194 10.455 9.26h.16c4.078 0 7.384 3.298 7.384 7.365c0 4.068-3.306 7.365-7.384 7.365h-3.063c1.284-2.436-.414-5.505-3.306-5.505h-.694l.067-.131c.74-1.576.348-3.426-.996-4.525a3.75 3.75 0 0 0-5.277.53l-4.5 5.503l-.131.171l-.102.153a3.768 3.768 0 0 0-.168 3.805h-3.06c-4.077 0-7.383-3.298-7.383-7.366c0-3.986 3.175-7.233 7.14-7.361l.404-.004c.525-5.099 4.117-9.26 10.455-9.26zM19.997 4a9.431 9.431 0 0 1 7.787 4.104A15.04 15.04 0 0 0 25.996 8c-6.078 0-10.476 3.44-11.96 8.62l-.08.29l-.115.476l-.413.076a9.379 9.379 0 0 0-6.908 6.07a6.564 6.564 0 0 1 4.04-11.737h.142A9.439 9.439 0 0 1 19.996 4z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherThunderstorm48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
