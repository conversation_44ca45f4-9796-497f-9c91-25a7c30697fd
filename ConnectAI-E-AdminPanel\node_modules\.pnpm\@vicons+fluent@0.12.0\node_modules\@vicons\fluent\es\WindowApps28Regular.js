import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3 6.75A3.75 3.75 0 0 1 6.75 3h14.5A3.75 3.75 0 0 1 25 6.75v4.26a3.284 3.284 0 0 0-.25-.01H23.5V9.5h-19v11.75a2.25 2.25 0 0 0 2.25 2.25H11v1.25c0 .084.003.168.01.25H6.75A3.75 3.75 0 0 1 3 21.25V6.75zm20.5 0a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75V8h19V6.75zm-5 11.75v-4.25A2.25 2.25 0 0 1 20.75 12h4A2.25 2.25 0 0 1 27 14.25v9.5A3.25 3.25 0 0 1 23.75 27h-9.5A2.25 2.25 0 0 1 12 24.75v-4a2.25 2.25 0 0 1 2.25-2.25h4.25zm1.5-4.25v4.25h5.5v-4.25a.75.75 0 0 0-.75-.75h-4a.75.75 0 0 0-.75.75zM25.5 20H20v5.5h3.75a1.75 1.75 0 0 0 1.75-1.75V20zm-11.25 0a.75.75 0 0 0-.75.75v4c0 .414.336.75.75.75h4.25V20h-4.25z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowApps28Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
