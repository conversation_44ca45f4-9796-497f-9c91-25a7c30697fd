import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M21 18a1 1 0 1 0 0 2h3a1 1 0 1 0 0-2h-3zM3 6a3 3 0 0 1 3-3h16.75A3.25 3.25 0 0 1 26 6.25v1.006c1.748.618 3 2.285 3 4.244v13a4.5 4.5 0 0 1-4.5 4.5h-17A4.5 4.5 0 0 1 3 24.5V6.25h.01A3.04 3.04 0 0 1 3 6zm21.5 3H5v15.5A2.5 2.5 0 0 0 7.5 27h17a2.5 2.5 0 0 0 2.5-2.5v-13A2.5 2.5 0 0 0 24.5 9zM24 6.25C24 5.56 23.44 5 22.75 5H6a1 1 0 0 0 0 2h18v-.75z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wallet32Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
