import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.5 15.024h7a.5.5 0 0 1 .09.992l-.09.008h-7a.5.5 0 0 1-.09-.992l.09-.008zm-1.996-2h10.992c.278 0 .504.224.504.5c0 .246-.178.45-.413.492l-.09.008H4.503a.502.502 0 0 1-.504-.5c0-.245.178-.45.413-.491l.09-.009zm5.496-10c2.465 0 3.863 1.574 4.066 3.474h.062C15.714 6.498 17 7.735 17 9.261c0 1.526-1.286 2.763-2.872 2.763H5.872C4.286 12.024 3 10.787 3 9.261c0-1.469 1.192-2.67 2.697-2.758l.237-.005C6.139 4.586 7.535 3.024 10 3.024zm0 1c-1.65 0-3.087 1.27-3.087 3.025c0 .278-.254.497-.545.497h-.55C4.814 7.546 4 8.324 4 9.285c0 .96.814 1.74 1.818 1.74h8.364c1.004 0 1.818-.78 1.818-1.74s-.814-1.74-1.818-1.74h-.55c-.29 0-.545-.218-.545-.496c0-1.777-1.438-3.025-3.087-3.025z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherFog20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
