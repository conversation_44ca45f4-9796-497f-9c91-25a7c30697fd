import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.384 4.616a1.25 1.25 0 1 0-1.768 1.768l16.22 16.22c-.054.11-.11.219-.167.328c-2.108 4.022-5.739 6.835-12.52 9.261a1.35 1.35 0 0 0-.656 2.037a17.933 17.933 0 0 0 5.821 5.367c7.198 4.156 16.073 2.776 21.695-2.82l6.607 6.607a1.25 1.25 0 0 0 1.768-1.768l-37-37zM33.24 35.008c-4.841 4.815-12.48 6.002-18.677 2.424A15.471 15.471 0 0 1 10.457 34c6.139-2.408 9.91-5.342 12.23-9.544l10.554 10.553zm-3.177-24.422c7.13 4.116 9.752 13.04 6.136 20.311l1.85 1.85c4.763-8.569 1.787-19.406-6.736-24.326a17.929 17.929 0 0 0-8.022-2.389A1.35 1.35 0 0 0 21.91 7.7c.897 3.676 1.084 6.778.653 9.559l2.122 2.122c.798-3.235.8-6.72.005-10.694l.469.08c1.714.32 3.369.93 4.906 1.819z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherMoonOff48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
