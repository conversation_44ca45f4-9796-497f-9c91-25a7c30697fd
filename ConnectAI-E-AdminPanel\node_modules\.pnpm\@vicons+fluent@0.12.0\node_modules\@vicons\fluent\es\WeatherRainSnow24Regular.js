import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M14.498 17.5a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zM12 4.001c3.168 0 4.966 2.097 5.227 4.63h.08A3.687 3.687 0 0 1 21 12.314a3.687 3.687 0 0 1-3.692 3.682L16 15.997a.75.75 0 0 1-1.433 0l-5.58-.001l-1.578 2.627a.75.75 0 0 1-1.344-.658l.045-.092l1.145-1.877h-.562A3.687 3.687 0 0 1 3 12.314A3.687 3.687 0 0 1 6.693 8.63h.08c.262-2.55 2.058-4.63 5.227-4.63zm-.422 12.704a.75.75 0 0 1 .32.933l-.046.091l-.566.9a.75.75 0 0 1-1.345-.66l.045-.09l.567-.9a.75.75 0 0 1 1.025-.274zM17.25 17a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zM12 5.5c-2.071 0-3.877 1.633-3.877 3.889c0 .357-.319.638-.684.638h-.69c-1.261 0-2.284 1.001-2.284 2.236c0 1.235 1.023 2.237 2.284 2.237H17.25c1.261 0 2.284-1.002 2.284-2.237s-1.023-2.236-2.284-2.236h-.69c-.365 0-.684-.28-.684-.638c0-2.285-1.806-3.89-3.877-3.89z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherRainSnow24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
