import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10 3c2.465 0 3.863 1.574 4.066 3.474h.062C15.714 6.474 17 7.711 17 9.237C17 10.763 15.714 12 14.128 12l-.703-.001V12h-.03l-.957 1.741a.5.5 0 0 1-.876-.482l.693-1.26h-1.818V12h-.041l-.958 1.741a.5.5 0 0 1-.876-.482l.693-1.26H7.432V12h-.036l-.958 1.741a.5.5 0 0 1-.876-.482l.693-1.26l-.383.001C4.286 12 3 10.763 3 9.237c0-1.47 1.192-2.671 2.697-2.758l.237-.005C6.139 4.561 7.535 3 10 3zm-2.89 8h7.071C15.187 11 16 10.221 16 9.26c0-.96-.814-1.739-1.818-1.739h-.55c-.29 0-.545-.218-.545-.496C13.087 5.248 11.65 4 10 4C8.35 4 6.913 5.27 6.913 7.025c0 .278-.254.496-.545.496h-.55C4.814 7.521 4 8.3 4 9.261C4 10.22 4.814 11 5.818 11h1.273a.61.61 0 0 1 .02 0zm.638 3.316a.5.5 0 0 1 .186.682l-1 1.75a.5.5 0 0 1-.868-.496l1-1.75a.5.5 0 0 1 .682-.186zm3.186.682a.5.5 0 0 0-.868-.496l-1 1.75a.5.5 0 0 0 .868.496l1-1.75z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherDrizzle20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
