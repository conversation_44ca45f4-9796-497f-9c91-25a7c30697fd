import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11.612 12.13a.5.5 0 0 1 .037.706l-1.027 1.167H12.5a.5.5 0 0 1 .39.811l-2.45 3a.5.5 0 0 1-.783-.623l1.805-2.188H9.5a.5.5 0 0 1-.372-.834l1.777-2.002a.5.5 0 0 1 .707-.037zM11 6c2.465 0 3.863 1.574 4.066 3.474h.062c1.586 0 2.872 1.237 2.872 2.763C18 13.763 16.714 15 15.128 15h-1.21c.301-.846-.231-1.813-1.148-1.974l-.127-.017a1.5 1.5 0 0 0-2.564-1.394L8.385 13.5a1.476 1.476 0 0 0-.3 1.5H6.872C5.286 15 4 13.763 4 12.237c0-1.526 1.286-2.763 2.872-2.763h.062C7.139 7.561 8.535 6 11 6zM8.392 3c1.456 0 2.726.828 3.353 2.045A6.055 6.055 0 0 0 11 5C8.61 5 6.868 6.307 6.246 8.286l-.062.214l-.046.187l-.165.03a3.734 3.734 0 0 0-2.716 2.258a2.622 2.622 0 0 1 1.2-4.856l.222-.005A3.77 3.77 0 0 1 8.392 3z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherThunderstorm20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
