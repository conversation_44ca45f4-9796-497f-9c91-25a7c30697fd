import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M15.989 4l-3.067 3.063a3.5 3.5 0 0 0-.778 1.18l-.08.224l-.74 2.295a2.25 2.25 0 0 0 2.633 2.885l.156-.04l2.328-.7a3.502 3.502 0 0 0 1.285-.706l.181-.169L22 7.946v8.809a3.25 3.25 0 0 1-3.25 3.25H5.25A3.25 3.25 0 0 1 2 16.755v-4.2l.076-.031l.073-.041l3.76-2.363l.098-.052a.75.75 0 0 1 1.01.933l-.046.104l-1.208 2.286l-.07.145a2.25 2.25 0 0 0 2.993 2.953l.17-.086l1.765-.996l.085-.057a.75.75 0 0 0-.735-1.292l-.092.045l-1.759.993l-.098.046a.75.75 0 0 1-.974-.957l.041-.093l1.208-2.286l.08-.166a2.25 2.25 0 0 0-3.121-2.874l-.146.084L2 10.805V7.25a3.25 3.25 0 0 1 3.066-3.245L5.25 4H15.99zm5.187-.455l.13.12l.12.13a2.269 2.269 0 0 1-.121 3.08l-4.281 4.273a2.25 2.25 0 0 1-.943.562l-2.327.7a1 1 0 0 1-1.24-1.265l.74-2.295c.109-.34.298-.65.551-.903l4.29-4.283a2.27 2.27 0 0 1 3.08-.12z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Whiteboard24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
