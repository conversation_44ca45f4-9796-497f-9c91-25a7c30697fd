import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M26.001 12c6.337 0 9.932 4.195 10.455 9.26h.16c4.078 0 7.384 3.298 7.384 7.365c0 4.068-3.306 7.365-7.384 7.365h-1.723l-.04.095l-.065.128l-3.5 6.063a1.5 1.5 0 0 1-2.665-1.372l.066-.128l2.763-4.786h-4.295l-.04.095l-.066.128l-3.5 6.063a1.5 1.5 0 0 1-2.664-1.372l.066-.128l2.762-4.786H19.42l-.039.095l-.066.128l-3.5 6.063a1.5 1.5 0 0 1-2.665-1.372l.067-.128l2.762-4.786h-.593c-4.078 0-7.384-3.297-7.384-7.365c0-4.067 3.306-7.365 7.384-7.365h.16c.526-5.099 4.118-9.26 10.455-9.26zm0 2.495c-4.261 0-7.975 3.448-7.975 8.21c0 .755-.656 1.348-1.407 1.348h-1.421c-2.594 0-4.697 2.113-4.697 4.72c0 2.608 2.103 4.722 4.697 4.722h21.606c2.594 0 4.697-2.114 4.697-4.721c0-2.608-2.103-4.722-4.697-4.722h-1.42c-.752 0-1.408-.592-1.408-1.346c0-4.824-3.714-8.21-7.975-8.21zM8.708 20.303a1.244 1.244 0 0 1-.558 1.57l-.115.055l-2.311.962a1.244 1.244 0 0 1-1.067-2.243l.115-.055l2.312-.962a1.244 1.244 0 0 1 1.624.673zm13-9.64l-.06.019c-.925.302-1.785.702-2.572 1.19a5.5 5.5 0 0 0-6.826 7.95a9.276 9.276 0 0 0-2.125 1.086a7.883 7.883 0 0 1 11.584-10.246zm-16.128.179l.127.046l2.319.989c.63.266.932.995.675 1.628c-.24.592-.883.89-1.48.711l-.126-.045l-2.32-.99a1.26 1.26 0 0 1-.674-1.628c.24-.591.883-.89 1.48-.711zm7.368-6.174l.055.116l.96 2.306a1.25 1.25 0 0 1-2.254 1.072l-.055-.115l-.96-2.306a1.25 1.25 0 0 1 2.254-1.073zm9.049-.56c.602.25.908.91.73 1.521l-.046.13l-.992 2.276a1.263 1.263 0 0 1-2.38-.837l.046-.13l.991-2.276a1.264 1.264 0 0 1 1.65-.684z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherRainShowersDay48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
