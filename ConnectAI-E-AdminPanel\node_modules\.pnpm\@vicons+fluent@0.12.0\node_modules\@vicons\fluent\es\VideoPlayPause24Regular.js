import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.651 6.617l7.502 4.753c.214.136.33.353.346.577V7.25a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75v9.5a.75.75 0 0 1-.75.75h-3a.75.75 0 0 1-.75-.75v-4.69a.742.742 0 0 1-.347.577l-7.5 4.747A.75.75 0 0 1 2.5 16.75v-9.5a.75.75 0 0 1 1.151-.633zM21.248 6.5a.75.75 0 0 1 .75.75v9.499a.75.75 0 0 1-.75.75h-3a.75.75 0 0 1-.75-.75v-9.5a.75.75 0 0 1 .75-.75h3zM14.498 8h-1.5v8h1.5V8zm6 0h-1.5V16h1.5V8zM4 8.613v6.775l5.35-3.385L4 8.613z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoPlayPause24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
