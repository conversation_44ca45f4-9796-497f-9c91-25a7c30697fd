import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10 2a.75.75 0 0 1 .743.648l.007.102v2.373l1.507-1.324a.75.75 0 0 1 .99 1.127L10.75 7.119v2.129h2.131l2.195-2.496a.75.75 0 0 1 .97-.135l.089.067a.75.75 0 0 1 .135.97l-.067.089l-1.325 1.505h2.373a.75.75 0 0 1 .743.649l.007.102a.75.75 0 0 1-.648.743l-.102.007h-2.375l1.325 1.508a.75.75 0 0 1 .01.979l-.078.08a.75.75 0 0 1-.98.009l-.079-.078l-2.195-2.498l-2.13-.001l.001 2.132l2.498 2.194a.75.75 0 0 1 .136.97l-.067.089a.75.75 0 0 1-.97.135l-.089-.067l-1.509-1.325v2.374a.75.75 0 0 1-.649.743l-.102.006a.75.75 0 0 1-.743-.648l-.007-.102v-2.372l-1.504 1.324a.75.75 0 0 1-.99-1.127l2.495-2.195v-2.13l-1.953.001l-2.369 2.698a.75.75 0 1 1-1.127-.99l1.5-1.708H2.75a.75.75 0 0 1-.743-.649L2 10a.75.75 0 0 1 .648-.743l.102-.007l2.194-.001L3.8 7.943a.75.75 0 1 1 1.127-.989L6.94 9.249h2.308V7.118L6.753 4.924a.75.75 0 0 1-.135-.97l.067-.088a.75.75 0 0 1 .97-.136l.088.067L9.25 5.122V2.75a.75.75 0 0 1 .552-.723l.097-.02L10 2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSnowflake20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
