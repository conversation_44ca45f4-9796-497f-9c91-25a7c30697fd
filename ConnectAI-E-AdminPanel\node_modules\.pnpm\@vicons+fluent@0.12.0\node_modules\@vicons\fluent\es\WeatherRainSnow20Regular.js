import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10 4c2.465 0 3.863 1.574 4.066 3.474h.062c1.586 0 2.872 1.237 2.872 2.763C17 11.763 15.714 13 14.128 13h-1.502a.5.5 0 1 1-.948 0l-4.237-.001l-1.496 2.746a.5.5 0 0 1-.914-.395l.036-.083l1.235-2.268l-.43.001C4.286 13 3 11.763 3 10.237c0-1.526 1.286-2.763 2.872-2.763h.062C6.139 5.561 7.535 4 10 4zm1.5 11a.5.5 0 1 1 0 1a.5.5 0 0 1 0-1zm-2.198-.94a.49.49 0 0 1 .235.587l-.036.082l-.562 1.014a.504.504 0 0 1-.678.197a.49.49 0 0 1-.236-.587l.036-.082l.562-1.014a.504.504 0 0 1 .679-.197zM13.5 14a.5.5 0 1 1 0 1a.5.5 0 0 1 0-1zM10 5C8.35 5 6.913 6.27 6.913 8.025c0 .278-.254.496-.545.496h-.55C4.814 8.521 4 9.3 4 10.261C4 11.22 4.814 12 5.818 12h8.364C15.186 12 16 11.221 16 10.26c0-.96-.814-1.739-1.818-1.739h-.55c-.29 0-.545-.218-.545-.496C13.087 6.248 11.65 5 10 5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherRainSnow20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
