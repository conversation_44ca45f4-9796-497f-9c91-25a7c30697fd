import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10.96 16.44a1.501 1.501 0 1 1 2.122 2.122a1.501 1.501 0 0 1-2.123-2.122zm-2.604-2.742a5.232 5.232 0 0 1 7.4 0c.46.461.838 1.025 1.101 1.625a1 1 0 1 1-1.832.803a3.356 3.356 0 0 0-.683-1.013a3.233 3.233 0 0 0-4.572 0a3.255 3.255 0 0 0-.672 1a1 1 0 1 1-1.832-.802a5.25 5.25 0 0 1 1.09-1.613zM6.31 10.707a8.128 8.128 0 0 1 11.495 0a8.35 8.35 0 0 1 1.504 2.085a1 1 0 1 1-1.781.91a6.36 6.36 0 0 0-1.137-1.581a6.128 6.128 0 0 0-9.8 1.562a1 1 0 1 1-1.784-.902a8.08 8.08 0 0 1 1.503-2.074z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wifi224Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
