import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.936 3.005L17.75 3H6.25l-.184.005A3.25 3.25 0 0 0 3 6.25v11.5l.005.184A3.25 3.25 0 0 0 6.25 21h8.285a5.812 5.812 0 0 1-.963-1.5H6.25l-.144-.006A1.75 1.75 0 0 1 4.5 17.75L4.501 8H19.5v3.886c.49.395.988.627 1.501.725V6.25l-.005-.184a3.25 3.25 0 0 0-3.06-3.06zM6.25 4.5h11.5c.966 0 1.75.784 1.75 1.75v.25h-15v-.25c0-.966.783-1.75 1.75-1.75zm14.75 9.124a4.134 4.134 0 0 1-1.5-.526a5.55 5.55 0 0 1-1.217-.976a.389.389 0 0 0-.566 0c-.994 1.036-2.094 1.545-3.317 1.545c-.221 0-.4.186-.4.416v2.501l.004.266c.034 1.013.263 1.898.684 2.65c.324.58.761 1.08 1.311 1.5c.524.4 1.149.727 1.874.979c.082.028.171.028.253 0c2.56-.89 3.874-2.713 3.874-5.395v-2.5l-.009-.085a.405.405 0 0 0-.391-.332c-.204 0-.403-.014-.6-.043z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowShield24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
