import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M19.502 36a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3zm9 0a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3zm-13.5-2a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3zm9 0a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3zm9 0a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3zm-9-26c6.338 0 9.933 4.195 10.456 9.26h.16c4.078 0 7.384 3.298 7.384 7.365c0 4.068-3.306 7.365-7.385 7.365H13.389c-4.078 0-7.384-3.297-7.384-7.365c0-4.067 3.306-7.365 7.384-7.365h.16C14.074 12.161 17.666 8 24.003 8z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSnow48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
