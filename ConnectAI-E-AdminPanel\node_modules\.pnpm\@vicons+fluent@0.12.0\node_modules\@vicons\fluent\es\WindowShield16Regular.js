import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.5 1.999a2.5 2.5 0 0 0-2.5 2.5v7.002a2.5 2.5 0 0 0 2.5 2.5h3.738a5.26 5.26 0 0 1-.63-1H4.5a1.5 1.5 0 0 1-1.5-1.5L3.001 5h10l.001.91c.331.255.663.42 1 .508V4.5a2.5 2.5 0 0 0-2.5-2.5H4.5zM3.086 4A1.5 1.5 0 0 1 4.5 3h7a1.5 1.5 0 0 1 1.414 1h-9.83zm10.916 3.442a3.523 3.523 0 0 1-1-.348a4.716 4.716 0 0 1-1.253-.984a.334.334 0 0 0-.495 0c-.87.932-1.832 1.39-2.903 1.39c-.193 0-.35.168-.35.375v2.251l.004.239c.033 1.027.283 1.908.746 2.636c.237.374.53.707.878 1c.482.406 1.07.733 1.761.98c.072.026.15.026.222 0c2.24-.8 3.39-2.441 3.39-4.855v-2.25l-.008-.076a.357.357 0 0 0-.343-.3c-.22 0-.437-.02-.649-.058z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowShield16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
