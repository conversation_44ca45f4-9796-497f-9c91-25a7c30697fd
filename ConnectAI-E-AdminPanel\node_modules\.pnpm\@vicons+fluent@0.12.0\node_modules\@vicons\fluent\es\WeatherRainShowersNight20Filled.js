import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11.015 6.018c2.465 0 3.863 1.573 4.066 3.474h.062c1.586 0 2.872 1.237 2.872 2.763c0 1.525-1.286 2.763-2.872 2.763l-.716-.001l-.01.025l-1.487 2.704a.5.5 0 0 1-.915-.396l.036-.083l1.24-2.25h-1.853l-.01.025l-1.488 2.704a.5.5 0 0 1-.913-.396l.035-.083l1.24-2.25h-1.87l-1.487 2.729a.5.5 0 0 1-.596.235l-.082-.036a.5.5 0 0 1-.236-.595l.036-.083l1.225-2.25h-.405c-1.586 0-2.872-1.236-2.872-2.762c0-1.47 1.192-2.671 2.697-2.758l.237-.005c.205-1.913 1.602-3.474 4.066-3.474zm-5.157-4a4.236 4.236 0 0 1 1.895.565a4.238 4.238 0 0 1 1.979 2.572c-1.724.38-2.963 1.54-3.468 3.142l-.065.22l-.046.188l-.165.03a3.754 3.754 0 0 0-2.2 1.362a4.238 4.238 0 0 1-1.662-1.418a.629.629 0 0 1 .306-.949c1.376-.492 2.117-1.046 2.54-1.855c.464-.884.549-1.82.242-3.08c-.1-.41.223-.8.644-.776z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherRainShowersNight20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
