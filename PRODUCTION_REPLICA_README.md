# ConnectAI 生产环境复刻部署指南

本项目基于生产环境的 `docker ps` 输出，完整复刻了 ConnectAI 系统的所有服务和配置。

## 📋 系统架构

### 🏗️ 服务组件

#### 基础设施服务
- **MySQL 数据库** (4个实例)
  - manager-server_mysql_1 (端口: 53306)
  - field-base-proxy_mysql_1 (端口: 49979)
  - field-base-server_mysql_1 (端口: 49982)
  - proxyall_mysql_1 (端口: 50294)

- **Redis 缓存** (3个实例)
  - manager-server_redis_1 (端口: 49642)
  - field-base-server_redis_1 (端口: 49978)
  - datachat-api-es-1_redis_1 (内部)

- **Elasticsearch 搜索引擎**
  - datachat-api-es-1_elasticsearch_1 (端口: 50094/50093)

- **RabbitMQ 消息队列**
  - manager-server_rabbitmq_1 (端口: 49491/49490)

- **Nginx 代理** (4个实例)
  - manager-server_proxy_1 (端口: 8081)
  - proxyall_proxy_1 (端口: 10001)
  - field-base-server_proxy_1 (端口: 10011)
  - field-base-proxy_proxy_1 (端口: 10012)

#### ConnectAI 核心服务
- **知识服务器**: know-server (端口: 8086)
- **管理服务**: connectai-manager (多个实例，端口: 50344-50352)
- **消费者服务**: 
  - 飞书消费者 (多个实例)
  - 钉钉消费者 (多个实例)
  - 企业微信消费者
  - Messenger消费者
  - 应用消费者 (多个实例)

#### AI 代理服务 (20+个)
- OpenAI、Claude、GPT、星火、文心一言、智谱AI
- 通义千问、阿里云、腾讯混元、百川、商汤
- Gemini、翻译、ElevenLabs、Stability AI 等

#### 其他服务
- **证书管理**: certd (端口: 7001-7002)
- **GeoIP API**: geoip-api (端口: 20086)
- **Lark 部署服务器**: lark-deploy-server (端口: 7000)

## 🚀 快速开始

### 1. 环境要求

#### 系统要求
- **操作系统**: Linux/macOS/Windows
- **内存**: 至少 8GB (推荐 16GB+)
- **磁盘**: 至少 50GB 可用空间
- **CPU**: 4核心以上

#### 软件要求
- Docker 20.10+
- Docker Compose 1.29+

### 2. 配置环境变量

复制并编辑环境变量文件：
```bash
cp .env.production .env.local
```

**重要配置项**：
```bash
# AI API 密钥 (必须配置)
OPENAI_API_KEY=your_openai_api_key_here
CLAUDE_API_KEY=your_claude_api_key_here
XINGHUO_API_KEY=your_xinghuo_api_key
WENXIN_API_KEY=your_wenxin_api_key
ZHIPUAI_API_KEY=your_zhipuai_api_key

# 企业应用配置
FEISHU_APP_ID=your_feishu_app_id
FEISHU_APP_SECRET=your_feishu_app_secret
DINGDING_APP_KEY=your_dingding_app_key
DINGDING_APP_SECRET=your_dingding_app_secret

# 数据库密码
MYSQL_ROOT_PASSWORD=connectai2023
```

### 3. 部署服务

#### Linux/macOS 用户
```bash
# 给脚本执行权限
chmod +x deploy-production-replica.sh

# 启动所有服务
./deploy-production-replica.sh start

# 查看服务状态
./deploy-production-replica.sh status

# 查看日志
./deploy-production-replica.sh logs
```

#### Windows 用户
```cmd
# 启动所有服务
deploy-production-replica.bat start

# 查看服务状态
deploy-production-replica.bat status

# 查看日志
deploy-production-replica.bat logs
```

## 🔧 服务管理

### 基本操作

```bash
# 启动服务
./deploy-production-replica.sh start

# 停止服务
./deploy-production-replica.sh stop

# 重启服务
./deploy-production-replica.sh restart

# 查看状态
./deploy-production-replica.sh status

# 查看所有日志
./deploy-production-replica.sh logs

# 查看特定服务日志
./deploy-production-replica.sh logs mysql-manager
```

### 数据备份与恢复

```bash
# 备份数据
./deploy-production-replica.sh backup

# 恢复数据
./deploy-production-replica.sh restore /path/to/backup
```

## 🌐 服务访问

### Web 服务
- **管理后台**: http://localhost:50344
- **知识服务**: http://localhost:8086
- **代理管理**: http://localhost:10001

### 管理服务
- **Nginx 代理**: http://localhost:8081
- **RabbitMQ 管理**: http://localhost:49490 (用户名/密码: rabbitmq/rabbitmq)
- **Elasticsearch**: http://localhost:50094

### AI 代理服务
- **OpenAI 代理**: http://localhost:50314
- **Claude 代理**: http://localhost:50320
- **星火代理**: http://localhost:50295
- **文心代理**: http://localhost:50302
- **智谱代理**: http://localhost:50323

### 数据库服务
- **MySQL (Manager)**: localhost:53306
- **MySQL (Proxy)**: localhost:49979
- **MySQL (Server)**: localhost:49982
- **MySQL (ProxyAll)**: localhost:50294
- **Redis (Manager)**: localhost:49642
- **Redis (Server)**: localhost:49978

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查 Docker 状态
docker info

# 检查端口占用
netstat -tulpn | grep :8081

# 查看服务日志
./deploy-production-replica.sh logs [service_name]
```

#### 2. 内存不足
```bash
# 调整 Elasticsearch 内存设置
# 编辑 .env.production 文件
ES_JAVA_OPTS=-Xms256m -Xmx256m
```

#### 3. 磁盘空间不足
```bash
# 清理 Docker 资源
docker system prune -f
docker volume prune -f

# 清理旧的备份
find ./backups -name "connectai-backup-*" -mtime +30 -delete
```

#### 4. 数据库连接问题
```bash
# 检查 MySQL 状态
docker-compose exec mysql-manager mysqladmin ping

# 重置数据库密码
docker-compose exec mysql-manager mysql -u root -p
```

### 日志分析

```bash
# 查看错误日志
./deploy-production-replica.sh logs | grep -i error

# 查看特定时间段日志
docker-compose logs --since="2024-01-01T00:00:00" --until="2024-01-01T23:59:59"

# 实时监控日志
./deploy-production-replica.sh logs -f
```

## 📊 监控与维护

### 系统监控

```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
df -h

# 查看内存使用
free -h

# 查看网络连接
netstat -tulpn
```

### 定期维护

```bash
# 每日备份 (添加到 crontab)
0 2 * * * /path/to/deploy-production-replica.sh backup

# 每周清理 Docker 资源
0 3 * * 0 docker system prune -f

# 每月清理旧备份
0 4 1 * * find /path/to/backups -name "connectai-backup-*" -mtime +30 -delete
```

## 🔐 安全配置

### 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 8081
sudo ufw allow 8086
sudo ufw allow 10001

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8081/tcp
sudo firewall-cmd --permanent --add-port=8086/tcp
sudo firewall-cmd --reload
```

### SSL 证书配置
```bash
# 使用 Let's Encrypt
# 编辑 docker-compose.yml 添加证书卷挂载
volumes:
  - /etc/letsencrypt:/etc/letsencrypt:ro
```

## 📈 性能优化

### 数据库优化
```sql
-- MySQL 配置优化
SET GLOBAL innodb_buffer_pool_size = 1073741824;  -- 1GB
SET GLOBAL max_connections = 200;
```

### Redis 优化
```bash
# Redis 配置
maxmemory 512mb
maxmemory-policy allkeys-lru
```

### Elasticsearch 优化
```yaml
# ES 配置
environment:
  - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
  - "discovery.type=single-node"
  - "indices.memory.index_buffer_size=10%"
```

## 🆘 技术支持

### 获取帮助
1. 查看日志: `./deploy-production-replica.sh logs`
2. 检查服务状态: `./deploy-production-replica.sh status`
3. 查看系统资源: `docker stats`
4. 检查网络连接: `docker network ls`

### 联系方式
- 项目文档: 查看项目 README
- 问题反馈: 提交 GitHub Issue
- 技术交流: 加入技术群组

## 📄 许可证

本项目遵循原项目的许可证协议。
