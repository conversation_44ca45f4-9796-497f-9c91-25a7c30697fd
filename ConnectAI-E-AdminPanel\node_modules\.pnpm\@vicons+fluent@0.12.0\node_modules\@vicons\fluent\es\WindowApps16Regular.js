import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.5 2A2.5 2.5 0 0 0 2 4.5v7A2.5 2.5 0 0 0 4.5 14H6v-1H4.5A1.5 1.5 0 0 1 3 11.5V6h11V4.5A2.5 2.5 0 0 0 11.5 2h-7zM13 5H3v-.5A1.5 1.5 0 0 1 4.5 3h7A1.5 1.5 0 0 1 13 4.5V5zm-.25 2h1.5c.966 0 1.75.784 1.75 1.75V14a2 2 0 0 1-2 2H8.75A1.75 1.75 0 0 1 7 14.25v-1.5c0-.966.784-1.75 1.75-1.75H11V8.75c0-.966.784-1.75 1.75-1.75zM11 12H8.75a.75.75 0 0 0-.75.75v1.5c0 .414.336.75.75.75H11v-3zm1 3h2a1 1 0 0 0 1-1v-2h-3v3zm0-4h3V8.75a.75.75 0 0 0-.75-.75h-1.5a.75.75 0 0 0-.75.75V11z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowApps16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
