import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M22.5 3.75a.75.75 0 0 0-1.5 0V5h-1.25a.75.75 0 0 0 0 1.5H21v1.25a.75.75 0 0 0 1.5 0V6.5h1.25a.75.75 0 0 0 0-1.5H22.5V3.75zM7.75 5.5a.75.75 0 0 1 .75.75V7.5h1.25a.75.75 0 0 1 0 1.5H8.5v1.25a.75.75 0 0 1-1.5 0V9H5.75a.75.75 0 0 1 0-1.5H7V6.25a.75.75 0 0 1 .75-.75zm12 11.5a.75.75 0 0 1 .75.75V19h1.25a.75.75 0 0 1 0 1.5H20.5v1.25a.75.75 0 0 1-1.5 0V20.5h-1.25a.75.75 0 0 1 0-1.5H19v-1.25a.75.75 0 0 1 .75-.75zm-.116-7.522a2.875 2.875 0 0 0-4.54-.636l-.848.844l4.067 4.066l.84-.84a2.875 2.875 0 0 0 .481-3.434zm-2.383 5.334l-4.067-4.067L2.807 21.088a2.88 2.88 0 1 0 4.068 4.079l10.376-10.355z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wand28Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
