import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.039 16.464a2.155 2.155 0 0 1-.044-.05l.01.002l-.078-.089a2.6 2.6 0 0 0-.121-.133a3.78 3.78 0 0 0-1.485-.918l-1.837-.6a.728.728 0 0 1-.351-1.103a.731.731 0 0 1 .351-.264l1.837-.6a3.79 3.79 0 0 0 1.462-.92c.087-.1.169-.204.245-.311c.127-.16.244-.33.35-.505c.114-.196.211-.4.29-.613l.612-1.881a.729.729 0 0 1 .684-.484c.15 0 .297.046.42.133c.036.03.07.064.1.1c.07.071.125.157.16.25l.601 1.835a3.793 3.793 0 0 0 2.397 2.396l1.838.6l.037.008A.728.728 0 0 1 12 14a.72.72 0 0 1-.484.684l-1.838.6a3.782 3.782 0 0 0-1.48.919a3.79 3.79 0 0 0-.917 1.48l-.6 1.836l-.017.045a.719.719 0 0 1-.167.206a.286.286 0 0 0-.032.043c-.014.021-.028.042-.05.057a.73.73 0 0 1-.998-.156a.713.713 0 0 1-.105-.2l-.6-1.835a3.792 3.792 0 0 0-.6-1.13a1.239 1.239 0 0 0-.074-.085zm24.201-.708a5.998 5.998 0 0 1-4.243 10.241a6.002 6.002 0 0 1-6-5.999a5.998 5.998 0 0 1 6-5.999c1.592 0 3.118.632 4.244 1.757zM40.25 8H8.59l.548 1.688a1.8 1.8 0 0 0 .43.7a.709.709 0 0 0 .083.066c.022.015.043.03.062.048H40.25a1.25 1.25 0 0 1 1.25 1.25v24.496a1.25 1.25 0 0 1-1.25 1.25H34v-6.251a3.25 3.25 0 0 0-3.25-3.25H17.245a3.25 3.25 0 0 0-3.25 3.25v6.248h-6.25a1.25 1.25 0 0 1-1.251-1.25V21.949a2.684 2.684 0 0 1-.5.053a2.71 2.71 0 0 1-1.574-.5a2.65 2.65 0 0 1-.426-.414v15.159a3.749 3.749 0 0 0 3.75 3.75H40.25A3.75 3.75 0 0 0 44 36.245V11.749A3.748 3.748 0 0 0 40.25 8zm-29.9 13.748a2.214 2.214 0 0 0 1.38-1.397l.349-1.07a.422.422 0 0 1 .399-.279a.42.42 0 0 1 .244.078a.418.418 0 0 1 .155.2l.348 1.071a2.2 2.2 0 0 0 1.4 1.397l1.07.348l.022.006a.422.422 0 0 1 .278.399a.422.422 0 0 1-.278.398l-1.072.349a2.208 2.208 0 0 0-1.401 1.397l-.35 1.07a.422.422 0 0 1-.398.278a.42.42 0 0 1-.244-.078a.417.417 0 0 1-.155-.2l-.348-1.07a2.199 2.199 0 0 0-1.4-1.403l-1.07-.348a.425.425 0 0 1-.284-.4a.419.419 0 0 1 .283-.398l1.072-.348z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoPersonSparkle48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
