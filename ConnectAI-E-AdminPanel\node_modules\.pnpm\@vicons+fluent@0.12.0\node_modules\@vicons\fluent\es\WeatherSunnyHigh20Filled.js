import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M9.5 3.5v-1a.5.5 0 0 1 1 0v1a.5.5 0 0 1-1 0zM7 8a3 3 0 1 1 6 0a3 3 0 0 1-6 0zm2.5 4.5v1a.5.5 0 0 0 1 0v-1a.5.5 0 0 0-1 0zM4.5 8a.5.5 0 0 0 0 1h1a.5.5 0 0 0 0-1h-1zm11 1h-1a.5.5 0 0 1 0-1h1a.5.5 0 0 1 0 1zM6.146 5.854a.5.5 0 1 0 .708-.708l-1-1a.5.5 0 1 0-.708.708l1 1zm.708 5.292a.5.5 0 0 0-.708 0l-1 1a.5.5 0 1 0 .708.708l1-1a.5.5 0 0 0 0-.707zm7-5.292a.5.5 0 0 1-.708-.708l1-1a.5.5 0 0 1 .708.708l-1 1zm-.708 5.292a.5.5 0 0 1 .708 0l1 1a.5.5 0 0 1-.708.708l-1-1a.5.5 0 0 1 0-.707zm-7.012 5.311A15.754 15.754 0 0 1 10 16c1.669 0 3.045.229 4.13.527c1.04.285 1.815.634 2.324.908a6.899 6.899 0 0 1 .7.43l.03.022l.004.004a.5.5 0 0 0 .624-.782l-.002-.002l-.005-.003l-.013-.01l-.047-.035a7.914 7.914 0 0 0-.818-.505a12.17 12.17 0 0 0-2.532-.992C13.227 15.242 11.761 15 10 15c-1.621 0-2.992.205-4.111.488a12.457 12.457 0 0 0-2.744 1.027a8.123 8.123 0 0 0-.702.413a4.838 4.838 0 0 0-.233.164l-.015.012l-.004.003l-.002.002h-.001a.5.5 0 0 0 .624.782l.006-.005l.032-.024a7.115 7.115 0 0 1 .76-.462a11.448 11.448 0 0 1 2.524-.943z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSunnyHigh20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
