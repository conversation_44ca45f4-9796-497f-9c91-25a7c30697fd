import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10.5 4A2.5 2.5 0 0 1 13 6.5v7a2.5 2.5 0 0 1-2 2.45V12.5A3.5 3.5 0 0 0 7.5 9h-4c-.537 0-1.045.12-1.5.337V6.5A2.5 2.5 0 0 1 4.5 4h6zm3.5 8.082V7.93l2.77-2.313a.75.75 0 0 1 1.23.575v7.668a.75.75 0 0 1-1.236.57L14 12.082zM1 12.5A2.5 2.5 0 0 1 3.5 10h4a2.5 2.5 0 0 1 2.5 2.5v4A2.5 2.5 0 0 1 7.5 19h-4A2.5 2.5 0 0 1 1 16.5v-4zm4.02.034a.452.452 0 0 0-.447-.037a.49.49 0 0 0-.156.108a.51.51 0 0 0-.145.357v3.075a.502.502 0 0 0 .145.358a.563.563 0 0 0 .158.11a.45.45 0 0 0 .323.02a.52.52 0 0 0 .13-.064l2.296-1.567a.47.47 0 0 0 .163-.185a.536.536 0 0 0-.003-.487a.487.487 0 0 0-.168-.182L5.02 12.534z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoRecording20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
