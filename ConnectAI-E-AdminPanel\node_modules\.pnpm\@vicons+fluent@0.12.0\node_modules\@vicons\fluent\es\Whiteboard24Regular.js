import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M15.989 4l-1.503 1.5H5.25l-.144.006A1.75 1.75 0 0 0 3.5 7.25v2.24l2.61-1.64a2.25 2.25 0 0 1 3.008.569l.095.139a2.25 2.25 0 0 1 .163 2.082l-.079.166l-1.208 2.286a.75.75 0 0 0 .933 1.05l.098-.046l1.76-.993a.75.75 0 0 1 .826 1.247l-.085.057l-1.765.996a2.25 2.25 0 0 1-3.163-2.867l.07-.145l1.208-2.286a.75.75 0 0 0-.964-1.037l-.098.052L3.5 11.262v5.493c0 .966.784 1.75 1.75 1.75h13.5a1.75 1.75 0 0 0 1.75-1.75V9.443L22 7.946v8.809a3.25 3.25 0 0 1-3.25 3.25H5.25A3.25 3.25 0 0 1 2 16.755V7.25a3.25 3.25 0 0 1 3.066-3.245L5.25 4h10.739zm5.186-.455l.13.12l.121.13a2.269 2.269 0 0 1-.121 3.08l-4.281 4.273a2.25 2.25 0 0 1-.943.562l-2.327.7a1 1 0 0 1-1.24-1.265l.739-2.295c.11-.34.299-.65.552-.903l4.29-4.283a2.27 2.27 0 0 1 3.08-.12zm-2.02 1.18l-4.29 4.284a.751.751 0 0 0-.184.3l-.447 1.39l1.416-.425a.752.752 0 0 0 .314-.188l4.28-4.273l.075-.086a.769.769 0 0 0-1.163-1.002z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Whiteboard24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
