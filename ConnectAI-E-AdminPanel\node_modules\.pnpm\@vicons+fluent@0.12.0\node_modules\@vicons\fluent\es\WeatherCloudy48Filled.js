import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M26.003 14.018c6.337 0 9.932 4.194 10.455 9.26h.16c4.078 0 7.384 3.298 7.384 7.365c0 4.068-3.306 7.365-7.384 7.365h-21.23c-4.078 0-7.384-3.297-7.384-7.365c0-4.067 3.306-7.365 7.385-7.365h.16c.526-5.099 4.117-9.26 10.454-9.26zM20 8a9.431 9.431 0 0 1 7.8 4.125a14.871 14.871 0 0 0-1.8-.107c-6.078 0-10.476 3.438-11.96 8.614l-.08.29l-.115.475l-.414.077a9.377 9.377 0 0 0-6.905 6.06a6.564 6.564 0 0 1 4.038-11.739h.142A9.44 9.44 0 0 1 20 8z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherCloudy48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
