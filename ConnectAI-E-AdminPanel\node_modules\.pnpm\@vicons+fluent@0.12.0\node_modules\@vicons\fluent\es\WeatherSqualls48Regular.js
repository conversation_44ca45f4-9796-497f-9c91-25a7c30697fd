import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M38.05 19.55a5.98 5.98 0 0 1 5.981 5.977c0 3.3-2.705 5.992-6.01 5.992c-.042 0-.084-.002-.125-.007l-.125.007h-3.033c.56.857.886 1.88.886 2.98c0 3.061-2.299 5.507-5.336 5.507c-3.018 0-4.821-1.746-5.31-4.189a1.25 1.25 0 1 1 2.45-.491c.27 1.35 1.129 2.18 2.86 2.18c1.62 0 2.836-1.293 2.836-3.008c0-1.555-1.208-2.839-2.758-2.98H5.281a1.25 1.25 0 0 1-.128-2.493l.128-.006l24.626-.002c.056-.008.115-.012.174-.012c.129 0 .256.004.383.012l7.307.002l.124.006l.127-.006c1.862 0 3.403-1.472 3.504-3.301l.005-.19a3.48 3.48 0 0 0-3.481-3.478a3.48 3.48 0 0 0-3.475 3.264a1.25 1.25 0 1 1-2.496-.15a5.98 5.98 0 0 1 5.97-5.614zm-15.028 4.483H5.28a1.25 1.25 0 0 1-.128-2.494l.128-.006h17.74a5.491 5.491 0 1 0-5.491-5.492a1.25 1.25 0 0 1-2.5 0a7.992 7.992 0 1 1 7.992 7.992H5.28h17.74z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSqualls48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
