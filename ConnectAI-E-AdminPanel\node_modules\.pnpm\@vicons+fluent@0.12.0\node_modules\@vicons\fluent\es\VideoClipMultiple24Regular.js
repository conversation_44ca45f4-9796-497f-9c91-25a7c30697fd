import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M8 7.752v5.497a.75.75 0 0 0 1.155.631l4.618-2.959a.5.5 0 0 0 0-.842L9.155 7.12A.75.75 0 0 0 8 7.752zM5.25 3A3.25 3.25 0 0 0 2 6.25v9a3.25 3.25 0 0 0 3.25 3.25h10.5A3.25 3.25 0 0 0 19 15.25v-9A3.25 3.25 0 0 0 15.75 3H5.25zM3.5 6.25c0-.966.784-1.75 1.75-1.75h10.5c.966 0 1.75.784 1.75 1.75v9A1.75 1.75 0 0 1 15.75 17H5.25a1.75 1.75 0 0 1-1.75-1.75v-9zM5.01 19.5A3.247 3.247 0 0 0 7.75 21h8.5c2.9 0 5.25-2.35 5.25-5.25v-7c0-1.15-.598-2.162-1.5-2.74v9.74a3.75 3.75 0 0 1-3.75 3.75H5.01z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoClipMultiple24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
