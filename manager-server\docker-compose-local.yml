version: '2'
services:
  manager:
    restart: always
    image: connectai-manager
    volumes:
      - ./server:/server
      - ./etc/web_config.conf:/etc/web_config.conf
      - ../data:/data
    ports:
      - "3000:3000"
    depends_on:
      - mysql
      - redis
      - rabbitmq
    environment:
      - DEBUG=true
      - PYTHONPATH=/server

  mysql:
    platform: linux/x86_64
    restart: always
    image: mysql:5.7
    volumes:
      - ./data/mysql/data:/var/lib/mysql
      - ./data/mysql/conf.d:/etc/mysql/conf.d
    environment:
      MYSQL_ROOT_PASSWORD: 'connectai2023'
      MYSQL_DATABASE: 'connectai-manager'
      TZ: 'Asia/Shanghai'
    ports:
      - "3306:3306"
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  redis:
    restart: always
    image: redis:alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - ./data/redis:/data

  rabbitmq:
    restart: always
    image: rabbitmq:3.7-management-alpine
    environment:
      RABBITMQ_ERLANG_COOKIE: "SWQOKODSQALRPCLNMEQG"
      RABBITMQ_DEFAULT_USER: "rabbitmq"
      RABBITMQ_DEFAULT_PASS: "rabbitmq"
      RABBITMQ_DEFAULT_VHOST: "/"
    ports:
      - "15672:15672"  # 管理界面
      - "5672:5672"    # AMQP端口
    volumes:
      - ./data/rabbitmq:/var/lib/rabbitmq

  # 消费者进程
  appconsumer:
    restart: always
    image: connectai-manager
    volumes:
      - ./server:/server
      - ./etc/web_config.conf:/etc/web_config.conf
      - ../data:/data
    command: python3 /server/scripts/application_consumer.py
    depends_on:
      - mysql
      - redis
      - rabbitmq
    environment:
      - PYTHONPATH=/server

  feishuconsumer:
    restart: always
    image: connectai-manager
    volumes:
      - ./server:/server
      - ./etc/web_config.conf:/etc/web_config.conf
      - ../data:/data
    command: python3 /server/scripts/feishu_consumer.py
    depends_on:
      - mysql
      - redis
      - rabbitmq
    environment:
      - PYTHONPATH=/server

  dingdingconsumer:
    restart: always
    image: connectai-manager
    volumes:
      - ./server:/server
      - ./etc/web_config.conf:/etc/web_config.conf
      - ../data:/data
    command: python3 /server/scripts/dingding_consumer.py
    depends_on:
      - mysql
      - redis
      - rabbitmq
    environment:
      - PYTHONPATH=/server

  weworkconsumer:
    restart: always
    image: connectai-manager
    volumes:
      - ./server:/server
      - ./etc/web_config.conf:/etc/web_config.conf
      - ../data:/data
    command: python3 /server/scripts/wework_consumer.py
    depends_on:
      - mysql
      - redis
      - rabbitmq
    environment:
      - PYTHONPATH=/server

  messengerconsumer:
    restart: always
    image: connectai-manager
    volumes:
      - ./server:/server
      - ./etc/web_config.conf:/etc/web_config.conf
      - ../data:/data
    command: python3 /server/scripts/messenger_consumer.py
    depends_on:
      - mysql
      - redis
      - rabbitmq
    environment:
      - PYTHONPATH=/server
