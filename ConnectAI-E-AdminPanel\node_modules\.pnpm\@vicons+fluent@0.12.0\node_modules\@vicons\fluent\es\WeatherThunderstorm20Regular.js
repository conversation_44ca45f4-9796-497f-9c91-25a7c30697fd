import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M9.128 14.169l1.767-2a.5.5 0 0 1 .798.597l-.054.072l-1.017 1.165H12.5a.5.5 0 0 1 .437.744l-.046.067l-2.445 3.003a.5.5 0 0 1-.831-.547l.05-.076l1.797-2.191H9.5a.5.5 0 0 1-.423-.768l.051-.066l1.767-2l-1.767 2zM11 6c2.465 0 3.863 1.574 4.066 3.474h.062c1.586 0 2.872 1.237 2.872 2.763C18 13.763 16.714 15 15.128 15h-1.256a1.071 1.071 0 0 0 .01-1h1.3C16.186 14 17 13.221 17 12.26c0-.96-.814-1.739-1.818-1.739h-.55c-.29 0-.545-.218-.545-.496C14.087 8.248 12.65 7 11 7c-1.65 0-3.087 1.27-3.087 3.025c0 .278-.254.496-.545.496h-.55C5.814 10.521 5 11.3 5 12.261C5 13.22 5.814 14 6.818 14h1.3a1.071 1.071 0 0 0 .01 1H6.872C5.286 15 4 13.763 4 12.237c0-1.526 1.286-2.763 2.872-2.763h.062C7.139 7.561 8.535 6 11 6zM8.392 3c1.456 0 2.726.828 3.353 2.045a6.055 6.055 0 0 0-1.284-.022A2.647 2.647 0 0 0 8.375 4c-1.296 0-2.4.946-2.62 2.225l-.037.21a1 1 0 0 1-.986.83h-.258C3.66 7.265 3 7.933 3 8.757c0 .57.315 1.065.778 1.316c-.214.272-.39.576-.52.902a2.622 2.622 0 0 1 1.2-4.856l.221-.005A3.77 3.77 0 0 1 8.392 3z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherThunderstorm20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
