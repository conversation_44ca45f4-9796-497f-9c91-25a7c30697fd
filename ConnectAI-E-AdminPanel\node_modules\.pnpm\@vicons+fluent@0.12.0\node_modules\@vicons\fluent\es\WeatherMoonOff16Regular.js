import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11.616 12.323l2.53 2.53a.5.5 0 0 0 .708-.707l-13-13a.5.5 0 1 0-.708.708L6.38 7.087c-.86 1.93-2.748 2.586-3.97 2.808a.5.5 0 0 0-.36.71a6 6 0 0 0 9.566 1.718zm-.707-.707A4.983 4.983 0 0 1 7.456 13c-1.754 0-3.299-.904-4.191-2.273c1.257-.34 2.923-1.116 3.863-2.892l3.781 3.781zM12.456 8c0 .69-.14 1.346-.391 1.943l.753.753a6 6 0 0 0-5.572-8.693a.5.5 0 0 0-.475.593c.125.661.197 1.41.145 2.199l.902.902a9.2 9.2 0 0 0 .032-2.682A5 5 0 0 1 12.456 8z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherMoonOff16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
