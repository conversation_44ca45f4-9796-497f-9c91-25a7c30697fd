import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12.232 9H13.5a.5.5 0 0 1 0 1h-7a.5.5 0 0 1 0-1h.757a1 1 0 0 1-.123-1.17l2.5-4.33A1 1 0 0 1 11 3.134l2.598 1.5A1 1 0 0 1 13.964 6l-1.732 3zm.866-3.5L10.5 4L8 8.33L9.16 9h1.918l2.02-3.5zM13.96 8l.577-1a1 1 0 0 1 .763.4l2.5 3.333a1 1 0 0 1 .2.6V16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-4.667a1 1 0 0 1 .2-.6L4.7 7.4a1 1 0 0 1 .8-.4h.963l-.479.83a1.006 1.006 0 0 0-.078.17H5.5l-2.25 3h13.5L14.5 8h-.54zM3 16h14v-4H3v4z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Vote20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
