import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.28 2.22a.75.75 0 1 0-1.06 1.06L4.52 5.582A3.251 3.251 0 0 0 2 8.75v10.5a3.25 3.25 0 0 0 3.25 3.25h9.502A3.25 3.25 0 0 0 18 19.372v-.311l6.72 6.72a.75.75 0 0 0 1.06-1.061L3.28 2.22zM8.682 5.5L18 14.818v-6.19A3.25 3.25 0 0 0 14.752 5.5h-6.07zM19.5 16.318l4.665 4.666a1.244 1.244 0 0 0 1.083-1.24V8.246c0-1.115-1.35-1.672-2.136-.882L19.5 10.992v5.326z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoOff28Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
