import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M16.832 7.384c.41.409.796.877 1.133 1.365a.5.5 0 0 1-.823.568A8.612 8.612 0 0 0 3.946 8.091c-.348.348-.693.77-1.007 1.222a.5.5 0 0 1-.821-.57c.346-.5.728-.966 1.121-1.36a9.612 9.612 0 0 1 13.593 0zm-2.236 1.973a6.637 6.637 0 0 1 1.194 1.656a.5.5 0 0 1-.89.455a5.643 5.643 0 0 0-1.01-1.404a5.45 5.45 0 0 0-8.716 1.39a.5.5 0 1 1-.892-.452a6.45 6.45 0 0 1 10.315-1.645zm-1.709 2.497c.355.354.647.79.85 1.254a.5.5 0 0 1-.916.401a3.135 3.135 0 0 0-.64-.948a3.033 3.033 0 0 0-4.29 0a3.051 3.051 0 0 0-.631.938a.5.5 0 1 1-.916-.401a4.05 4.05 0 0 1 .84-1.244a4.033 4.033 0 0 1 5.703 0zm-1.924 1.933a1.298 1.298 0 1 1-1.836 1.836a1.298 1.298 0 0 1 1.836-1.836z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wifi120Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
