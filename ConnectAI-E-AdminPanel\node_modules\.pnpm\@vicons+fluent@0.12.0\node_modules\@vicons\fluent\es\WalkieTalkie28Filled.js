import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11 8.5v4h6v-4h-6zM10.25 1a.75.75 0 0 1 .75.75V3.5h8.75A2.25 2.25 0 0 1 22 5.75V16a.75.75 0 0 1-.19.498L20 18.535v4.715a2.25 2.25 0 0 1-2.25 2.25h-7.5A2.25 2.25 0 0 1 8 23.25v-4.715l-1.81-2.037A.75.75 0 0 1 6 16V5.75A2.25 2.25 0 0 1 8.25 3.5H9.5V1.75a.75.75 0 0 1 .75-.75zM9.5 7.75v5.5c0 .415.336.75.75.75h7.5a.75.75 0 0 0 .75-.75v-5.5a.75.75 0 0 0-.75-.75h-7.5a.75.75 0 0 0-.75.75zm1.5 9.5c0 .414.336.75.75.75h4.5a.75.75 0 0 0 0-1.5h-4.5a.75.75 0 0 0-.75.75z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WalkieTalkie28Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
