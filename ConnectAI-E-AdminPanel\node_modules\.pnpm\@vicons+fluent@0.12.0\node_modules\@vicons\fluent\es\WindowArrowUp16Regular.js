import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2 4.499a2.5 2.5 0 0 1 2.5-2.5h7.002a2.5 2.5 0 0 1 2.5 2.5v1.76a5.508 5.508 0 0 0-1-.659V5h-10L3 11.501a1.5 1.5 0 0 0 1.5 1.5h1.1c.183.358.404.693.658 1H4.5a2.5 2.5 0 0 1-2.5-2.5V4.5zM4.5 3a1.5 1.5 0 0 0-1.414 1h9.83A1.5 1.5 0 0 0 11.5 3h-7zm6 12a4.5 4.5 0 1 0 0-9a4.5 4.5 0 0 0 0 9zm2.354-4.854a.5.5 0 0 1-.708.708L11 9.707V12.5a.5.5 0 0 1-1 0V9.707l-1.146 1.147a.5.5 0 0 1-.708-.708l2-2A.499.499 0 0 1 10.497 8h.006a.498.498 0 0 1 .348.144l.003.003l2 2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowArrowUp16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
