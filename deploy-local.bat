@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM ConnectAI 本地部署脚本
echo 🚀 ConnectAI 本地部署脚本
echo ========================

set ACTION=%1
if "%ACTION%"=="" set ACTION=start

REM 检查Docker
echo 🔍 检查Docker环境...
where docker >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 未安装或未启动
    echo 请确保 Docker Desktop 正在运行
    pause
    exit /b 1
)

where docker-compose >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose 未安装
    pause
    exit /b 1
)

echo ✅ Docker 环境检查通过

REM 检查配置文件
if not exist ".env.local" (
    echo ❌ 配置文件 .env.local 不存在
    echo 请先运行配置向导或手动创建配置文件
    pause
    exit /b 1
)

goto :action_%ACTION%

REM 启动服务
:action_start
echo 🚀 启动 ConnectAI 本地环境...

REM 创建数据目录
if not exist "data" mkdir data
if not exist "data\mysql" mkdir data\mysql
if not exist "data\redis" mkdir data\redis
if not exist "data\elasticsearch" mkdir data\elasticsearch
if not exist "data\rabbitmq" mkdir data\rabbitmq
if not exist "data\files" mkdir data\files

echo 📥 拉取 Docker 镜像...
docker-compose -f docker-compose.local.yml --env-file .env.local pull

echo 🔄 启动服务...
docker-compose -f docker-compose.local.yml --env-file .env.local up -d

echo ⏳ 等待服务启动...
timeout /t 30 /nobreak >nul

echo 📊 检查服务状态...
docker-compose -f docker-compose.local.yml --env-file .env.local ps

call :show_access_info
goto :end

REM 停止服务
:action_stop
echo 🛑 停止 ConnectAI 本地环境...
docker-compose -f docker-compose.local.yml --env-file .env.local down
echo ✅ 服务已停止
goto :end

REM 重启服务
:action_restart
echo 🔄 重启 ConnectAI 本地环境...
call :action_stop
timeout /t 5 /nobreak >nul
call :action_start
goto :end

REM 查看状态
:action_status
echo 📊 ConnectAI 本地环境状态：
docker-compose -f docker-compose.local.yml --env-file .env.local ps
goto :end

REM 查看日志
:action_logs
set SERVICE=%2
if "%SERVICE%"=="" (
    echo 📋 显示所有服务日志...
    docker-compose -f docker-compose.local.yml --env-file .env.local logs --tail=50
) else (
    echo 📋 显示 %SERVICE% 服务日志...
    docker-compose -f docker-compose.local.yml --env-file .env.local logs --tail=50 %SERVICE%
)
goto :end

REM 清理环境
:action_clean
echo 🧹 清理 ConnectAI 本地环境...
echo ⚠️  警告：这将删除所有数据！
set /p confirm="确认继续？(y/N): "
if /i not "%confirm%"=="y" (
    echo 操作已取消
    goto :end
)

docker-compose -f docker-compose.local.yml --env-file .env.local down -v
docker system prune -f
echo ✅ 清理完成
goto :end

REM 显示访问信息
:show_access_info
echo.
echo 🌐 ConnectAI 本地环境访问信息：
echo ==========================================
echo 🖥️  主要服务：
echo    管理后台:     http://localhost:50344
echo    知识服务:     http://localhost:8086
echo    Nginx代理:    http://localhost:8081
echo    Docker管理:   http://localhost:9000
echo.
echo 🔧 管理工具：
echo    RabbitMQ:     http://localhost:49490
echo    用户名/密码:   rabbitmq/rabbitmq
echo    Elasticsearch: http://localhost:50094
echo.
echo 🤖 AI 代理服务：
echo    OpenAI代理:   http://localhost:50314
echo    Claude代理:   http://localhost:50320
echo    星火代理:     http://localhost:50295
echo    文心代理:     http://localhost:50302
echo.
echo 💾 数据库：
echo    MySQL:        localhost:53306
echo    用户名/密码:   root/connectai2023
echo    Redis:        localhost:49642
echo.
echo 📊 管理命令：
echo    查看状态:     %~nx0 status
echo    查看日志:     %~nx0 logs [service]
echo    重启服务:     %~nx0 restart
echo    停止服务:     %~nx0 stop
echo    清理环境:     %~nx0 clean
echo ==========================================
goto :eof

REM 显示帮助
:action_help
echo.
echo ConnectAI 本地部署脚本
echo.
echo 用法: %~nx0 [action]
echo.
echo 支持的操作：
echo   start    启动本地环境 (默认)
echo   stop     停止本地环境
echo   restart  重启本地环境
echo   status   显示服务状态
echo   logs     显示日志 [service]
echo   clean    清理环境 (删除所有数据)
echo   help     显示此帮助信息
echo.
echo 示例：
echo   %~nx0 start          # 启动环境
echo   %~nx0 logs mysql     # 查看MySQL日志
echo   %~nx0 status         # 查看状态
echo.
goto :end

REM 未知操作
:action_
echo ❌ 未知操作: %ACTION%
call :action_help
goto :end

:end
if "%ACTION%"=="help" pause
exit /b 0
