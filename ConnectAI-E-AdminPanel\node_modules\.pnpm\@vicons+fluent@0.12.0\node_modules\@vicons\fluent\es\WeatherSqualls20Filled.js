import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M15.498 8a2.5 2.5 0 0 1 .165 4.995l-.165.005c-.016 0-.033 0-.049-.002l-.117.009h-.742c.113.271.174.568.174.879a2.35 2.35 0 0 1-2.375 2.377c-1.003 0-1.74-.443-2.13-1.175a.75.75 0 0 1 1.271-.79l.1.163c.136.194.36.302.76.302a.85.85 0 0 0 .874-.877c0-.44-.362-.815-.844-.867l-.215-.012H2.75a.75.75 0 0 1-.102-1.493l.102-.007h12.582l.048.002l.059-.007l.176-.009a1 1 0 1 0-1.009-1.449l-.09.207a.75.75 0 0 1-1.368-.607A2.502 2.502 0 0 1 15.498 8zm-6 1.995H2.75a.75.75 0 0 1-.102-1.493l.102-.007h6.749a2 2 0 1 0-2-2a.75.75 0 0 1-1.5 0a3.5 3.5 0 1 1 3.934 3.473l-.1.02l-.103.007H2.75h6.749z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSqualls20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
