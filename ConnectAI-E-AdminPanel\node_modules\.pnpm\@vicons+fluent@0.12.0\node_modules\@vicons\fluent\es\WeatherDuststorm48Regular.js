import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.504 35a3.5 3.5 0 1 1 0 7a3.5 3.5 0 0 1 0-7zm0 2.5a1 1 0 1 0 0 2a1 1 0 0 0 0-2zm20.519-20a5.98 5.98 0 0 1 5.981 5.977c0 3.3-2.705 5.991-6.01 5.991l.033.001c-.049.011-.098.02-.15.024l-.127.007h-3.033c.565.86.894 1.889.894 2.994c0 3.062-2.298 5.507-5.335 5.507c-3.018 0-4.822-1.746-5.311-4.188a1.25 1.25 0 1 1 2.45-.492c.271 1.35 1.129 2.18 2.86 2.18c1.621 0 2.836-1.292 2.836-3.007c0-1.591-1.264-2.898-2.864-2.989l-.179-.005H5.25a1.25 1.25 0 0 1-.128-2.494L5.25 27h32.465c.09-.02.183-.032.28-.032c1.926 0 3.51-1.575 3.51-3.49A3.48 3.48 0 0 0 38.022 20a3.48 3.48 0 0 0-3.475 3.264a1.25 1.25 0 0 1-2.496-.15a5.98 5.98 0 0 1 5.97-5.614zM22.99 6a7.992 7.992 0 0 1 .226 15.98l-.107.015L23 22H5.25a1.25 1.25 0 0 1-.128-2.494l.128-.006h17.541l.1-.013l.1-.004a5.492 5.492 0 1 0-5.491-5.492a1.25 1.25 0 1 1-2.5 0A7.992 7.992 0 0 1 22.991 6zm15.513 1.014a3.5 3.5 0 1 1 0 7a3.5 3.5 0 0 1 0-7zM7.502 5a3.499 3.499 0 1 1 0 6.998a3.499 3.499 0 0 1 0-6.998zm31.002 4.514a1 1 0 1 0 0 2a1 1 0 0 0 0-2zM7.502 7.5a.999.999 0 1 0 0 1.997a.999.999 0 0 0 0-1.997z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherDuststorm48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
