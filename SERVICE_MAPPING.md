# 生产环境与复刻环境服务映射表

本文档详细说明了原生产环境容器与复刻环境服务的对应关系。

## 📊 服务映射总览

| 原生产环境容器名 | 复刻环境服务名 | 镜像 | 端口映射 | 状态 |
|---|---|---|---|---|
| manager-server_mysql_1 | mysql-manager | mysql:5.7 | 53306:3306 | ✅ 已复刻 |
| field-base-proxy_mysql_1 | mysql-proxy | mysql:5.7 | 49979:3306 | ✅ 已复刻 |
| field-base-server_mysql_1 | mysql-server | mysql:5.7 | 49982:3306 | ✅ 已复刻 |
| proxyall_mysql_1 | mysql-proxyall | mysql:5.7 | 50294:3306 | ✅ 已复刻 |
| manager-server_redis_1 | redis-manager | redis:alpine | 49642:6379 | ✅ 已复刻 |
| field-base-server_redis_1 | redis-server | redis:alpine | 49978:6379 | ✅ 已复刻 |
| datachat-api-es-1_redis_1 | redis-datachat | redis:alpine | 内部 | ✅ 已复刻 |

## 🗄️ 数据库服务映射

### MySQL 实例
```yaml
# 原生产环境 -> 复刻环境
manager-server_mysql_1 -> mysql-manager
  端口: 53306:3306
  数据库: connectai-manager
  
field-base-proxy_mysql_1 -> mysql-proxy
  端口: 49979:3306
  数据库: connectai-proxy
  
field-base-server_mysql_1 -> mysql-server
  端口: 49982:3306
  数据库: connectai-server
  
proxyall_mysql_1 -> mysql-proxyall
  端口: 50294:3306
  数据库: connectai-proxyall
```

### Redis 实例
```yaml
# 原生产环境 -> 复刻环境
manager-server_redis_1 -> redis-manager
  端口: 49642:6379
  用途: 管理服务缓存
  
field-base-server_redis_1 -> redis-server
  端口: 49978:6379
  用途: 服务器缓存
  
datachat-api-es-1_redis_1 -> redis-datachat
  端口: 内部
  用途: 数据聊天缓存
```

## 🔧 基础设施服务映射

### Elasticsearch
```yaml
# 原生产环境 -> 复刻环境
datachat-api-es-1_elasticsearch_1 -> elasticsearch
  镜像: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
  端口: 50094:9200, 50093:9300
  用途: 搜索引擎服务
```

### RabbitMQ
```yaml
# 原生产环境 -> 复刻环境
manager-server_rabbitmq_1 -> rabbitmq
  镜像: rabbitmq:3.7-management-alpine
  端口: 49491:5672, 49490:15672
  用途: 消息队列服务
```

### Nginx 代理
```yaml
# 原生产环境 -> 复刻环境
manager-server_proxy_1 -> proxy-manager
  镜像: jwilder/nginx-proxy:alpine
  端口: 8081:80
  用途: 管理服务代理
  
proxyall_proxy_1 -> proxy-proxyall
  端口: 10001:80
  用途: 代理服务总入口
  
field-base-server_proxy_1 -> proxy-field-server
  端口: 10011:80
  用途: 服务器代理
  
field-base-proxy_proxy_1 -> proxy-field-proxy
  端口: 10012:80
  用途: 代理服务代理
```

## 🤖 ConnectAI 核心服务映射

### 知识服务器
```yaml
# 原生产环境 -> 复刻环境
datachat-api-es-1_know-server_1 -> know-server
  镜像: know-server:es
  端口: 8086:80
  用途: 知识库服务
```

### 管理服务
```yaml
# 原生产环境 -> 复刻环境 (示例，实际有8个实例)
manager-server_manager_1 -> manager-1
  镜像: connectai-manager
  端口: 50345:3000
  
manager-server_manager_2 -> manager-2
  端口: 50346:3000
  
manager-server_admin_1 -> admin
  端口: 50344:3000
  用途: 管理后台
```

### 消费者服务
```yaml
# 飞书消费者 (多个实例)
manager-server_feishuconsumer_1 -> feishu-consumer-1
manager-server_feishuconsumer_2 -> feishu-consumer-2
# ... 更多实例

# 钉钉消费者
manager-server_dingdingconsumer_1 -> dingding-consumer-1
manager-server_dingdingconsumer_2 -> dingding-consumer-2

# 企业微信消费者
manager-server_weworkconsumer_1 -> wework-consumer

# Messenger消费者
manager-server_messengerconsumer_1 -> messenger-consumer

# 应用消费者 (多个实例)
manager-server_appconsumer_1 -> app-consumer-1
manager-server_appconsumer_2 -> app-consumer-2
# ... 更多实例
```

## 🔌 AI 代理服务映射

### 主要 AI 服务
```yaml
# OpenAI 相关
proxyall_connectai-proxy-openai_1 -> proxy-openai
  端口: 50314:10086
  
proxyall_connectai-proxy-gpts_1 -> proxy-gpts
  端口: 50301:10086

# Claude 相关
proxyall_connectai-proxy-claude_1 -> proxy-claude
  端口: 50320:10086

# 国产 AI 服务
proxyall_connectai-proxy-xinghuo_1 -> proxy-xinghuo
  端口: 50295:10086
  
proxyall_connectai-proxy-wenxin_1 -> proxy-wenxin
  端口: 50302:10086
  
proxyall_connectai-proxy-zhipuai_2 -> proxy-zhipuai
  端口: 50323:10086
  
proxyall_connectai-proxy-qwen_1 -> proxy-qwen
  端口: 50306:10086

# 云服务商 AI
proxyall_connectai-proxy-aliyun_1 -> proxy-aliyun
  端口: 50304:10086
  
proxyall_connectai-proxy-hunyuan_1 -> proxy-hunyuan
  端口: 50307:10086

# 其他 AI 服务
proxyall_connectai-proxy-gemini_1 -> proxy-gemini
  端口: 50303:10086
  
proxyall_connectai-proxy-stability_1 -> proxy-stability
  端口: 50298:10086
  
proxyall_connectai-proxy-elevenlabs_1 -> proxy-elevenlabs
  端口: 50309:10086
```

### 专业服务
```yaml
# 翻译服务
proxyall_connectai-proxy-translate_1 -> proxy-translate
  端口: 50296:10086

# 短信服务
proxyall_connectai-proxy-sms_1 -> proxy-sms
  端口: 50308:10086

# 视频处理
proxyall_connectai-proxy-bibigpt_1 -> proxy-bibigpt
  端口: 50326:10086
```

## 🛠️ 其他服务映射

### 证书管理
```yaml
# 原生产环境 -> 复刻环境
certd -> certd
  镜像: registry.cn-shenzhen.aliyuncs.com/handsfree/certd:latest
  端口: 7001-7002:7001-7002
  用途: SSL证书管理
```

### 地理位置服务
```yaml
# 原生产环境 -> 复刻环境
geoip-api_geoip_1 -> geoip-api
  镜像: rehiy/geoip-api
  端口: 20086:80
  用途: IP地理位置查询
```

### 部署服务
```yaml
# 原生产环境 -> 复刻环境
lark-deploy-server-git_server_1 -> lark-deploy-server
  镜像: d.ai2e.cn:5000/lark-deploy-server:latest
  端口: 7000:7000
  用途: 飞书应用部署
```

### 快捷服务
```yaml
# 原生产环境 -> 复刻环境
field-base-server_server_1 -> shortcut-server
  镜像: shortcut-server
  端口: 50232:9999
  用途: 快捷操作服务
```

## 📋 未复刻的服务

以下服务由于镜像ID或特殊配置，在复刻环境中可能需要额外处理：

```yaml
# 使用镜像ID的服务 (需要重新构建或获取镜像)
cb9f5beb0628 -> 需要确定具体镜像
c43b197911df -> 需要确定具体镜像
3c9b3d26190c -> 需要确定具体镜像
# ... 其他镜像ID
```

## 🔄 服务依赖关系

### 启动顺序
1. **基础设施服务**: MySQL, Redis, Elasticsearch, RabbitMQ
2. **代理服务**: Nginx 代理
3. **核心服务**: ConnectAI 管理服务, 知识服务器
4. **消费者服务**: 各种消费者服务
5. **AI 代理服务**: 各种 AI 代理
6. **其他服务**: 证书管理, GeoIP 等

### 服务间通信
```yaml
# 网络依赖
manager-services -> mysql-manager, redis-manager, rabbitmq
proxy-services -> mysql-proxy
ai-proxies -> mysql-proxyall
know-server -> elasticsearch, redis-datachat
consumers -> mysql-manager, redis-manager, rabbitmq
```

## 📝 配置差异说明

### 环境变量
- 生产环境可能使用不同的API密钥和配置
- 复刻环境使用统一的环境变量文件管理

### 数据持久化
- 生产环境: 可能使用外部存储
- 复刻环境: 使用 Docker volumes

### 网络配置
- 生产环境: 可能使用自定义网络配置
- 复刻环境: 使用统一的 Docker 网络

## 🚀 部署建议

1. **分阶段部署**: 先启动基础设施，再启动应用服务
2. **资源监控**: 监控内存和CPU使用情况
3. **日志管理**: 配置日志轮转和清理
4. **备份策略**: 定期备份数据和配置
5. **安全配置**: 修改默认密码和密钥
