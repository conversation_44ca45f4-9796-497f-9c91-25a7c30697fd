#!/bin/bash

# ConnectAI 快速部署脚本
# 使用方法: ./quick-deploy.sh

set -e

# 配置
REMOTE_HOST="ubuntu@************"
REMOTE_PATH="/opt/connectai"

echo "🚀 ConnectAI 快速部署脚本"
echo "目标服务器: $REMOTE_HOST"
echo ""

# 检查SSH连接
echo "📡 检查SSH连接..."
if ! ssh -o ConnectTimeout=10 -o BatchMode=yes $REMOTE_HOST exit 2>/dev/null; then
    echo "❌ 无法连接到服务器，请检查SSH配置"
    exit 1
fi
echo "✅ SSH连接正常"

# 使用现有的Makefile构建
echo "🔨 使用Makefile构建项目..."
if [ -f "Makefile" ]; then
    make package
    echo "✅ 项目构建完成"
else
    echo "❌ 未找到Makefile，请确保在项目根目录运行"
    exit 1
fi

# 上传文件
echo "📤 上传部署文件..."
ssh $REMOTE_HOST "sudo mkdir -p $REMOTE_PATH && sudo chown \$USER:\$USER $REMOTE_PATH"

# 查找生成的文件
DEPLOY_FILE=$(ls deploy-*.tar.gz | head -1)
IMAGES_FILE=$(ls images-*.tar.gz | head -1)
BASE_IMAGES_FILE="images-base.tar.gz"

if [ ! -f "$DEPLOY_FILE" ]; then
    echo "❌ 未找到部署包文件"
    exit 1
fi

echo "上传 $DEPLOY_FILE..."
scp $DEPLOY_FILE $REMOTE_HOST:$REMOTE_PATH/

if [ -f "$BASE_IMAGES_FILE" ]; then
    echo "上传 $BASE_IMAGES_FILE..."
    scp $BASE_IMAGES_FILE $REMOTE_HOST:$REMOTE_PATH/
fi

if [ -f "$IMAGES_FILE" ]; then
    echo "上传 $IMAGES_FILE..."
    scp $IMAGES_FILE $REMOTE_HOST:$REMOTE_PATH/
fi

# 创建远程部署脚本
cat > /tmp/remote-deploy.sh << 'EOF'
#!/bin/bash
set -e

DEPLOY_PATH="/opt/connectai"
cd $DEPLOY_PATH

echo "🔧 安装Docker环境..."
if ! command -v docker &> /dev/null; then
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
    echo "⚠️  Docker已安装，请重新登录后再次运行部署"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
fi

echo "📦 解压部署包..."
DEPLOY_FILE=$(ls deploy-*.tar.gz | head -1)
tar -zxf $DEPLOY_FILE

echo "🐳 加载Docker镜像..."
if [ -f "images-base.tar.gz" ]; then
    echo "加载基础镜像..."
    docker load < images-base.tar.gz
fi

IMAGES_FILE=$(ls images-*.tar.gz | grep -v base | head -1)
if [ -f "$IMAGES_FILE" ]; then
    echo "加载应用镜像..."
    docker load < $IMAGES_FILE
fi

echo "🛑 停止现有服务..."
cd build
docker-compose down 2>/dev/null || true

echo "📁 创建数据目录..."
mkdir -p data/{mysql/data,mysql/conf.d,rabbitmq,elasticsearch,redis,files,search_index}
sudo chown -R 1000:1000 data/elasticsearch 2>/dev/null || true
sudo chmod -R 755 data

echo "🚀 启动服务..."
docker-compose up -d

echo "⏳ 等待服务启动..."
sleep 30

echo "📊 检查服务状态..."
docker-compose ps

echo ""
echo "🎉 部署完成！"
echo "📱 访问地址："
echo "   管理面板: http://$(curl -s ifconfig.me):8080"
echo "   API服务:  http://$(curl -s ifconfig.me):8081"
echo "   知识库:   http://$(curl -s ifconfig.me):8082"
echo ""
echo "💡 管理命令："
echo "   查看状态: cd $DEPLOY_PATH/build && docker-compose ps"
echo "   查看日志: cd $DEPLOY_PATH/build && docker-compose logs"
echo "   重启服务: cd $DEPLOY_PATH/build && docker-compose restart"
EOF

echo "📤 上传部署脚本..."
scp /tmp/remote-deploy.sh $REMOTE_HOST:$REMOTE_PATH/
ssh $REMOTE_HOST "chmod +x $REMOTE_PATH/remote-deploy.sh"

echo "🎯 执行远程部署..."
ssh $REMOTE_HOST "$REMOTE_PATH/remote-deploy.sh"

echo ""
echo "🧹 清理本地文件..."
rm -f deploy-*.tar.gz images-*.tar.gz /tmp/remote-deploy.sh

echo ""
echo "✅ 部署完成！"
echo "🌐 请访问: http://************:8080"
