import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M29.068 35.12a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3zM24 7c6.337 0 9.932 4.195 10.455 9.26h.16c4.078 0 7.384 3.298 7.384 7.365c0 4.068-3.306 7.365-7.384 7.365h-2.661a1.5 1.5 0 1 1-2.774 0l-11.76.001l-.04.094l-.066.128l-3.5 6.063a1.5 1.5 0 0 1-2.665-1.372l.067-.129l2.762-4.785h-.593c-4.078 0-7.384-3.297-7.384-7.365c0-4.067 3.306-7.365 7.384-7.365h.16C14.072 11.161 17.664 7 24.001 7zm-.646 26.317a1.5 1.5 0 0 1 .615 1.92l-.066.129l-1.084 1.89a1.5 1.5 0 0 1-2.664-1.371l.066-.129l1.083-1.89a1.5 1.5 0 0 1 2.05-.55zm10.712.303a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3zM24 9.495c-4.261 0-7.975 3.448-7.975 8.21c0 .755-.656 1.348-1.407 1.348h-1.421c-2.594 0-4.697 2.113-4.697 4.72c0 2.608 2.103 4.722 4.697 4.722h21.606c2.594 0 4.697-2.114 4.697-4.721c0-2.608-2.103-4.722-4.697-4.722h-1.42c-.752 0-1.408-.592-1.408-1.346c0-4.824-3.714-8.21-7.975-8.21z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherRainSnow48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
