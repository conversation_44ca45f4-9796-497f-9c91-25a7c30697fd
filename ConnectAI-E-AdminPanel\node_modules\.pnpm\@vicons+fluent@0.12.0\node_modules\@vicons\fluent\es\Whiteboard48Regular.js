import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M35.288 7.444c2.15-2.15 5.73-1.795 7.417.735a4.82 4.82 0 0 1-.524 6l-8.01 8.392a3.75 3.75 0 0 1-1.87 1.065l-5.77 1.332a1.25 1.25 0 0 1-1.516-1.408l.875-5.691a3.75 3.75 0 0 1 1.055-2.082l8.343-8.343zm5.337 2.122a2.32 2.32 0 0 0-3.57-.354l-8.342 8.343a1.25 1.25 0 0 0-.352.694l-.595 3.868l3.974-.917a1.25 1.25 0 0 0 .623-.355l8.01-8.391a2.32 2.32 0 0 0 .252-2.888zm-11.928.934H10.25a3.75 3.75 0 0 0-3.75 3.75v4.932c1.01-.747 2.106-1.514 3.208-2.165c1.342-.792 2.796-1.474 4.186-1.686c1.445-.221 2.946.062 4.055 1.355c.523.61.827 1.285.922 2.005c.093.705-.024 1.377-.225 1.982c-.36 1.087-1.073 2.168-1.672 3.078l-.12.182c-.682 1.038-1.196 1.866-1.392 2.603c-.09.339-.095.602-.043.82c.049.211.17.464.465.76c.403.403.758.545 1.06.585c.32.042.699-.019 1.15-.213c.943-.404 1.908-1.258 2.772-2.122a1.25 1.25 0 1 1 1.768 1.768c-.869.869-2.128 2.04-3.553 2.651c-.732.315-1.572.511-2.462.395c-.909-.12-1.756-.55-2.503-1.296c-.581-.582-.963-1.237-1.131-1.957c-.167-.712-.106-1.403.061-2.033c.32-1.201 1.087-2.373 1.719-3.334l.035-.054c.676-1.028 1.22-1.856 1.473-2.62c.12-.362.149-.643.12-.868c-.028-.209-.114-.44-.342-.705c-.391-.456-.921-.642-1.78-.511c-.915.14-2.04.629-3.292 1.368c-1.233.728-2.492 1.638-3.667 2.526c-.273.207-.545.415-.812.62V33.75a3.75 3.75 0 0 0 3.75 3.75h27.5a3.75 3.75 0 0 0 3.75-3.75V18.514l2.5-2.619V33.75A6.25 6.25 0 0 1 37.75 40h-27.5A6.25 6.25 0 0 1 4 33.75v-19.5A6.25 6.25 0 0 1 10.25 8h20.947l-2.5 2.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Whiteboard48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
