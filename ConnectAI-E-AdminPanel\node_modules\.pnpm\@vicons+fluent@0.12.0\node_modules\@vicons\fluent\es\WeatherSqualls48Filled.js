import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M37.355 18.99c3.679 0 6.647 3.03 6.647 6.75s-2.968 6.75-6.647 6.75c-.06 0-.12-.003-.178-.009l-.175.01h-1.564c.364.762.567 1.612.567 2.509c0 3.346-2.605 6.01-6.003 6.01c-3.153 0-4.918-1.345-5.848-3.559a1.75 1.75 0 0 1 3.227-1.355c.415.987.975 1.414 2.62 1.414c1.448 0 2.504-1.08 2.504-2.51c0-1.31-1.11-2.421-2.54-2.505l-.145-.005H5.75a1.75 1.75 0 0 1-.144-3.494l.144-.006h31.252c.06 0 .12.003.178.01l.031-.004l.144-.006c1.73 0 3.147-1.447 3.147-3.25s-1.418-3.25-3.147-3.25c-1.402 0-2.552.853-2.865 2.155a1.75 1.75 0 1 1-3.403-.818c.702-2.918 3.29-4.837 6.268-4.837zM22.5 6.99a8.5 8.5 0 0 1 .255 16.995l-.255.004l-16.75.001a1.75 1.75 0 0 1-.143-3.494l.144-.006l16.75-.001a5 5 0 0 0 0-9.999c-2.748 0-4.882 1.966-4.995 4.56l-.005.217a1.75 1.75 0 1 1-3.5 0c0-4.67 3.778-8.277 8.5-8.277z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSqualls48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
