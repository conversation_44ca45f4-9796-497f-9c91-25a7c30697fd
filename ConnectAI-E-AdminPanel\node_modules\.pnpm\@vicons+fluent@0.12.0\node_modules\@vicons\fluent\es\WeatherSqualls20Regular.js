import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M15.613 8.22a2.392 2.392 0 1 1 0 4.782h-.022l-.09.006h-1.205c.22.339.347.742.347 1.175c0 1.215-.913 2.186-2.118 2.186c-1.198 0-1.914-.694-2.108-1.663a.5.5 0 0 1 .98-.197c.107.533.445.86 1.128.86c.639 0 1.118-.51 1.118-1.186c0-.65-.536-1.18-1.2-1.18l-9.938.005a.5.5 0 0 1-.09-.992l.09-.008h9.87l.068-.006c.053 0 .105.002.157.006h2.902l.021.002l.09-.008c.724 0 1.319-.551 1.386-1.257l.007-.133a1.392 1.392 0 0 0-2.783-.086a.5.5 0 0 1-.998-.06a2.392 2.392 0 0 1 2.388-2.245zm-5.848 1.794h-7.26a.5.5 0 0 1-.09-.992l.09-.008h7.26a2.231 2.231 0 1 0-2.231-2.232a.5.5 0 0 1-1 0a3.231 3.231 0 1 1 3.43 3.226l-.199.006h-7.26h7.26z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSqualls20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
