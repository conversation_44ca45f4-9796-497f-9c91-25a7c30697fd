import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M42.705 8.179c-1.687-2.53-5.267-2.885-7.417-.735l-8.343 8.343a3.75 3.75 0 0 0-1.055 2.082l-.875 5.69a1.25 1.25 0 0 0 1.516 1.409l5.77-1.332a3.75 3.75 0 0 0 1.87-1.065l8.01-8.391a4.82 4.82 0 0 0 .524-6.001zm-20.161 15a3.75 3.75 0 0 0 4.55 4.225l5.77-1.332a6.25 6.25 0 0 0 3.116-1.774L44 15.895V33.75A6.25 6.25 0 0 1 37.75 40h-27.5A6.25 6.25 0 0 1 4 33.75v-9.604c.073-.032.144-.072.212-.119c.578-.4 1.287-.944 2.045-1.526c.344-.264.699-.535 1.055-.805c1.175-.888 2.434-1.798 3.667-2.526c1.253-.74 2.377-1.229 3.292-1.368c.858-.131 1.389.055 1.78.511c.228.266.314.496.341.705c.03.225.002.506-.119.868c-.253.764-.797 1.592-1.473 2.62l-.036.054c-.63.96-1.399 2.133-1.718 3.334c-.167.63-.228 1.32-.061 2.033c.168.72.55 1.375 1.131 1.957c.836.835 1.79 1.232 2.748 1.3c.927.066 1.778-.178 2.456-.48a8.173 8.173 0 0 0 1.647-.996a8.45 8.45 0 0 0 .646-.554l.013-.013l.005-.004l.001-.002h.001l-.883-.885l.883.884a1.251 1.251 0 0 0-1.765-1.77l-.001.001l-.015.014l-.08.074a6.011 6.011 0 0 1-.333.276a5.684 5.684 0 0 1-1.134.691c-.447.198-.877.298-1.263.27c-.354-.025-.744-.16-1.158-.574c-.296-.296-.416-.549-.465-.76c-.052-.218-.047-.481.043-.82c.196-.738.71-1.565 1.392-2.603l.12-.182c.6-.91 1.312-1.991 1.672-3.078c.2-.605.318-1.277.225-1.982c-.095-.72-.4-1.395-.922-2.005c-1.109-1.293-2.61-1.576-4.056-1.355c-1.39.212-2.843.894-4.185 1.686c-1.36.803-2.71 1.784-3.903 2.685c-.402.303-.78.594-1.135.866h-.001l-.001.001c-.233.18-.456.35-.668.512V14.25A6.25 6.25 0 0 1 10.25 8h20.947l-6.02 6.02a6.25 6.25 0 0 0-1.758 3.468l-.875 5.692z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Whiteboard48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
