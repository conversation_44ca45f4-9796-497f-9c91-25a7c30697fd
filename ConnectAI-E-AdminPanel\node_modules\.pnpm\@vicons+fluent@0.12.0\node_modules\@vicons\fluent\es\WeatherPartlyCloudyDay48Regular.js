import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M25.995 15.998c6.337 0 9.932 4.194 10.454 9.26h.16c4.079 0 7.385 3.298 7.385 7.365c0 4.068-3.306 7.365-7.385 7.365H15.38c-4.077 0-7.383-3.297-7.383-7.365c0-4.067 3.306-7.365 7.384-7.365h.16c.526-5.099 4.118-9.26 10.455-9.26zm0 2.495c-4.261 0-7.975 3.448-7.975 8.21c0 .755-.657 1.348-1.408 1.348h-1.42c-2.595 0-4.697 2.113-4.697 4.72c0 2.608 2.103 4.722 4.697 4.722h21.606c2.594 0 4.697-2.114 4.697-4.721c0-2.608-2.103-4.722-4.697-4.722h-1.42c-.752 0-1.408-.592-1.408-1.346c0-4.824-3.714-8.21-7.975-8.21zm-17.29 5.799a1.244 1.244 0 0 1-.559 1.57l-.115.055l-2.311.961a1.243 1.243 0 0 1-1.067-2.243l.115-.055l2.311-.961a1.244 1.244 0 0 1 1.625.673zm13-9.642l-.062.02c-.925.302-1.784.702-2.57 1.19a5.5 5.5 0 0 0-6.826 7.95a9.278 9.278 0 0 0-2.127 1.086A7.883 7.883 0 0 1 21.704 14.65zm-16.129.18l.127.046l2.319.989c.63.266.932.995.675 1.629c-.24.591-.883.89-1.48.71l-.126-.045l-2.32-.989a1.26 1.26 0 0 1-.674-1.629c.24-.591.882-.89 1.479-.71zm7.368-6.173l.055.115l.96 2.306a1.25 1.25 0 0 1-2.254 1.072l-.055-.115l-.96-2.306a1.25 1.25 0 0 1 2.254-1.072zm9.049-.56c.601.249.908.91.73 1.52l-.046.13l-.992 2.276a1.264 1.264 0 0 1-2.38-.837l.046-.13l.99-2.276a1.264 1.264 0 0 1 1.652-.683z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherPartlyCloudyDay48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
