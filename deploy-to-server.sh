#!/bin/bash

# ConnectAI项目远程部署脚本
# 使用方法: ./deploy-to-server.sh

set -e

# 配置变量
REMOTE_HOST="ubuntu@************"
REMOTE_PATH="/opt/connectai"
PROJECT_NAME="connectai-project-manager"
VERSION="2.0.1"
CHANNEL="privatization"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查SSH连接
check_ssh_connection() {
    echo_info "检查SSH连接到 $REMOTE_HOST..."
    if ssh -o ConnectTimeout=10 -o BatchMode=yes $REMOTE_HOST exit 2>/dev/null; then
        echo_info "SSH连接成功"
    else
        echo_error "无法连接到远程服务器 $REMOTE_HOST"
        echo "请确保："
        echo "1. 服务器地址正确"
        echo "2. SSH密钥已配置或可以密码登录"
        echo "3. 服务器防火墙允许SSH连接"
        exit 1
    fi
}

# 检查本地环境
check_local_environment() {
    echo_info "检查本地环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查pnpm
    if ! command -v pnpm &> /dev/null; then
        echo_warn "pnpm未安装，尝试使用npm..."
        if ! command -v npm &> /dev/null; then
            echo_error "npm和pnpm都未安装，请先安装Node.js和npm/pnpm"
            exit 1
        fi
    fi
    
    echo_info "本地环境检查完成"
}

# 构建项目
build_project() {
    echo_info "开始构建项目..."
    
    # 构建前端项目
    echo_info "构建管理面板..."
    cd ConnectAI-E-AdminPanel
    if command -v pnpm &> /dev/null; then
        pnpm install
        pnpm run build
    else
        npm install
        npm run build
    fi
    cd ..
    
    echo_info "构建消息Web界面..."
    cd Lark-Messenger-Web
    if command -v pnpm &> /dev/null; then
        pnpm install
        pnpm run build
    else
        npm install
        npm run build
    fi
    cd ..
    
    # 构建Docker镜像
    echo_info "构建Docker镜像..."
    
    echo_info "构建知识服务器镜像..."
    docker build -t know-server:${VERSION}-${CHANNEL} -f ./DataChat-API/docker/Dockerfile ./DataChat-API
    
    echo_info "构建管理服务器镜像..."
    docker build -t connectai-manager:${VERSION}-${CHANNEL} -f ./manager-server/docker/Dockerfile ./manager-server
    
    echo_info "项目构建完成"
}

# 准备部署文件
prepare_deployment() {
    echo_info "准备部署文件..."
    
    # 清理并创建构建目录
    rm -rf build
    cp -r ./deploy build
    
    # 复制前端构建文件
    mkdir -p ./build/dist
    cp -r ./ConnectAI-E-AdminPanel/dist/* ./build/dist/
    cp -r ./Lark-Messenger-Web/dist/* ./build/dist/
    mkdir -p ./build/dist/upload
    
    # 更新docker-compose.yml中的镜像版本
    sed -i.bak "s/know-server:es/know-server:${VERSION}-${CHANNEL}/g" ./build/docker-compose.yml
    sed -i.bak "s/connectai-manager:1.0/connectai-manager:${VERSION}-${CHANNEL}/g" ./build/docker-compose.yml
    
    # 复制Elasticsearch插件
    cp ./DataChat-API/elasticsearch-analysis-ik-8.9.0.zip ./build/ 2>/dev/null || echo_warn "Elasticsearch插件文件不存在，跳过"
    
    # 创建部署包
    tar -zcf deploy-${VERSION}-${CHANNEL}.tar.gz build/
    
    echo_info "部署文件准备完成"
}

# 导出Docker镜像
export_docker_images() {
    echo_info "导出Docker镜像..."
    
    # 导出基础镜像
    echo_info "导出基础镜像..."
    docker save \
        docker.elastic.co/elasticsearch/elasticsearch:8.9.0 \
        lloydzhou/nchan \
        jwilder/nginx-proxy:alpine \
        mysql:5.7 \
        rabbitmq:3.7-management-alpine \
        redis:alpine | gzip > images-base.tar.gz
    
    # 导出应用镜像
    echo_info "导出应用镜像..."
    docker save \
        know-server:${VERSION}-${CHANNEL} \
        connectai-manager:${VERSION}-${CHANNEL} | gzip > images-${VERSION}-${CHANNEL}.tar.gz
    
    echo_info "Docker镜像导出完成"
}

# 上传文件到服务器
upload_files() {
    echo_info "上传文件到服务器..."
    
    # 创建远程目录
    ssh $REMOTE_HOST "sudo mkdir -p $REMOTE_PATH && sudo chown \$USER:\$USER $REMOTE_PATH"
    
    # 上传部署包
    echo_info "上传部署包..."
    scp deploy-${VERSION}-${CHANNEL}.tar.gz $REMOTE_HOST:$REMOTE_PATH/
    
    # 上传Docker镜像
    echo_info "上传基础镜像..."
    scp images-base.tar.gz $REMOTE_HOST:$REMOTE_PATH/
    
    echo_info "上传应用镜像..."
    scp images-${VERSION}-${CHANNEL}.tar.gz $REMOTE_HOST:$REMOTE_PATH/
    
    # 上传部署脚本
    echo_info "上传远程部署脚本..."
    cat > remote-deploy.sh << 'EOF'
#!/bin/bash

set -e

VERSION="2.0.1"
CHANNEL="privatization"
DEPLOY_PATH="/opt/connectai"

echo "开始远程部署..."

cd $DEPLOY_PATH

# 解压部署包
echo "解压部署包..."
tar -zxf deploy-${VERSION}-${CHANNEL}.tar.gz

# 加载Docker镜像
echo "加载基础Docker镜像..."
docker load < images-base.tar.gz

echo "加载应用Docker镜像..."
docker load < images-${VERSION}-${CHANNEL}.tar.gz

# 停止现有服务
echo "停止现有服务..."
cd build
docker-compose down || true

# 创建数据目录
echo "创建数据目录..."
mkdir -p data/{mysql/data,mysql/conf.d,rabbitmq,elasticsearch,redis,files,search_index}

# 设置权限
sudo chown -R 1000:1000 data/elasticsearch
sudo chmod -R 755 data

# 启动服务
echo "启动服务..."
docker-compose up -d

echo "等待服务启动..."
sleep 30

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

echo "部署完成！"
echo "访问地址："
echo "  管理面板: http://$(hostname -I | awk '{print $1}'):8080"
echo "  API文档: http://$(hostname -I | awk '{print $1}'):8081"
echo "  知识库: http://$(hostname -I | awk '{print $1}'):8082"

EOF
    
    scp remote-deploy.sh $REMOTE_HOST:$REMOTE_PATH/
    ssh $REMOTE_HOST "chmod +x $REMOTE_PATH/remote-deploy.sh"
    
    echo_info "文件上传完成"
}

# 在远程服务器执行部署
deploy_on_remote() {
    echo_info "在远程服务器执行部署..."
    
    # 检查远程服务器Docker环境
    ssh $REMOTE_HOST << 'EOF'
if ! command -v docker &> /dev/null; then
    echo "在远程服务器安装Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
    echo "Docker安装完成，请重新登录后再次运行部署脚本"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "在远程服务器安装Docker Compose..."
    sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
fi
EOF
    
    # 执行远程部署脚本
    ssh $REMOTE_HOST "$REMOTE_PATH/remote-deploy.sh"
    
    echo_info "远程部署完成"
}

# 清理本地文件
cleanup() {
    echo_info "清理本地临时文件..."
    rm -f deploy-${VERSION}-${CHANNEL}.tar.gz
    rm -f images-base.tar.gz
    rm -f images-${VERSION}-${CHANNEL}.tar.gz
    rm -f remote-deploy.sh
    rm -rf build
    echo_info "清理完成"
}

# 主函数
main() {
    echo_info "开始ConnectAI项目部署到远程服务器"
    echo_info "目标服务器: $REMOTE_HOST"
    echo_info "部署路径: $REMOTE_PATH"
    echo_info "版本: $VERSION-$CHANNEL"
    echo ""
    
    check_ssh_connection
    check_local_environment
    build_project
    prepare_deployment
    export_docker_images
    upload_files
    deploy_on_remote
    cleanup
    
    echo_info "部署完成！"
    echo_info "请访问以下地址查看服务："
    echo_info "  管理面板: http://************:8080"
    echo_info "  API服务: http://************:8081"
    echo_info "  知识库: http://************:8082"
}

# 执行主函数
main "$@"
