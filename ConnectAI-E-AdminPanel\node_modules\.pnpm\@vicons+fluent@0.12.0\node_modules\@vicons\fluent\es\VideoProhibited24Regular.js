import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M13.75 5A3.25 3.25 0 0 1 17 8.25v.173l3.864-2.318A.75.75 0 0 1 22 6.748v6.062a6.518 6.518 0 0 0-1.5-1.078V8.073L17 10.174v.845c-.52.04-1.022.14-1.5.294V8.25a1.75 1.75 0 0 0-1.75-1.75h-8.5A1.75 1.75 0 0 0 3.5 8.25v8.5c0 .966.784 1.75 1.75 1.75h5.826c.081.523.224 1.026.422 1.5H5.25A3.25 3.25 0 0 1 2 16.75v-8.5A3.25 3.25 0 0 1 5.25 5h8.5zM23 17.5a5.5 5.5 0 1 1-11 0a5.5 5.5 0 0 1 11 0zm-9.5 0c0 .834.255 1.608.691 2.248l5.557-5.557A4 4 0 0 0 13.5 17.5zm4 4a4 4 0 0 0 3.309-6.248l-5.557 5.557c.64.436 1.414.691 2.248.691z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoProhibited24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
