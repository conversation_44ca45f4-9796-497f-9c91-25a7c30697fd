import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M24 4c.69 0 1.25.56 1.25 1.25v1.5a1.25 1.25 0 1 1-2.5 0v-1.5c0-.69.56-1.25 1.25-1.25zm0 6a8 8 0 1 0 0 16a8 8 0 0 0 0-16zm-5.5 8a5.5 5.5 0 1 1 11 0a5.5 5.5 0 0 1-11 0zm6.75 11.25a1.25 1.25 0 1 0-2.5 0v1.5a1.25 1.25 0 1 0 2.5 0v-1.5zM11.25 16a1.25 1.25 0 1 0 0 2.5h1.5a1.25 1.25 0 1 0 0-2.5h-1.5zM34 17.25c0-.69.56-1.25 1.25-1.25h1.5a1.25 1.25 0 1 1 0 2.5h-1.5c-.69 0-1.25-.56-1.25-1.25zm-20.134-5.616a1.25 1.25 0 0 0 1.768-1.768l-1.5-1.5a1.25 1.25 0 0 0-1.768 1.768l1.5 1.5zm1.768 12.732a1.25 1.25 0 0 0-1.768 0l-1.5 1.5a1.25 1.25 0 0 0 1.768 1.768l1.5-1.5a1.25 1.25 0 0 0 0-1.768zm18.5-12.732a1.25 1.25 0 0 1-1.768-1.768l1.5-1.5a1.25 1.25 0 0 1 1.768 1.768l-1.5 1.5zm-1.768 12.732a1.25 1.25 0 0 1 1.768 0l1.5 1.5a1.25 1.25 0 0 1-1.768 1.768l-1.5-1.5a1.25 1.25 0 0 1 0-1.768zM6.05 43.71a1.25 1.25 0 0 1-1.76-.16c-.344-.411-.266-1.407.163-1.763l.007-.006l.022-.018l.075-.06a21.03 21.03 0 0 1 1.31-.934c.9-.593 2.214-1.374 3.899-2.154A33.903 33.903 0 0 1 24 35.5a33.903 33.903 0 0 1 14.234 3.115c1.685.78 3 1.561 3.899 2.153a21.007 21.007 0 0 1 1.31.935l.075.06l.028.024l.004.002s.84.88.16 1.762c-.442.53-1.23.6-1.76.159l-.01-.007l-.05-.04a18.508 18.508 0 0 0-1.132-.806a27.694 27.694 0 0 0-3.573-1.972A31.402 31.402 0 0 0 24 38a31.402 31.402 0 0 0-13.185 2.885a27.69 27.69 0 0 0-3.574 1.972a18.495 18.495 0 0 0-1.131.806l-.05.04l-.01.007z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSunnyHigh48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
