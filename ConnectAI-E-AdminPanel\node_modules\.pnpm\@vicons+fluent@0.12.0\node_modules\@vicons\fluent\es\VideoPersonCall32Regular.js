import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2 9.5A3.5 3.5 0 0 1 5.5 6h21A3.5 3.5 0 0 1 30 9.5v4.683a4.993 4.993 0 0 0-.562-.245l-.02-.008l-.235-.078l-.737-.222a3.53 3.53 0 0 0-.446-.104V9.5A1.5 1.5 0 0 0 26.5 8h-21A1.5 1.5 0 0 0 4 9.5v13A1.5 1.5 0 0 0 5.5 24H10v-2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v.879a3.003 3.003 0 0 0-2 .76V22h-8v2h7.659l-.075.094L18.208 26H5.5A3.5 3.5 0 0 1 2 22.5v-13zM16 18a4 4 0 1 0 0-8a4 4 0 0 0 0 8zm0-2a2 2 0 1 1 0-4a2 2 0 0 1 0 4zm9.686.012a2 2 0 0 1 2.327-.946l.717.216l.192.064a3.5 3.5 0 0 1 2.262 2.79c.297 2.068-.367 4.486-1.968 7.259c-1.597 2.766-3.355 4.548-5.29 5.328a3.5 3.5 0 0 1-3.715-.705l-.542-.514l-.122-.126a2 2 0 0 1-.125-2.497l1.357-1.88l.091-.114a1.5 1.5 0 0 1 1.563-.442l2.051.627l.053.01h.043c.245-.027.743-.522 1.355-1.582c.68-1.178.82-1.867.633-2.045l-1.043-.972l-.133-.133a2.497 2.497 0 0 1-.442-2.718l.662-1.471l.074-.149z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoPersonCall32Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
