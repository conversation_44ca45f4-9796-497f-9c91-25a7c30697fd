import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M25.25 5.25a1.25 1.25 0 1 0-2.5 0v1.5a1.25 1.25 0 1 0 2.5 0v-1.5zM24 10a8 8 0 1 0 0 16a8 8 0 0 0 0-16zM4.29 43.55a1.25 1.25 0 0 0 1.76.16l.01-.007l.05-.04c.046-.038.12-.095.22-.17c.2-.15.506-.37.911-.636a27.69 27.69 0 0 1 3.574-1.972A31.402 31.402 0 0 1 24 38c5.56 0 10.069 1.443 13.185 2.885a27.694 27.694 0 0 1 3.573 1.972a18.508 18.508 0 0 1 1.132.806l.05.04l.01.007a1.25 1.25 0 0 0 1.76-.16c.68-.881-.16-1.76-.16-1.76l-.004-.003l-.028-.024l-.04-.031l-.036-.03a21.007 21.007 0 0 0-1.31-.934a30.194 30.194 0 0 0-3.898-2.153A33.903 33.903 0 0 0 24 35.5a33.903 33.903 0 0 0-14.234 3.115c-1.685.78-3 1.561-3.899 2.153a21.03 21.03 0 0 0-1.31.935a7.779 7.779 0 0 0-.075.06l-.022.018l-.007.006c-.429.356-.507 1.352-.163 1.764zM24 28c.69 0 1.25.56 1.25 1.25v1.5a1.25 1.25 0 1 1-2.5 0v-1.5c0-.69.56-1.25 1.25-1.25zM10 17.25c0-.69.56-1.25 1.25-1.25h1.5a1.25 1.25 0 1 1 0 2.5h-1.5c-.69 0-1.25-.56-1.25-1.25zM35.25 16a1.25 1.25 0 1 0 0 2.5h1.5a1.25 1.25 0 1 0 0-2.5h-1.5zm-19.616-4.366a1.25 1.25 0 0 1-1.768 0l-1.5-1.5a1.25 1.25 0 0 1 1.768-1.768l1.5 1.5a1.25 1.25 0 0 1 0 1.768zm-1.768 12.732a1.25 1.25 0 0 1 1.768 1.768l-1.5 1.5a1.25 1.25 0 0 1-1.768-1.768l1.5-1.5zm18.5-12.732a1.25 1.25 0 0 0 1.768 0l1.5-1.5a1.25 1.25 0 0 0-1.768-1.768l-1.5 1.5a1.25 1.25 0 0 0 0 1.768zm1.768 12.732a1.25 1.25 0 0 0-1.768 1.768l1.5 1.5a1.25 1.25 0 0 0 1.768-1.768l-1.5-1.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSunnyHigh48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
