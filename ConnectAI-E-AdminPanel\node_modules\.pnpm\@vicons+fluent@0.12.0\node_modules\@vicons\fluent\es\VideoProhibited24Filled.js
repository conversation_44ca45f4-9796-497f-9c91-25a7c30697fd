import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11 17.5a6.503 6.503 0 0 1 5-6.326V8.25A3.25 3.25 0 0 0 12.75 5h-7.5A3.25 3.25 0 0 0 2 8.25v8.5A3.25 3.25 0 0 0 5.25 20h6.248A6.48 6.48 0 0 1 11 17.5zm6.5-6.5c1.747 0 3.332.689 4.5 1.81V7.04a1 1 0 0 0-1.648-.762L17 9.128v1.89a6.62 6.62 0 0 1 .5-.018zm5.5 6.5a5.5 5.5 0 1 1-11 0a5.5 5.5 0 0 1 11 0zm-9.5 0c0 .834.255 1.608.691 2.248l5.557-5.557A4 4 0 0 0 13.5 17.5zm4 4a4 4 0 0 0 3.309-6.248l-5.557 5.557c.64.436 1.414.691 2.248.691z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoProhibited24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
