import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.5 14.997a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3zm0 1a.5.5 0 1 0 0 1a.5.5 0 0 0 0-1zm9.113-7.776a2.392 2.392 0 0 1 .164 4.776l-.192.005l-.083.006h-1.206c.22.338.347.742.347 1.175c0 1.215-.913 2.186-2.118 2.186c-1.198 0-1.914-.694-2.108-1.663a.5.5 0 1 1 .98-.197c.107.532.445.86 1.128.86c.639 0 1.117-.51 1.117-1.186c0-.57-.41-1.046-.957-1.157l-.141-.019l-10.039.001a.5.5 0 0 1-.09-.992l.09-.008l9.881-.001l.057-.005c.052 0 .105.002.157.006h2.902l.025.002l.086-.008c.724 0 1.319-.551 1.386-1.257l.007-.133a1.392 1.392 0 0 0-2.783-.086a.5.5 0 1 1-.998-.06a2.392 2.392 0 0 1 2.388-2.245zM9.765 3.55a3.231 3.231 0 0 1 .016 6.463H2.505a.5.5 0 0 1-.09-.992l.09-.008h7.26a2.231 2.231 0 1 0-2.231-2.232a.5.5 0 0 1-1 0a3.231 3.231 0 0 1 3.231-3.231zM3.5 3a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3zm0 1a.5.5 0 1 0 0 1a.5.5 0 0 0 0-1zm13.002-2a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3zm0 1a.5.5 0 1 0 0 1a.5.5 0 0 0 0-1z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherDuststorm20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
