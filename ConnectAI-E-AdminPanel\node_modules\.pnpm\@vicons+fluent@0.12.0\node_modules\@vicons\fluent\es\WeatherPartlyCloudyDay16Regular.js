import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M8.999 7a2.5 2.5 0 0 0-2.5 2.5v.5H5.5a1.5 1.5 0 0 0 0 3h7a1.5 1.5 0 0 0 0-3h-1l-.001-.5a2.5 2.5 0 0 0-2.5-2.5zM5.535 9a3.5 3.5 0 0 1 6.928 0h.037a2.5 2.5 0 0 1 0 5h-7a2.5 2.5 0 0 1 0-5h.035zm3.252-6.18a.5.5 0 1 0-.883-.47l-.426.803a.5.5 0 0 0 .883.47l.426-.804zm-3.772-.747a.5.5 0 1 0-.957.293l.267.87a.5.5 0 0 0 .956-.293l-.266-.87zM6 4c.916 0 1.735.41 2.285 1.056c-.388.062-.759.173-1.107.328A2 2 0 0 0 4.386 8.18a3.489 3.489 0 0 0-.9.456A3 3 0 0 1 6 4zm-4.18.214a.5.5 0 1 0-.47.883l.803.426a.5.5 0 0 0 .47-.883l-.804-.426zm.416 4.463a.5.5 0 0 0-.293-.956l-.87.266a.5.5 0 1 0 .293.956l.87-.266z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherPartlyCloudyDay16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
