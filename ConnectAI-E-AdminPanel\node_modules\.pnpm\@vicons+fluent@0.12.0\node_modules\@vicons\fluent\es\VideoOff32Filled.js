import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M20.5 21.914l7.793 7.793a1 1 0 0 0 1.414-1.414l-26-26a1 1 0 0 0-1.414 1.414L4.536 5.95A4.5 4.5 0 0 0 2 10v12a4.5 4.5 0 0 0 4.5 4.5H16a4.5 4.5 0 0 0 4.5-4.5v-.086zm1.5-3.449l5.245 5.245c1.207.663 2.755-.2 2.755-1.643V9.934c0-1.539-1.756-2.417-2.987-1.494L22 12.2v6.265zM9.035 5.5L20.5 16.965V10A4.5 4.5 0 0 0 16 5.5H9.035z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoOff32Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
