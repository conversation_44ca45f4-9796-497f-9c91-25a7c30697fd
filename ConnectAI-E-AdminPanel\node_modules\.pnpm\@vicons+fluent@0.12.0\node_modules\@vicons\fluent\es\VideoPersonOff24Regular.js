import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2.22 2.22a.75.75 0 0 1 1.06 0l18.5 18.5a.75.75 0 0 1-1.06 1.06l-1.776-1.775H4.25A2.25 2.25 0 0 1 2 17.755V6.25a2.25 2.25 0 0 1 1.2-1.99l-.98-.98a.75.75 0 0 1 0-1.06zm13.274 14.335L14.44 15.5H8.75a.25.25 0 0 0-.243.193l-.007.057l-.001 2.754h6.995v-1.949zM11.94 13a3 3 0 0 1-2.938-2.939L4.439 5.5H4.25a.75.75 0 0 0-.75.75v11.505c0 .414.336.75.75.75l2.749-.001L7 15.75a1.75 1.75 0 0 1 1.606-1.744L8.75 14h4.19l-1.001-1zm-.229-4.472l-1.157-1.156a3 3 0 0 1 4.075 4.075l-1.156-1.157a1.503 1.503 0 0 0-1.762-1.762zm8.785 8.786V6.25a.75.75 0 0 0-.75-.75H8.682L7.182 4h12.563a2.25 2.25 0 0 1 2.25 2.25v11.505c0 .312-.063.61-.178.88l-1.322-1.321z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoPersonOff24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
