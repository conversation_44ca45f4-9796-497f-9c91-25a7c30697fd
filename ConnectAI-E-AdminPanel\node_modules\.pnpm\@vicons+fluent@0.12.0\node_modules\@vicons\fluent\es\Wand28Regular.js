import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M21.75 3a.75.75 0 0 1 .75.75V5h1.25a.75.75 0 0 1 0 1.5H22.5v1.25a.75.75 0 0 1-1.5 0V6.5h-1.25a.75.75 0 0 1 0-1.5H21V3.75a.75.75 0 0 1 .75-.75zM8.5 6.25a.75.75 0 1 0-1.5 0V7.5H5.75a.75.75 0 1 0 0 1.5H7v1.25a.75.75 0 0 0 1.5 0V9h1.25a.75.75 0 0 0 0-1.5H8.5V6.25zm12 11.5a.75.75 0 0 0-1.5 0V19h-1.25a.75.75 0 0 0 0 1.5H19v1.25a.75.75 0 0 0 1.5 0V20.5h1.25a.75.75 0 0 0 0-1.5H20.5v-1.25zm-.866-8.272a2.875 2.875 0 0 0-4.54-.636L2.806 21.088a2.88 2.88 0 1 0 4.068 4.079l12.279-12.254a2.875 2.875 0 0 0 .48-3.435zm-3.482.426a1.375 1.375 0 0 1 1.942 1.947l-.842.84l-1.945-1.945l.845-.842zm-1.907 1.902l1.945 1.945L5.815 24.105a1.38 1.38 0 1 1-1.949-1.954l10.379-10.345z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wand28Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
