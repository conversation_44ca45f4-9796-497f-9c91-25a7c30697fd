{"version": 3, "sources": ["../../.pnpm/colord@2.9.3/node_modules/colord/plugins/names.mjs"], "sourcesContent": ["export default function(e,f){var a={white:\"#ffffff\",bisque:\"#ffe4c4\",blue:\"#0000ff\",cadetblue:\"#5f9ea0\",chartreuse:\"#7fff00\",chocolate:\"#d2691e\",coral:\"#ff7f50\",antiquewhite:\"#faebd7\",aqua:\"#00ffff\",azure:\"#f0ffff\",whitesmoke:\"#f5f5f5\",papayawhip:\"#ffefd5\",plum:\"#dda0dd\",blanchedalmond:\"#ffebcd\",black:\"#000000\",gold:\"#ffd700\",goldenrod:\"#daa520\",gainsboro:\"#dcdcdc\",cornsilk:\"#fff8dc\",cornflowerblue:\"#6495ed\",burlywood:\"#deb887\",aquamarine:\"#7fffd4\",beige:\"#f5f5dc\",crimson:\"#dc143c\",cyan:\"#00ffff\",darkblue:\"#00008b\",darkcyan:\"#008b8b\",darkgoldenrod:\"#b8860b\",darkkhaki:\"#bdb76b\",darkgray:\"#a9a9a9\",darkgreen:\"#006400\",darkgrey:\"#a9a9a9\",peachpuff:\"#ffdab9\",darkmagenta:\"#8b008b\",darkred:\"#8b0000\",darkorchid:\"#9932cc\",darkorange:\"#ff8c00\",darkslateblue:\"#483d8b\",gray:\"#808080\",darkslategray:\"#2f4f4f\",darkslategrey:\"#2f4f4f\",deeppink:\"#ff1493\",deepskyblue:\"#00bfff\",wheat:\"#f5deb3\",firebrick:\"#b22222\",floralwhite:\"#fffaf0\",ghostwhite:\"#f8f8ff\",darkviolet:\"#9400d3\",magenta:\"#ff00ff\",green:\"#008000\",dodgerblue:\"#1e90ff\",grey:\"#808080\",honeydew:\"#f0fff0\",hotpink:\"#ff69b4\",blueviolet:\"#8a2be2\",forestgreen:\"#228b22\",lawngreen:\"#7cfc00\",indianred:\"#cd5c5c\",indigo:\"#4b0082\",fuchsia:\"#ff00ff\",brown:\"#a52a2a\",maroon:\"#800000\",mediumblue:\"#0000cd\",lightcoral:\"#f08080\",darkturquoise:\"#00ced1\",lightcyan:\"#e0ffff\",ivory:\"#fffff0\",lightyellow:\"#ffffe0\",lightsalmon:\"#ffa07a\",lightseagreen:\"#20b2aa\",linen:\"#faf0e6\",mediumaquamarine:\"#66cdaa\",lemonchiffon:\"#fffacd\",lime:\"#00ff00\",khaki:\"#f0e68c\",mediumseagreen:\"#3cb371\",limegreen:\"#32cd32\",mediumspringgreen:\"#00fa9a\",lightskyblue:\"#87cefa\",lightblue:\"#add8e6\",midnightblue:\"#191970\",lightpink:\"#ffb6c1\",mistyrose:\"#ffe4e1\",moccasin:\"#ffe4b5\",mintcream:\"#f5fffa\",lightslategray:\"#778899\",lightslategrey:\"#778899\",navajowhite:\"#ffdead\",navy:\"#000080\",mediumvioletred:\"#c71585\",powderblue:\"#b0e0e6\",palegoldenrod:\"#eee8aa\",oldlace:\"#fdf5e6\",paleturquoise:\"#afeeee\",mediumturquoise:\"#48d1cc\",mediumorchid:\"#ba55d3\",rebeccapurple:\"#663399\",lightsteelblue:\"#b0c4de\",mediumslateblue:\"#7b68ee\",thistle:\"#d8bfd8\",tan:\"#d2b48c\",orchid:\"#da70d6\",mediumpurple:\"#9370db\",purple:\"#800080\",pink:\"#ffc0cb\",skyblue:\"#87ceeb\",springgreen:\"#00ff7f\",palegreen:\"#98fb98\",red:\"#ff0000\",yellow:\"#ffff00\",slateblue:\"#6a5acd\",lavenderblush:\"#fff0f5\",peru:\"#cd853f\",palevioletred:\"#db7093\",violet:\"#ee82ee\",teal:\"#008080\",slategray:\"#708090\",slategrey:\"#708090\",aliceblue:\"#f0f8ff\",darkseagreen:\"#8fbc8f\",darkolivegreen:\"#556b2f\",greenyellow:\"#adff2f\",seagreen:\"#2e8b57\",seashell:\"#fff5ee\",tomato:\"#ff6347\",silver:\"#c0c0c0\",sienna:\"#a0522d\",lavender:\"#e6e6fa\",lightgreen:\"#90ee90\",orange:\"#ffa500\",orangered:\"#ff4500\",steelblue:\"#4682b4\",royalblue:\"#4169e1\",turquoise:\"#40e0d0\",yellowgreen:\"#9acd32\",salmon:\"#fa8072\",saddlebrown:\"#8b4513\",sandybrown:\"#f4a460\",rosybrown:\"#bc8f8f\",darksalmon:\"#e9967a\",lightgoldenrodyellow:\"#fafad2\",snow:\"#fffafa\",lightgrey:\"#d3d3d3\",lightgray:\"#d3d3d3\",dimgray:\"#696969\",dimgrey:\"#696969\",olivedrab:\"#6b8e23\",olive:\"#808000\"},r={};for(var d in a)r[a[d]]=d;var l={};e.prototype.toName=function(f){if(!(this.rgba.a||this.rgba.r||this.rgba.g||this.rgba.b))return\"transparent\";var d,i,n=r[this.toHex()];if(n)return n;if(null==f?void 0:f.closest){var o=this.toRgb(),t=1/0,b=\"black\";if(!l.length)for(var c in a)l[c]=new e(a[c]).toRgb();for(var g in a){var u=(d=o,i=l[g],Math.pow(d.r-i.r,2)+Math.pow(d.g-i.g,2)+Math.pow(d.b-i.b,2));u<t&&(t=u,b=g)}return b}};f.string.push([function(f){var r=f.toLowerCase(),d=\"transparent\"===r?\"#0000\":a[r];return d?new e(d).toRgb():null},\"name\"])}\n"], "mappings": ";;;AAAe,SAAR,cAAiB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAC,OAAM,WAAU,QAAO,WAAU,MAAK,WAAU,WAAU,WAAU,YAAW,WAAU,WAAU,WAAU,OAAM,WAAU,cAAa,WAAU,MAAK,WAAU,OAAM,WAAU,YAAW,WAAU,YAAW,WAAU,MAAK,WAAU,gBAAe,WAAU,OAAM,WAAU,MAAK,WAAU,WAAU,WAAU,WAAU,WAAU,UAAS,WAAU,gBAAe,WAAU,WAAU,WAAU,YAAW,WAAU,OAAM,WAAU,SAAQ,WAAU,MAAK,WAAU,UAAS,WAAU,UAAS,WAAU,eAAc,WAAU,WAAU,WAAU,UAAS,WAAU,WAAU,WAAU,UAAS,WAAU,WAAU,WAAU,aAAY,WAAU,SAAQ,WAAU,YAAW,WAAU,YAAW,WAAU,eAAc,WAAU,MAAK,WAAU,eAAc,WAAU,eAAc,WAAU,UAAS,WAAU,aAAY,WAAU,OAAM,WAAU,WAAU,WAAU,aAAY,WAAU,YAAW,WAAU,YAAW,WAAU,SAAQ,WAAU,OAAM,WAAU,YAAW,WAAU,MAAK,WAAU,UAAS,WAAU,SAAQ,WAAU,YAAW,WAAU,aAAY,WAAU,WAAU,WAAU,WAAU,WAAU,QAAO,WAAU,SAAQ,WAAU,OAAM,WAAU,QAAO,WAAU,YAAW,WAAU,YAAW,WAAU,eAAc,WAAU,WAAU,WAAU,OAAM,WAAU,aAAY,WAAU,aAAY,WAAU,eAAc,WAAU,OAAM,WAAU,kBAAiB,WAAU,cAAa,WAAU,MAAK,WAAU,OAAM,WAAU,gBAAe,WAAU,WAAU,WAAU,mBAAkB,WAAU,cAAa,WAAU,WAAU,WAAU,cAAa,WAAU,WAAU,WAAU,WAAU,WAAU,UAAS,WAAU,WAAU,WAAU,gBAAe,WAAU,gBAAe,WAAU,aAAY,WAAU,MAAK,WAAU,iBAAgB,WAAU,YAAW,WAAU,eAAc,WAAU,SAAQ,WAAU,eAAc,WAAU,iBAAgB,WAAU,cAAa,WAAU,eAAc,WAAU,gBAAe,WAAU,iBAAgB,WAAU,SAAQ,WAAU,KAAI,WAAU,QAAO,WAAU,cAAa,WAAU,QAAO,WAAU,MAAK,WAAU,SAAQ,WAAU,aAAY,WAAU,WAAU,WAAU,KAAI,WAAU,QAAO,WAAU,WAAU,WAAU,eAAc,WAAU,MAAK,WAAU,eAAc,WAAU,QAAO,WAAU,MAAK,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,cAAa,WAAU,gBAAe,WAAU,aAAY,WAAU,UAAS,WAAU,UAAS,WAAU,QAAO,WAAU,QAAO,WAAU,QAAO,WAAU,UAAS,WAAU,YAAW,WAAU,QAAO,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,aAAY,WAAU,QAAO,WAAU,aAAY,WAAU,YAAW,WAAU,WAAU,WAAU,YAAW,WAAU,sBAAqB,WAAU,MAAK,WAAU,WAAU,WAAU,WAAU,WAAU,SAAQ,WAAU,SAAQ,WAAU,WAAU,WAAU,OAAM,UAAS,GAAE,IAAE,CAAC;AAAE,WAAQ,KAAK;AAAE,MAAE,EAAE,CAAC,CAAC,IAAE;AAAE,MAAI,IAAE,CAAC;AAAE,IAAE,UAAU,SAAO,SAASA,IAAE;AAAC,QAAG,EAAE,KAAK,KAAK,KAAG,KAAK,KAAK,KAAG,KAAK,KAAK,KAAG,KAAK,KAAK;AAAG,aAAM;AAAc,QAAIC,IAAE,GAAE,IAAE,EAAE,KAAK,MAAM,CAAC;AAAE,QAAG;AAAE,aAAO;AAAE,QAAG,QAAMD,KAAE,SAAOA,GAAE,SAAQ;AAAC,UAAI,IAAE,KAAK,MAAM,GAAE,IAAE,IAAE,GAAE,IAAE;AAAQ,UAAG,CAAC,EAAE;AAAO,iBAAQ,KAAK;AAAE,YAAE,CAAC,IAAE,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM;AAAE,eAAQ,KAAK,GAAE;AAAC,YAAI,KAAGC,KAAE,GAAE,IAAE,EAAE,CAAC,GAAE,KAAK,IAAIA,GAAE,IAAE,EAAE,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,IAAE,EAAE,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,IAAE,EAAE,GAAE,CAAC;AAAG,YAAE,MAAI,IAAE,GAAE,IAAE;AAAA,MAAE;AAAC,aAAO;AAAA,IAAC;AAAA,EAAC;AAAE,IAAE,OAAO,KAAK,CAAC,SAASD,IAAE;AAAC,QAAIE,KAAEF,GAAE,YAAY,GAAEC,KAAE,kBAAgBC,KAAE,UAAQ,EAAEA,EAAC;AAAE,WAAOD,KAAE,IAAI,EAAEA,EAAC,EAAE,MAAM,IAAE;AAAA,EAAI,GAAE,MAAM,CAAC;AAAC;", "names": ["f", "d", "r"]}