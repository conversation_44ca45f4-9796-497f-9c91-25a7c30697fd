import { openBlock as _openBlock, createElementBlock as _createElementBlock, createStaticVNode as _createStaticVNode, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createStaticVNode('<g fill="none"><g clip-path="url(#clip0_118447_760)"><path d="M2 4.501a2.5 2.5 0 0 1 2.5-2.5h6a2.5 2.5 0 0 1 2.5 2.5v1.94c-.13.09-.254.191-.37.307l-.63.63V5H3V10.5a1.5 1.5 0 0 0 1.5 1.5h3.143c-.126.25-.225.515-.294.79l-.053.21H4.5a2.5 2.5 0 0 1-2.5-2.5v-6zM4.499 3a1.5 1.5 0 0 0-1.415 1h8.83a1.5 1.5 0 0 0-1.415-1h-6zm6.489 5.39L8.39 10.989a.5.5 0 0 1-.243-.841l1.646-1.646l-1.647-1.647a.5.5 0 1 1 .708-.707l2 2a.501.501 0 0 1 .134.244zM6.853 6.855a.5.5 0 1 0-.707-.707l-2 2a.5.5 0 0 0 0 .707l2 2a.5.5 0 0 0 .707-.707L5.207 8.501l1.646-1.647zm8.692.6a1.56 1.56 0 0 0-2.207 0l-4.289 4.29a2.777 2.777 0 0 0-.73 1.29l-.303 1.21a.61.61 0 0 0 .739.74l1.211-.303a2.778 2.778 0 0 0 1.29-.73l4.289-4.29a1.56 1.56 0 0 0 0-2.206z" fill="currentColor"></path></g><defs><clipPath id="clip0_118447_760"><path fill="#fff" d="M0 0h16v16H0z"></path></clipPath></defs></g>', 1)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowDevEdit16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
