#!/bin/bash

# ConnectAI 生产环境复刻部署脚本
# 使用方法: ./deploy-production-replica.sh [action]
# 支持的操作: start, stop, restart, status, logs, backup, restore

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
COMPOSE_FILE="$SCRIPT_DIR/production-replica-docker-compose.yml"
ENV_FILE="$SCRIPT_DIR/.env.production"
DATA_DIR="$SCRIPT_DIR/data"
BACKUP_DIR="$SCRIPT_DIR/backups"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查依赖
check_dependencies() {
    echo_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        echo_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    echo_info "依赖检查完成"
}

# 初始化环境
init_environment() {
    echo_info "初始化环境..."
    
    # 创建数据目录
    mkdir -p "$DATA_DIR"/{mysql/{manager,proxy,server,proxyall},redis/{manager,server,datachat},elasticsearch,rabbitmq,files}
    
    # 创建备份目录
    mkdir -p "$BACKUP_DIR"
    
    # 设置权限
    sudo chown -R 1000:1000 "$DATA_DIR/elasticsearch" 2>/dev/null || true
    chmod -R 755 "$DATA_DIR"
    
    # 检查环境变量文件
    if [ ! -f "$ENV_FILE" ]; then
        echo_warn "环境变量文件不存在，创建默认配置"
        cp "$SCRIPT_DIR/.env.production" "$ENV_FILE"
        echo_warn "请编辑 $ENV_FILE 配置您的API密钥和其他设置"
    fi
    
    echo_info "环境初始化完成"
}

# 启动服务
start_services() {
    echo_info "启动 ConnectAI 生产环境复刻..."
    
    check_dependencies
    init_environment
    
    # 拉取镜像
    echo_info "拉取 Docker 镜像..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" pull --ignore-pull-failures
    
    # 启动服务
    echo_info "启动服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
    
    # 等待服务启动
    echo_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    check_services_health
    
    echo_info "服务启动完成！"
    show_access_info
}

# 停止服务
stop_services() {
    echo_info "停止 ConnectAI 服务..."
    
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down
    
    echo_info "服务已停止"
}

# 重启服务
restart_services() {
    echo_info "重启 ConnectAI 服务..."
    
    stop_services
    sleep 5
    start_services
}

# 检查服务状态
check_services_health() {
    echo_info "检查服务状态..."
    
    # 检查容器状态
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
    
    # 检查关键服务健康状态
    echo_info "检查关键服务健康状态..."
    
    # 检查 MySQL
    for db in manager proxy server proxyall; do
        if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T mysql-$db mysqladmin ping -h localhost --silent; then
            echo_info "MySQL ($db) 健康"
        else
            echo_warn "MySQL ($db) 可能未就绪"
        fi
    done
    
    # 检查 Redis
    for redis in manager server datachat; do
        if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T redis-$redis redis-cli ping | grep -q PONG; then
            echo_info "Redis ($redis) 健康"
        else
            echo_warn "Redis ($redis) 可能未就绪"
        fi
    done
    
    # 检查 Elasticsearch
    if curl -s http://localhost:50094/_health | grep -q "green\|yellow"; then
        echo_info "Elasticsearch 健康"
    else
        echo_warn "Elasticsearch 可能未就绪"
    fi
}

# 显示服务状态
show_status() {
    echo_info "ConnectAI 服务状态："
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
}

# 显示日志
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        echo_info "显示所有服务日志..."
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f --tail=100
    else
        echo_info "显示 $service 服务日志..."
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f --tail=100 "$service"
    fi
}

# 备份数据
backup_data() {
    local backup_name="connectai-backup-$(date +%Y%m%d-%H%M%S)"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    echo_info "开始备份数据到 $backup_path..."
    
    mkdir -p "$backup_path"
    
    # 备份数据目录
    echo_info "备份数据文件..."
    tar -czf "$backup_path/data.tar.gz" -C "$SCRIPT_DIR" data/
    
    # 备份数据库
    echo_info "备份数据库..."
    for db in manager proxy server proxyall; do
        echo_info "备份 MySQL ($db)..."
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T mysql-$db \
            mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" --all-databases > "$backup_path/mysql-$db.sql"
    done
    
    # 备份配置文件
    echo_info "备份配置文件..."
    cp "$ENV_FILE" "$backup_path/"
    cp "$COMPOSE_FILE" "$backup_path/"
    
    echo_info "备份完成: $backup_path"
}

# 恢复数据
restore_data() {
    local backup_path=$1
    
    if [ -z "$backup_path" ] || [ ! -d "$backup_path" ]; then
        echo_error "请指定有效的备份路径"
        echo "用法: $0 restore /path/to/backup"
        exit 1
    fi
    
    echo_warn "警告：恢复操作将覆盖现有数据！"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo_info "操作已取消"
        exit 0
    fi
    
    echo_info "从 $backup_path 恢复数据..."
    
    # 停止服务
    stop_services
    
    # 恢复数据文件
    if [ -f "$backup_path/data.tar.gz" ]; then
        echo_info "恢复数据文件..."
        tar -xzf "$backup_path/data.tar.gz" -C "$SCRIPT_DIR"
    fi
    
    # 启动服务
    start_services
    
    # 恢复数据库
    for db in manager proxy server proxyall; do
        if [ -f "$backup_path/mysql-$db.sql" ]; then
            echo_info "恢复 MySQL ($db)..."
            docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T mysql-$db \
                mysql -u root -p"$MYSQL_ROOT_PASSWORD" < "$backup_path/mysql-$db.sql"
        fi
    done
    
    echo_info "数据恢复完成"
}

# 显示访问信息
show_access_info() {
    echo_info "服务访问信息："
    echo "=================================="
    echo "🌐 Web 服务："
    echo "   管理后台: http://localhost:50344"
    echo "   知识服务: http://localhost:8086"
    echo "   代理管理: http://localhost:10001"
    echo ""
    echo "🔧 管理服务："
    echo "   Nginx 代理: http://localhost:8081"
    echo "   RabbitMQ 管理: http://localhost:49490 (rabbitmq/rabbitmq)"
    echo "   Elasticsearch: http://localhost:50094"
    echo ""
    echo "🤖 AI 代理服务："
    echo "   OpenAI 代理: http://localhost:50314"
    echo "   Claude 代理: http://localhost:50320"
    echo "   星火代理: http://localhost:50295"
    echo "   文心代理: http://localhost:50302"
    echo "   智谱代理: http://localhost:50323"
    echo ""
    echo "💾 数据库服务："
    echo "   MySQL (Manager): localhost:53306"
    echo "   MySQL (Proxy): localhost:49979"
    echo "   MySQL (Server): localhost:49982"
    echo "   MySQL (ProxyAll): localhost:50294"
    echo "   Redis (Manager): localhost:49642"
    echo "   Redis (Server): localhost:49978"
    echo ""
    echo "📊 监控命令："
    echo "   查看状态: $0 status"
    echo "   查看日志: $0 logs [service_name]"
    echo "   备份数据: $0 backup"
    echo "=================================="
}

# 显示帮助信息
show_help() {
    echo "ConnectAI 生产环境复刻部署脚本"
    echo ""
    echo "用法: $0 [action] [options]"
    echo ""
    echo "支持的操作："
    echo "  start          启动所有服务"
    echo "  stop           停止所有服务"
    echo "  restart        重启所有服务"
    echo "  status         显示服务状态"
    echo "  logs [service] 显示日志 (可选指定服务名)"
    echo "  backup         备份数据"
    echo "  restore <path> 从备份恢复数据"
    echo "  help           显示此帮助信息"
    echo ""
    echo "示例："
    echo "  $0 start                    # 启动所有服务"
    echo "  $0 logs mysql-manager       # 查看管理数据库日志"
    echo "  $0 backup                   # 备份数据"
    echo "  $0 restore /path/to/backup  # 恢复数据"
}

# 主函数
main() {
    local action=${1:-help}
    
    case $action in
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$2"
            ;;
        backup)
            backup_data
            ;;
        restore)
            restore_data "$2"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
