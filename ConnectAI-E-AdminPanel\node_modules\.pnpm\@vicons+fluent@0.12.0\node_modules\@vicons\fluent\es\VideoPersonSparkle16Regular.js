import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.222 3h10.026A1.747 1.747 0 0 1 15 4.75v6.5A1.751 1.751 0 0 1 13.252 13H2.762a1.748 1.748 0 0 1-1.75-1.75V7.489a1.221 1.221 0 0 0 1 .511v3.248a.75.75 0 0 0 .75.75h2.247v-1a1 1 0 0 1 1-1h3.996a.999.999 0 0 1 .999 1v1h2.248a.749.749 0 0 0 .749-.75v-6.5a.75.75 0 0 0-.75-.75H4.499a1.237 1.237 0 0 0-.317-.169l-.044-.016l-.024-.006l-.58-.188a.273.273 0 0 1-.1-.061a.277.277 0 0 1-.062-.1L3.22 3zm2.782 9h3.997v-1H6.004v1zM1.278 5.732a1.245 1.245 0 0 0-.269-.2L1 5.544a1.163 1.163 0 0 0-.226-.106l-.611-.2a.24.24 0 0 1-.117-.089a.238.238 0 0 1 0-.279a.239.239 0 0 1 .117-.088l.611-.2a1.289 1.289 0 0 0 .787-.783l.005-.016l.2-.611a.243.243 0 0 1 .368-.117c.04.029.07.07.087.117l.2.611a1.3 1.3 0 0 0 .305.494c.139.137.308.24.493.3l.613.2h.011a.242.242 0 0 1 0 .456l-.61.2c-.187.06-.356.163-.495.3c-.137.14-.24.309-.3.494l-.2.612l-.006.015a.255.255 0 0 1-.082.102a.242.242 0 0 1-.28 0a.25.25 0 0 1-.092-.117l-.2-.612a1.277 1.277 0 0 0-.3-.495zM10 7a2 2 0 0 0-1.999-2a1.997 1.997 0 0 0-1.998 2a2 2 0 0 0 1.998 2a1.997 1.997 0 0 0 1.999-2zM7.003 7a1 1 0 0 1 1.706-.707a1 1 0 0 1-1.413 1.414A1 1 0 0 1 7.003 7zm-3.117.081a.123.123 0 0 1 .044-.059a.119.119 0 0 1 .025-.012a.117.117 0 0 1 .042-.01c.025 0 .05.008.07.022l.023.018c.01.012.019.026.024.041l.1.306a.64.64 0 0 0 .152.247a.64.64 0 0 0 .247.152l.305.1h.007l.021.01c.015.009.027.02.037.034c.014.02.022.045.022.07a.121.121 0 0 1-.08.114l-.306.1a.641.641 0 0 0-.4.4l-.099.306a.115.115 0 0 1-.044.058a.116.116 0 0 1-.07.022a.119.119 0 0 1-.114-.08l-.1-.306a.627.627 0 0 0-.4-.401l-.305-.1a.12.12 0 0 1 0-.227l.306-.1a.642.642 0 0 0 .333-.27a.606.606 0 0 0 .06-.129l.1-.306z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VideoPersonSparkle16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
