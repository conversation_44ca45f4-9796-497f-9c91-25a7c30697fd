import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2.564 12.257l5.001-9a.5.5 0 0 1 .82-.078l.054.078l5.002 9a.5.5 0 0 1-.352.736l-.085.007H3a.5.5 0 0 1-.473-.665l.036-.078l5.001-9l-5.001 9zm5.438-2.38a.75.75 0 1 0 0 1.5a.75.75 0 0 0 0-1.5zm0-3a.625.625 0 0 0-.619.533l-.006.092l-.001.998l.006.092a.625.625 0 0 0 1.237 0l.007-.092v-.997l-.006-.092a.625.625 0 0 0-.618-.533z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Warning16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
