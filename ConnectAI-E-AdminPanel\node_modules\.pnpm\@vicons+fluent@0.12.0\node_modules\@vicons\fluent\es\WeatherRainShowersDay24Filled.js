import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M13.002 6.01c3.169 0 4.966 2.097 5.227 4.63h.08a3.687 3.687 0 0 1 3.692 3.682a3.687 3.687 0 0 1-3.692 3.683h-.515l-1.645 2.627a.75.75 0 0 1-1.344-.659l.045-.091l1.212-1.877h-2.136l-1.645 2.627a.75.75 0 0 1-1.344-.659l.045-.091l1.212-1.877h-2.136l-1.645 2.627a.75.75 0 0 1-1.344-.659l.045-.091l1.212-1.877h-.631a3.687 3.687 0 0 1-3.692-3.682a3.687 3.687 0 0 1 3.692-3.683h.08c.263-2.55 2.059-4.63 5.227-4.63zm-8.634 4.155a.75.75 0 0 1-.315.935l-.091.045l-.927.384a.75.75 0 0 1-.665-1.34l.091-.046l.927-.384a.75.75 0 0 1 .98.406zm6.487-4.833l-.172.058C8.9 6.02 7.621 7.395 7.068 9.213l-.07.25l-.058.238l-.206.038a4.638 4.638 0 0 0-1.67.715a3.942 3.942 0 0 1 5.791-5.122zm-7.914.031l.105.035l.927.384a.75.75 0 0 1-.469 1.42L3.4 7.168l-.927-.384a.75.75 0 0 1 .469-1.42zm3.815-2.986l.045.091l.384.926a.75.75 0 0 1-1.34.666l-.046-.092l-.383-.926a.75.75 0 0 1 1.34-.665zm4.354-.319a.75.75 0 0 1 .44.875l-.035.105l-.383.926a.75.75 0 0 1-1.421-.468l.035-.106l.384-.926a.75.75 0 0 1 .98-.406z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherRainShowersDay24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
