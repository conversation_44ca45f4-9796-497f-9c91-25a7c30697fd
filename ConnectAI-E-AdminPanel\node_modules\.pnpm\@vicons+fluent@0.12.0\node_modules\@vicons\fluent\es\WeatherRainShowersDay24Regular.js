import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M13.002 6.01c3.169 0 4.966 2.097 5.227 4.63h.08a3.687 3.687 0 0 1 3.692 3.682a3.687 3.687 0 0 1-3.692 3.683l-.618-.002l-1.551 2.625a.75.75 0 0 1-1.344-.658l.045-.092l1.118-1.875h-2.136l-1.551 2.625a.75.75 0 0 1-1.344-.658l.045-.092l1.118-1.875H9.955l-1.551 2.625a.75.75 0 0 1-1.344-.658l.045-.092l1.117-1.875l-.527.002a3.687 3.687 0 0 1-3.692-3.682a3.687 3.687 0 0 1 3.692-3.683h.08c.263-2.55 2.059-4.63 5.227-4.63zm0 1.497c-2.071 0-3.877 1.634-3.877 3.89c0 .357-.319.638-.684.638h-.69c-1.261 0-2.284 1-2.284 2.236c0 1.235 1.023 2.236 2.284 2.236h10.502c1.262 0 2.284-1 2.284-2.236c0-1.235-1.022-2.236-2.284-2.236h-.69c-.365 0-.684-.28-.684-.638c0-2.285-1.806-3.89-3.877-3.89zm-8.634 2.658a.75.75 0 0 1-.315.935l-.091.045l-.927.384a.75.75 0 0 1-.665-1.34l.091-.046l.927-.384a.75.75 0 0 1 .98.406zm6.487-4.833l-.172.058a5.822 5.822 0 0 0-1.46.758A2.442 2.442 0 0 0 6.43 9.815a4.608 4.608 0 0 0-1.367.64a3.942 3.942 0 0 1 5.791-5.122zm-7.914.031l.105.035l.927.384a.75.75 0 0 1-.469 1.42L3.4 7.168l-.927-.384a.75.75 0 0 1 .469-1.42zm3.815-2.986l.045.091l.384.926a.75.75 0 0 1-1.34.666l-.046-.092l-.383-.926a.75.75 0 0 1 1.34-.665zm4.354-.319a.75.75 0 0 1 .44.875l-.035.105l-.383.926a.75.75 0 0 1-1.421-.468l.035-.106l.384-.926a.75.75 0 0 1 .98-.406z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherRainShowersDay24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
